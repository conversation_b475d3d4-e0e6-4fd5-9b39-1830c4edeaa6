using Bright.Serialization;
using System.Collections.Generic;
using System.Text.Json;

{{ 
    name = x.name
    key_type = x.key_ttype
    key_type1 =  x.key_ttype1
    key_type2 =  x.key_ttype2
    value_type =  x.value_ttype
}}

namespace {{x.namespace_with_top_module}}
{

{{~if x.comment != '' ~}}
/// <summary>
/// {{x.escape_comment}}
/// </summary>
{{~end~}}
public sealed partial class {{name}}
{
    {{~if x.is_map_table ~}}
    public readonly Dictionary<{{cs_define_type key_type}}, {{cs_define_type value_type}}> DataMap;
    public readonly List<{{cs_define_type value_type}}> DataList;
    
    public {{name}}(JsonElement _json)
    {
        DataMap = new Dictionary<{{cs_define_type key_type}}, {{cs_define_type value_type}}>();
        DataList = new List<{{cs_define_type value_type}}>();
        
        foreach(JsonElement _row in _json.EnumerateArray())
        {
            var _v = {{cs_define_type value_type}}.Deserialize{{value_type.bean.name}}(_row);
            DataList.Add(_v);
            DataMap.Add(_v.{{x.index_field.convention_name}}, _v);
        }
        PostInit();
    }

{{~if value_type.is_dynamic~}}
    public T GetOrDefaultAs<T>({{cs_define_type key_type}} key) where T : {{cs_define_type value_type}} => DataMap.TryGetValue(key, out var v) ? (T)v : null;
    public T GetAs<T>({{cs_define_type key_type}} key) where T : {{cs_define_type value_type}} => (T)DataMap[key];
{{~end~}}
    public {{cs_define_type value_type}} GetOrDefault({{cs_define_type key_type}} key) => DataMap.TryGetValue(key, out var v) ? v : null;

    public void Resolve(Dictionary<string, object> _tables)
    {
        foreach(var v in DataList)
        {
            v.Resolve(_tables);
        }
        PostResolve();
    }

    public void TranslateText(System.Func<string, string, string> translator)
    {
        foreach(var v in DataList)
        {
            v.TranslateText(translator);
        }
    }
    
        {{~else if x.is_list_table ~}}
    public readonly List<{{cs_define_type value_type}}> DataList;

    {{~if x.is_union_index~}}
    private {{cs_table_union_map_type_name x}} DataMapUnion;
    {{~else if !x.index_list.empty?~}}
    {{~for idx in x.index_list~}}
    private Dictionary<{{cs_define_type idx.type}}, {{cs_define_type value_type}}> DataMap_{{idx.index_field.name}};
    {{~end~}}
    {{~end~}}
    
    public {{name}}(JsonElement _json)
    {
        DataList = new List<{{cs_define_type value_type}}>();
        
        foreach(JsonElement _row in _json.EnumerateArray())
        {
            var _v = {{cs_define_type value_type}}.Deserialize{{value_type.bean.name}}(_row);
            DataList.Add(_v);
        }
    {{~if x.is_union_index~}}
        DataMapUnion = new {{cs_table_union_map_type_name x}}();
        foreach(var _v in DataList)
        {
            DataMapUnion.Add(({{cs_table_key_list x "_v"}}), _v);
        }
    {{~else if !x.index_list.empty?~}}
    {{~for idx in x.index_list~}}
        DataMap_{{idx.index_field.name}} = new Dictionary<{{cs_define_type idx.type}}, {{cs_define_type value_type}}>();
    {{~end~}}
    foreach(var _v in DataList)
    {
    {{~for idx in x.index_list~}}
        DataMap_{{idx.index_field.name}}.Add(_v.{{idx.index_field.convention_name}}, _v);
    {{~end~}}
    }
    {{~end~}}
        PostInit();
    }

    {{~if x.is_union_index~}}
    public {{cs_define_type value_type}} Get({{cs_table_get_param_def_list x}}) => DataMapUnion.TryGetValue(({{cs_table_get_param_name_list x}}), out {{cs_define_type value_type}} __v) ? __v : null;
    {{~else if !x.index_list.empty? ~}}
        {{~for idx in x.index_list~}}
    public {{cs_define_type value_type}} GetBy{{idx.index_field.convention_name}}({{cs_define_type idx.type}} key) => DataMap_{{idx.index_field.name}}.TryGetValue(key, out {{cs_define_type value_type}} __v) ? __v : null;
        {{~end~}}
    {{~end~}}

    public void Resolve(Dictionary<string, object> _tables)
    {
        foreach(var v in DataList)
        {
            v.Resolve(_tables);
        }
        PostResolve();
    }

    public void TranslateText(System.Func<string, string, string> translator)
    {
        foreach(var v in DataList)
        {
            v.TranslateText(translator);
        }
    }
    {{~else~}}

     private readonly {{cs_define_type value_type}} _data;

    public {{name}}(JsonElement _json)
    {
        int n = _json.GetArrayLength();
        if (n != 1) throw new SerializationException("table mode=one, but size != 1");
        _data = {{cs_define_type value_type}}.Deserialize{{value_type.bean.name}}(_json[0]);
        PostInit();
    }

    {{~ for field in value_type.bean.hierarchy_export_fields ~}}
{{~if field.comment != '' ~}}
    /// <summary>
    /// {{field.escape_comment}}
    /// </summary>
{{~end~}}
     public {{cs_define_type field.ctype}} {{field.convention_name}} => _data.{{field.convention_name}};
    {{~end~}}

    public void Resolve(Dictionary<string, object> _tables)
    {
        _data.Resolve(_tables);
        PostResolve();
    }

    public void TranslateText(System.Func<string, string, string> translator)
    {
        _data.TranslateText(translator);
    }

    {{~end~}}

    partial void PostInit();
    partial void PostResolve();
}

}