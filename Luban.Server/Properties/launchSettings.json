{"profiles": {"Luban.Server": {"commandName": "Project"}, "TestTemplate": {"commandName": "Project", "commandLineArgs": "-t D:\\workspace\\luban_examples\\Projects\\CustomTemplates"}, "timezone-utc": {"commandName": "Project", "commandLineArgs": "--timezone \"Etc/UTC\""}, "TestTemplate-DisableCache": {"commandName": "Project", "commandLineArgs": "-t D:\\workspace\\luban_examples\\Projects\\CustomTemplates --disable_cache"}, "TestEncryptMemory-DisableCache": {"commandName": "Project", "commandLineArgs": "-t D:\\workspace\\luban_examples\\Projects\\Csharp_CustomTemplate_EncryptMemory\\CustomTemplate --disable_cache"}, "CodeTemplate-AsyncLoad-DisableCache": {"commandName": "Project", "commandLineArgs": "-t D:\\workspace\\luban_examples\\Projects\\Csharp_CustomTemplate_ExternalCodeTemplate\\CustomTemplate --disable_cache"}, "ExternalCodeTemplate": {"commandName": "Project", "commandLineArgs": "-t D:\\workspace\\luban_examples\\Projects\\Csharp_CustomTemplate_ExternalCodeTemplate\\CustomTemplate --disable_cache"}, "convert_template": {"commandName": "Project", "commandLineArgs": "-t D:\\workspace\\luban_examples\\Projects\\ConvertTemplates\\CustomTemplates"}}}