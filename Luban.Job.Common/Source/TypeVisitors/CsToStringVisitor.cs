using Luban.Job.Common.Types;

namespace Luban.Job.Common.TypeVisitors
{
    public class CsToStringVisitor : DecoratorFuncVisitor<string, string>
    {
        public static CsToStringVisitor Ins { get; } = new CsToStringVisitor();

        public override string DoAccept(TType type, string fieldName)
        {
            // Luban好像在json里是直接使用数字作为enum的
            if (type is TEnum) { return $"(int){fieldName}"; }
            // 默认输出首字母大写json不认
            if (type is TBool) { return $"{fieldName}.ToString().ToLower()"; }
            // 默认输出不带双引号
            // $"\"{}\""
            if (type is TString) { return $"$\"\\\"{{{fieldName}}}\\\"\""; }
            return fieldName;
        }

        public override string Accept(TArray type, string fieldName)
        {
            return $"Bright.Common.StringUtil.CollectionToString({fieldName})";
        }

        public override string Accept(TList type, string fieldName)
        {
            return $"Bright.Common.StringUtil.CollectionToString({fieldName})";
        }

        public override string Accept(TSet type, string fieldName)
        {
            return $"Bright.Common.StringUtil.CollectionToString({fieldName})";
        }

        public override string Accept(TMap type, string fieldName)
        {
            return $"Bright.Common.StringUtil.CollectionToString({fieldName})";
        }
    }
}
