using Luban.Job.Common.Types;
using System;
using System.Collections.Generic;


namespace Luban.Job.Common.Source.Types
{
    public class TSocResCheck : TString
    {
        public override string TypeName => "socrescheck";

        public static new TSocResCheck Create(bool isNullable, Dictionary<string, string> tags)
        {
            return new TSocResCheck(isNullable, tags);
        }

        private TSocResCheck(bool isNullable, Dictionary<string, string> tags) : base(isNullable, tags)
        {
        }
    }
}
