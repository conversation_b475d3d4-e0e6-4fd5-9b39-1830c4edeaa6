using System.Collections.Generic;

namespace Luban.Job.Common.Types
{
    public class TSocText : TText
    {
        public new const string L10N_FIELD_SUFFIX = "_l10n_index";

        public override string TypeName => "soctext";

        public readonly int EngLength;

        public static new TSocText Create(bool isNullable, Dictionary<string, string> tags)
        {
            return new TSocText(isNullable, tags);
        }

        private TSocText(bool isNullable, Dictionary<string, string> tags) : base(isNullable, tags)
        {
            if (tags.ContainsKey("length"))
            {
                if (!int.TryParse(tags["length"], out EngLength))
                {
                    throw new System.Exception("填入length参数的数值非法！");
                }
            }
            else
            {
                EngLength = 0;
            }
        }
    }
}
