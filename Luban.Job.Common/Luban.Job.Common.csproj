<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <PackageProjectUrl>https://github.com/focus-creative-games/luban</PackageProjectUrl>
    <PackageReadmeFile>README.md</PackageReadmeFile>
    <EnforceCodeStyleInBuild>True</EnforceCodeStyleInBuild>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Source\TypeVisitors\Proto\**" />
    <EmbeddedResource Remove="Source\TypeVisitors\Proto\**" />
    <None Remove="Source\TypeVisitors\Proto\**" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\..\README.md">
      <Pack>True</Pack>
      <PackagePath>\</PackagePath>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CommandLineParser" Version="2.8.0" />
    <PackageReference Include="Google.Protobuf" Version="3.19.1" />
    <PackageReference Include="Scriban" Version="4.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Luban.Server.Common\Luban.Server.Common.csproj" />
  </ItemGroup>

</Project>
