using System.Globalization;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.Security.Cryptography;
using Luban.Job.Cfg.Cache;

namespace SocDataConverter
{
    /// <summary>
    /// 覆盖表验证器，用于验证DataPath下playid_*覆盖表目录生成的CS文件与原配置表是否一致
    /// 使用智能Excel差异对比技术，支持列映射、公式处理和细粒度变更检测
    /// </summary>
    public class OverrideTableValidator
    {
        private static readonly NLog.Logger s_logger = NLog.LogManager.GetCurrentClassLogger();
        private readonly string _a2Path;
        private readonly string _dataPath;
        private readonly string _tempWorkingPath;
        private readonly Dictionary<string, string> _originalCsFilesMd5;
        private readonly Dictionary<string, List<ExcelDifference>> _tableDifferences;
        private readonly List<ValidationError> _validationErrors; // 收集所有验证错误
        private readonly Dictionary<string, string> _tableNameToOutputFileCache; // 缓存 表名@文件名 -> 输出文件名 的映射
        private bool _enableDebugLog = false; // 控制是否输出DEBUG级别日志

        /// <summary>
        /// 日志级别枚举
        /// </summary>
        public enum LogLevel
        {
            DEBUG,
            INFO,
            WARNING,
            ERROR
        }

        public OverrideTableValidator(string a2Path, bool enableDebugLog = false)
        {
            _a2Path = a2Path;
            _dataPath = Path.Combine(_a2Path, "Datas");
            _tempWorkingPath = Path.Combine(Path.GetTempPath(), $"OverrideTableValidation");
            _originalCsFilesMd5 = new Dictionary<string, string>();
            _tableDifferences = new Dictionary<string, List<ExcelDifference>>();
            _validationErrors = new List<ValidationError>();
            _tableNameToOutputFileCache = new Dictionary<string, string>();
            _enableDebugLog = enableDebugLog;

            // 初始化时缓存__tables__.xlsx中的映射关系
            InitializeTableNameToOutputFileCache(Path.Combine(_dataPath, "__tables__.xlsx"));
            InitializeTableNameToOutputFileCache(Path.Combine(_dataPath, "common", "__tables__.xlsx"));
        }

        /// <summary>
        /// 初始化表名到输出文件名的缓存
        /// </summary>
        private void InitializeTableNameToOutputFileCache(string tablesFilePath)
        {
            try
            {
                if (!File.Exists(tablesFilePath))
                {
                    Log(LogLevel.DEBUG, $"__tables__.xlsx文件不存在: {tablesFilePath}");
                    return;
                }

                using var fileStream = new FileStream(tablesFilePath, FileMode.Open, FileAccess.Read);
                using var workbook = new XSSFWorkbook(fileStream);
                var sheet = workbook.GetSheetAt(0); // 默认第一个工作表

                // 遍历所有行，建立映射关系
                for (int i = 3; i <= sheet.LastRowNum; i++) // 从第4行开始（数据行）
                {
                    var row = sheet.GetRow(i);
                    if (row == null) continue;

                    // 获取input列（第5列，索引为4）和output列（第12列，索引为11）
                    var inputCell = row.GetCell(4);
                    var outputCell = row.GetCell(11);

                    if (inputCell == null || outputCell == null) continue;

                    var inputValue = inputCell.ToString()?.Trim();
                    var outputValue = outputCell.ToString()?.Trim();

                    // 处理公式类型的输出列
                    if (outputCell.CellType == CellType.Formula)
                    {
                        var fullNameCol = row.GetCell(1);
                        outputValue = fullNameCol?.ToString()?.ToLower().Replace('.', '_');
                    }

                    // 检查是否匹配并添加到缓存
                    if (!string.IsNullOrEmpty(inputValue) && !string.IsNullOrEmpty(outputValue))
                    {
                        _tableNameToOutputFileCache[inputValue] = outputValue;
                        Log(LogLevel.DEBUG, $"缓存映射: {inputValue} -> {outputValue}");
                    }
                }
            }
            catch (Exception ex)
            {
                Log(LogLevel.WARNING, $"初始化表名映射缓存失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 统一的日志输出函数
        /// </summary>
        private void Log(LogLevel level, string message)
        {
            // 如果是DEBUG日志且未开启DEBUG模式，则不输出
            if (level == LogLevel.DEBUG && !_enableDebugLog)
                return;

            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var prefix = level switch
            {
                LogLevel.DEBUG => "[DEBUG]",
                LogLevel.INFO => "[INFO]",
                LogLevel.WARNING => "[WARNING]",
                LogLevel.ERROR => "[ERROR]",
                _ => "[LOG]"
            };

            var color = Console.ForegroundColor;
            Console.ForegroundColor = level switch
            {
                LogLevel.DEBUG => ConsoleColor.Gray,
                LogLevel.INFO => ConsoleColor.White,
                LogLevel.WARNING => ConsoleColor.Yellow,
                LogLevel.ERROR => ConsoleColor.Red,
                _ => ConsoleColor.White
            };

            Console.WriteLine($"{timestamp} {prefix} {message}");
            Console.ForegroundColor = color;
        }

        /// <summary>
        /// 执行覆盖表验证
        /// </summary>
        /// <param name="language">语言设置</param>
        /// <param name="exportType">导出类型</param>
        /// <param name="showDetailedDiff">是否显示详细差异报告</param>
        /// <returns>验证是否通过</returns>
        public async Task<bool> ValidateAsync(string language = "zh_CN",
            ExcelExporter.ExportType exportType = ExcelExporter.ExportType.server, bool showDetailedDiff = false)
        {
            try
            {
                Log(LogLevel.INFO, "开始覆盖表验证...");
                _validationErrors.Clear();

                // 1. 记录原始CS文件的
                if (!await RecordOriginalCsFilesMd5(exportType))
                {
                    // 如果记录MD5失败，继续处理但记录错误
                    AddValidationError(ErrorType.SystemError, "MD5记录", "记录原始CS文件MD5失败");
                }

                // 2. 遍历所有覆盖表目录
                var overrideDirectories = GetOverrideDirectories();
                Log(LogLevel.INFO, $"发现 {overrideDirectories.Count} 个覆盖表目录");

                foreach (var overrideDir in overrideDirectories)
                {
                    Log(LogLevel.INFO, $"验证覆盖表目录: {overrideDir}");
                    await ValidateOverrideDirectory(overrideDir, language, exportType, showDetailedDiff);
                }

                // 生成总体差异汇总报告
                if (showDetailedDiff)
                {
                    GenerateOverallDifferenceReport();
                }

                // 输出所有收集的验证错误
                await ReportValidationErrors();

                bool validationPassed = _validationErrors.Count == 0;
                Log(validationPassed ? LogLevel.INFO : LogLevel.ERROR,
                    validationPassed ? "覆盖表验证完成，所有检查通过" : $"覆盖表验证完成，发现 {_validationErrors.Count} 个错误");

                return validationPassed;
            }
            catch (Exception ex)
            {
                Log(LogLevel.ERROR, $"覆盖表验证异常: {ex.Message}");
                AddValidationError(ErrorType.SystemError, "系统异常", ex.Message);
                return false;
            }
            finally
            {
                // 清理临时目录
                // CleanupTempDirectory();
            }
        }

        /// <summary>
        /// 记录原始CS文件的MD5值
        /// </summary>
        private async Task<bool> RecordOriginalCsFilesMd5(ExcelExporter.ExportType exportType)
        {
            try
            {
                var codeOutputPath = Path.Combine(Util.Instance.RootDirInfo.FullName, ExcelExporter.CodeOutputPath[exportType]);

                if (!Directory.Exists(codeOutputPath))
                {
                    Log(LogLevel.ERROR, $"原始代码输出目录不存在: {codeOutputPath}");
                    AddValidationError(ErrorType.DirectoryNotFound, "原始代码目录", $"原始代码输出目录不存在: {codeOutputPath}");
                    return false;
                }

                var csFiles = Directory.GetFiles(codeOutputPath, "*.cs", SearchOption.AllDirectories);
                Log(LogLevel.INFO, $"记录 {csFiles.Length} 个CS文件的MD5值");

                foreach (var csFile in csFiles)
                {
                    var relativePath = Path.GetRelativePath(codeOutputPath, csFile);
                    var md5 = Util.CalcMD5(File.ReadAllBytes(csFile));
                    _originalCsFilesMd5[relativePath] = md5;
                }

                return true;
            }
            catch (Exception ex)
            {
                Log(LogLevel.ERROR, $"记录原始CS文件MD5失败: {ex.Message}");
                AddValidationError(ErrorType.SystemError, "MD5记录", $"记录原始CS文件MD5失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取DataPath/Datas下所有playid_*覆盖表目录
        /// </summary>
        private List<string> GetOverrideDirectories()
        {
            if (!Directory.Exists(_dataPath))
            {
                Log(LogLevel.WARNING, $"Datas目录不存在: {_dataPath}");
                return new List<string>();
            }

            // 查找Datas目录下所有以playid_开头的目录
            var overrideDirectories = Directory.GetDirectories(_dataPath, "playid_*", SearchOption.TopDirectoryOnly).ToList();
            Log(LogLevel.INFO, $"在 {_dataPath} 下发现 {overrideDirectories.Count} 个playid_*覆盖表目录");

            foreach (var dir in overrideDirectories)
            {
                Log(LogLevel.INFO, $"  - {Path.GetFileName(dir)}");
            }

            return overrideDirectories;
        }

        /// <summary>
        /// 验证单个覆盖表目录
        /// </summary>
        private async Task ValidateOverrideDirectory(string overrideDir, string language, ExcelExporter.ExportType exportType,
            bool showDetailedDiff = false)
        {
            var overrideDirName = Path.GetFileName(overrideDir);
            var tempDataPath = Path.Combine(_tempWorkingPath, overrideDirName);

            if (Directory.Exists(tempDataPath))
            {
                Directory.Delete(tempDataPath, true);
            }

            Directory.CreateDirectory(tempDataPath);

            // 直接拷贝整个Datas目录到临时目录，确保所有依赖文件和文件夹结构正确
            var originDataPath = _dataPath;
            var tempDatasPath = Path.Combine(tempDataPath, "Datas");
            if (Directory.Exists(originDataPath))
            {
                CopyDirectory(originDataPath, tempDatasPath);
                Log(LogLevel.DEBUG, $"  完整复制Datas目录: {originDataPath} -> {tempDatasPath}");
            }
            else
            {
                Directory.CreateDirectory(tempDatasPath);
                Log(LogLevel.WARNING, $"  未找到原始Datas目录: {originDataPath}");
            }

            // 应用覆盖表差量
            await ApplyOverrideChanges(overrideDir, tempDatasPath);

            // 拷贝Defines目录
            var originalDefinesPath = Path.Combine(_a2Path, "Defines");
            var tempDefinesPath = Path.Combine(tempDataPath, "Defines");
            if (Directory.Exists(originalDefinesPath))
            {
                CopyDirectory(originalDefinesPath, tempDefinesPath);
            }
            else
            {
                Log(LogLevel.WARNING, $"  未找到Defines目录: {originalDefinesPath}");
            }

            // 生成CS文件并验证
            await GenerateAndValidateCsFiles(tempDataPath, language, exportType, overrideDirName, showDetailedDiff);
        }

        /// <summary>
        /// 应用覆盖表差量变更（从playid_*目录直接读取）
        /// </summary>
        private async Task ApplyOverrideChanges(string overrideDir, string targetDatasPath)
        {
            Log(LogLevel.DEBUG, $" ApplyOverrideChanges 开始处理: {overrideDir}");

            // playid_*目录本身就包含覆盖表文件，不需要再找Datas子目录
            if (!Directory.Exists(overrideDir))
            {
                Log(LogLevel.WARNING, $"覆盖表目录不存在，跳过: {overrideDir}");
                return;
            }

            Log(LogLevel.DEBUG, $" 搜索覆盖表文件: {overrideDir}");
            var overrideExcelFiles = Directory.GetFiles(overrideDir, "*.xlsx", SearchOption.AllDirectories)
                .Where(f => !Path.GetFileName(f).StartsWith("~$"))
                .ToList();

            Log(LogLevel.INFO, $"从 {Path.GetFileName(overrideDir)} 应用 {overrideExcelFiles.Count} 个覆盖表文件");

            // 原始Datas目录路径（用于搜索原始文件）
            var originalDataPath = _dataPath;
            Log(LogLevel.DEBUG, $" 原始Datas路径: {originalDataPath}");

            for (int i = 0; i < overrideExcelFiles.Count; i++)
            {
                var overrideFile = overrideExcelFiles[i];
                Log(LogLevel.DEBUG, $" 处理覆盖表文件 {i + 1}/{overrideExcelFiles.Count}: {overrideFile}");

                // 计算相对于覆盖表目录的路径
                var relativePath = Path.GetRelativePath(overrideDir, overrideFile);
                var fileName = Path.GetFileName(overrideFile);
                Log(LogLevel.DEBUG, $" 文件名: {fileName}, 相对路径: {relativePath}");

                // 首先尝试直接路径匹配
                var targetFile = Path.Combine(targetDatasPath, relativePath);
                if (!File.Exists(targetFile))
                {
                    targetFile = Path.Combine(targetDatasPath, "common", "大厅战斗共用", relativePath);
                }

                if (!string.IsNullOrEmpty(targetFile) && File.Exists(targetFile))
                {
                    Log(LogLevel.DEBUG, $" 开始合并Excel文件: {overrideFile} -> {targetFile}");
                    var playId = Path.GetFileName(overrideDir); // 获取playid_xxx目录名
                    await MergeExcelFile(overrideFile, targetFile, playId);
                    var actualRelativePath = Path.GetRelativePath(targetDatasPath, targetFile);
                    Log(LogLevel.INFO, $"  应用覆盖表: {fileName} -> {actualRelativePath}");
                    Log(LogLevel.DEBUG, $" 完成合并Excel文件: {fileName}");
                }
                else
                {
                    Log(LogLevel.WARNING, $"  未找到对应的原始文件: {fileName}");
                    Log(LogLevel.WARNING, $"    原始搜索路径: {originalDataPath}");
                    Log(LogLevel.WARNING, $"    目标路径: {targetDatasPath}");
                }
            }

            Log(LogLevel.DEBUG, $" ApplyOverrideChanges 完成处理: {overrideDir}");
        }

        /// <summary>
        /// 合并Excel文件（差异检测和列映射）
        /// </summary>
        private async Task MergeExcelFile(string overrideFile, string targetFile, string playId = null)
        {
            Log(LogLevel.DEBUG, $" MergeExcelFile 开始: {Path.GetFileName(overrideFile)}");

            XSSFWorkbook targetWorkbook = null;
            XSSFWorkbook overrideWorkbook = null;
            FileStream targetStream = null;

            try
            {
                Log(LogLevel.DEBUG, $" 打开文件流...");
                // 分别处理流的创建和工作簿的加载
                targetStream = new FileStream(targetFile, FileMode.Open, FileAccess.ReadWrite);
                using var overrideStream = new FileStream(overrideFile, FileMode.Open, FileAccess.Read);

                Log(LogLevel.DEBUG, $" 加载工作簿...");
                targetWorkbook = new XSSFWorkbook(targetStream);
                overrideWorkbook = new XSSFWorkbook(overrideStream);

                var tempFileName = Path.GetFileName(overrideFile);
                _tableDifferences[tempFileName] = new List<ExcelDifference>();
                Log(LogLevel.DEBUG, $" 初始化差异记录: {tempFileName}");

                Log(LogLevel.DEBUG, $" 开始处理工作表，覆盖表有 {overrideWorkbook.NumberOfSheets} 个工作表");
                // 遍历覆盖表的所有工作表
                for (int i = 0; i < overrideWorkbook.NumberOfSheets; i++)
                {
                    var overrideSheet = overrideWorkbook.GetSheetAt(i);
                    var sheetName = overrideSheet.SheetName;
                    Log(LogLevel.DEBUG, $" 处理工作表 {i + 1}/{overrideWorkbook.NumberOfSheets}: {sheetName}");

                    var targetSheet = targetWorkbook.GetSheet(sheetName);
                    if (targetSheet == null)
                    {
                        Log(LogLevel.ERROR, $" {playId} 存在废表: {sheetName}@{targetFile}");
                        continue;
                    }

                    Log(LogLevel.DEBUG, $" 开始应用工作表差异检测: {sheetName}");
                    ApplySheetChangesWithDiffDetection(overrideSheet, targetSheet, tempFileName, sheetName, playId);
                    Log(LogLevel.DEBUG, $" 完成工作表差异检测: {sheetName}");
                }

                Log(LogLevel.DEBUG, $" 关闭覆盖表工作簿...");
                // 关闭覆盖表工作簿（只读）
                overrideWorkbook.Close();
                overrideWorkbook = null;

                Log(LogLevel.DEBUG, $" 保存目标文件...");
                // 重新创建目标文件流来保存修改
                targetStream.Close();
                targetStream = new FileStream(targetFile, FileMode.Create, FileAccess.Write);

                // 保存修改后的文件
                targetWorkbook.Write(targetStream);
                Log(LogLevel.DEBUG, $" 文件保存完成");

                // 输出差异报告
                GenerateDifferenceReport(tempFileName, false);
            }
            catch (Exception ex)
            {
                Log(LogLevel.ERROR, $" MergeExcelFile 异常: {ex.Message}");
                Log(LogLevel.ERROR, $" 异常堆栈: {ex.StackTrace}");
                AddValidationError(ErrorType.ExcelProcessingError, Path.GetFileName(overrideFile),
                    $"Excel文件处理异常: {ex.Message}");
            }
            finally
            {
                Log(LogLevel.DEBUG, $" 清理资源...");
                // 确保资源正确释放
                targetWorkbook?.Close();
                overrideWorkbook?.Close();
                targetStream?.Close();
                Log(LogLevel.DEBUG, $" MergeExcelFile 完成: {Path.GetFileName(overrideFile)}");
            }
        }

        /// <summary>
        /// 应用工作表变更（支持列顺序不一致，但新增列时抛出异常）
        /// </summary>
        private void ApplySheetChangesWithDiffDetection(ISheet overrideSheet, ISheet targetSheet, string fileName,
            string sheetName, string playId = null)
        {
            Log(LogLevel.DEBUG, $" ApplySheetChangesWithDiffDetection 开始处理工作表: {sheetName}");

            var headerInfo = GetSheetHeaderInfoFromTablesDef(fileName, sheetName, overrideSheet);
            // 建立列映射关系
            var columnMapping = BuildColumnMapping(overrideSheet, targetSheet, headerInfo);

            // 逐行对比和应用变更
            if (headerInfo.IsOneValueTable)
            {
                CheckForOneValueTableNewRows(overrideSheet, targetSheet, headerInfo, fileName, sheetName);
                ApplyOneValueTableRowChanges(overrideSheet, targetSheet, columnMapping, headerInfo, fileName, sheetName);
            }
            else
            {
                CheckForNewColumns(overrideSheet, columnMapping, headerInfo, fileName, sheetName, playId);
                ApplyRowChanges(overrideSheet, targetSheet, columnMapping, headerInfo, fileName, sheetName);
            }

            Log(LogLevel.DEBUG, $" ApplySheetChangesWithDiffDetection 完成处理工作表: {sheetName}");
        }

        private void CheckForOneValueTableNewRows(ISheet overrideSheet, ISheet targetSheet, SheetHeaderInfo headerInfo, string fileName, string sheetName)
        {
            Dictionary<string, IRow> targetRowMap = new();
            for (int i = headerInfo.HeaderLength; i <= targetSheet.LastRowNum; i++)
            {
                IRow targetRow = targetSheet.GetRow(i);
                ICell keyCell = targetRow.GetCell(headerInfo.MainKeyLocations[0]);
                targetRowMap[keyCell.StringCellValue] = targetRow;
            }

            for (int i = headerInfo.HeaderLength; i <= overrideSheet.LastRowNum; i++)
            {
                IRow overrideRow = overrideSheet.GetRow(i);
                if (!targetRowMap.TryGetValue(overrideRow.GetCell(headerInfo.MainKeyLocations[0]).StringCellValue,
                        out IRow targetRow))
                {
                    AddValidationError(ErrorType.NewRowError, fileName,
                        $"覆盖表 {fileName} 的工作表 {sheetName} 中存在新增行: {overrideRow.GetCell(headerInfo.MainKeyLocations[0]).StringCellValue}");
                }
            }
        }

        private void ApplyOneValueTableRowChanges(ISheet overrideSheet, ISheet targetSheet, ColumnMapping columnMapping,
            SheetHeaderInfo headerInfo, string fileName, string sheetName)
        {
            
        }

        /// <summary>
        /// 复制单元格数据（公式单元格会计算结果后赋值）
        /// </summary>
        public void CopyCell(ICell sourceCell, ICell targetCell)
        {
            switch (sourceCell.CellType)
            {
                case CellType.String:
                    targetCell.SetCellValue(sourceCell.StringCellValue);
                    break;
                case CellType.Numeric:
                    targetCell.SetCellValue(sourceCell.NumericCellValue);
                    break;
                case CellType.Boolean:
                    targetCell.SetCellValue(sourceCell.BooleanCellValue);
                    break;
                case CellType.Formula:
                    // 对于公式单元格，计算公式结果后赋值
                    try
                    {
                        var evaluator = sourceCell.Sheet.Workbook.GetCreationHelper().CreateFormulaEvaluator();
                        var cellValue = evaluator.Evaluate(sourceCell);

                        switch (cellValue.CellType)
                        {
                            case CellType.String:
                                targetCell.SetCellValue(cellValue.StringValue);
                                break;
                            case CellType.Numeric:
                                targetCell.SetCellValue(cellValue.NumberValue);
                                break;
                            case CellType.Boolean:
                                targetCell.SetCellValue(cellValue.BooleanValue);
                                break;
                            default:
                                // 如果无法计算，保留原公式
                                targetCell.SetCellFormula(sourceCell.CellFormula);
                                break;
                        }
                    }
                    catch (Exception ex)
                    {
                        Log(LogLevel.ERROR, $" 公式计算失败，保留原公式: {sourceCell.CellFormula}");
                        AddValidationError(ErrorType.ExcelProcessingError, "公式计算错误",
                            $"公式计算失败: {sourceCell.Sheet.SheetName} {sourceCell.CellFormula}, 错误: {ex.Message}");
                        targetCell.SetCellFormula(sourceCell.CellFormula);
                    }

                    break;
                default:
                    targetCell.SetCellValue(sourceCell.ToString());
                    break;
            }
        }

        /// <summary>
        /// 生成CS文件并验证MD5
        /// </summary>
        private async Task GenerateAndValidateCsFiles(string tempDataPath, string language, ExcelExporter.ExportType exportType,
            string overrideDirName, bool showDetailedDiff = false)
        {
            Log(LogLevel.INFO, $"为覆盖表 {overrideDirName} 生成CS文件...");

            // 使用ExcelExporter生成CS文件
            var result = await ExcelExporter.Run(tempDataPath, language, exportType);

            if (!result.Item1)
            {
                // 提取真正的错误信息，过滤掉load日志
                var errorMessage = ExtractRealErrorMessage(result.Item2, result.Item3);
                Log(LogLevel.ERROR, $"覆盖表 {overrideDirName} CS文件生成失败:\n{errorMessage}");
                AddValidationError(ErrorType.CodeGenerationError, overrideDirName, errorMessage);
            }

            // 验证生成的CS文件MD5
            await ValidateGeneratedCsFiles(exportType, overrideDirName);

            // 如果启用详细差异报告，输出当前覆盖表的差异
            if (showDetailedDiff)
            {
                GenerateDetailedDifferenceReportForDirectory(overrideDirName);
            }
        }

        /// <summary>
        /// 验证生成的CS文件MD5值
        /// </summary>
        private async Task ValidateGeneratedCsFiles(ExcelExporter.ExportType exportType, string overrideDirName)
        {
            var codeOutputPath = Path.Combine(Util.Instance.RootDirInfo.FullName, ExcelExporter.CodeOutputPath[exportType]);
            var csFiles = Directory.GetFiles(codeOutputPath, "*.cs", SearchOption.AllDirectories);

            var differentFiles = new List<string>();

            foreach (var csFile in csFiles)
            {
                var relativePath = Path.GetRelativePath(codeOutputPath, csFile);
                var currentMd5 = await CalculateFileMd5(csFile);

                if (_originalCsFilesMd5.TryGetValue(relativePath, out var originalMd5))
                {
                    if (currentMd5 != originalMd5)
                    {
                        differentFiles.Add(relativePath);
                    }
                }
                else
                {
                    // 新生成的文件
                    differentFiles.Add($"{relativePath} (新文件)");
                }
            }

            if (differentFiles.Count > 0)
            {
                var errorMessage = $"覆盖表 {overrideDirName} 生成的CS文件与原配置不一致:\n" +
                                   string.Join("\n", differentFiles.Select(f => $"  - {f}"));
                Log(LogLevel.ERROR, errorMessage);
                AddValidationError(ErrorType.MD5ValidationError, overrideDirName, errorMessage);
            }

            Log(LogLevel.INFO, $"覆盖表 {overrideDirName} 验证通过");
        }

        /// <summary>
        /// 计算文件MD5值
        /// </summary>
        private async Task<string> CalculateFileMd5(string filePath)
        {
            using var md5 = MD5.Create();
            using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            var hashBytes = await Task.Run(() => md5.ComputeHash(stream));
            return Convert.ToHexString(hashBytes);
        }

        /// <summary>
        /// 复制目录
        /// </summary>
        private void CopyDirectory(string sourcePath, string targetPath)
        {
            Directory.CreateDirectory(targetPath);

            foreach (var file in Directory.GetFiles(sourcePath))
            {
                var fileName = Path.GetFileName(file);
                var targetFile = Path.Combine(targetPath, fileName);
                File.Copy(file, targetFile, true);
            }

            foreach (var directory in Directory.GetDirectories(sourcePath))
            {
                var dirName = Path.GetFileName(directory);
                var targetDir = Path.Combine(targetPath, dirName);
                CopyDirectory(directory, targetDir);
            }
        }


        /// <summary>
        /// 清理临时目录
        /// </summary>
        private void CleanupTempDirectory()
        {
            try
            {
                if (Directory.Exists(_tempWorkingPath))
                {
                    Directory.Delete(_tempWorkingPath, true);
                    Log(LogLevel.DEBUG, "清理临时目录完成");
                }
            }
            catch (Exception ex)
            {
                Log(LogLevel.WARNING, $"清理临时目录失败: {ex.Message}");
            }
        }

        #region 差异检测方法

        /// <summary>
        /// 添加差异记录
        /// </summary>
        private void AddDifference(string fileName, ExcelDifference difference)
        {
            _tableDifferences[fileName].Add(difference);
        }

        /// <summary>
        /// 获取单元格的比较值（不计算公式，使用字符串表示）
        /// </summary>
        private string GetCellCompareValue(ICell cell)
        {
            if (cell == null) return "";

            switch (cell.CellType)
            {
                case CellType.String:
                    return cell.StringCellValue ?? "";
                case CellType.Numeric:
                    return cell.NumericCellValue.ToString(CultureInfo.InvariantCulture);
                case CellType.Boolean:
                    return cell.BooleanCellValue.ToString();
                case CellType.Formula:
                    // 对于公式，返回公式字符串进行对比
                    return $"={cell.CellFormula}";
                case CellType.Blank:
                    return "";
                default:
                    return cell.ToString() ?? "";
            }
        }


        /// <summary>
        /// 检查工作表是否为空
        /// </summary>
        private bool IsEmptySheet(ISheet sheet)
        {
            return sheet.GetRow(0) == null || sheet.LastRowNum < 0;
        }

        /// <summary>
        /// 从__tables__.xlsx和Luban缓存获取表的主键信息
        /// </summary>
        private SheetHeaderInfo GetSheetHeaderInfoFromTablesDef(string fileName, string sheetName, ISheet overrideSheet)
        {
            var headerInfo = new SheetHeaderInfo();

            // 构造input字符串进行匹配
            var inputToMatch = $"{sheetName}@{fileName}";

            Log(LogLevel.DEBUG, $" 查找表定义: {inputToMatch}");

            if (_tableNameToOutputFileCache.TryGetValue(inputToMatch, out var outputName))
            {
                if (FileRecordCacheManager.Ins.TryGetTableByOutputFile(outputName, out var table))
                {
                    Log(LogLevel.DEBUG, $" 从Luban缓存找到表定义: {table.FullName}");

                    //查找第一个不以 ## 开头的行, 则为 HeaderLength
                    if (table.IsOneValueTable)
                    {
                        headerInfo.IsOneValueTable = true;
                        // 查找##var列的位置
                        foreach (var col in overrideSheet.GetRow(0))
                        {
                            if (col.StringCellValue.Contains("##var"))
                            {
                                headerInfo.MainKeyLocations.Add(col.ColumnIndex);
                                break;
                            }
                        }

                        headerInfo.HeaderLength = 1;
                    }
                    else
                    {
                        // 查找第一个不以 ## 开头的行
                        for (int i = 0; i <= overrideSheet.LastRowNum; i++)
                        {
                            var row = overrideSheet.GetRow(i);
                            var col = row?.GetCell(0);
                            if (col?.StringCellValue.StartsWith("##") ?? false)
                            {
                                headerInfo.HeaderLength = i;
                            }
                            else
                            {
                                break;
                            }
                        }

                        Dictionary<string, int> comment2ColIndex = new();
                        for (int rowIndex = 0; rowIndex < overrideSheet.LastRowNum; rowIndex++)
                        {
                            var cell = overrideSheet.GetRow(rowIndex)?.GetCell(0);
                            if (cell == null || !cell.StringCellValue.StartsWith("##comment"))continue;
                            for (int colIndex = 0; colIndex < overrideSheet.GetRow(rowIndex).LastCellNum; colIndex++)
                            {
                                var comment = overrideSheet.GetRow(rowIndex).GetCell(colIndex)?.StringCellValue;
                                if (comment != null)
                                {
                                    comment2ColIndex[comment] = colIndex;
                                }
                            }
                            break;
                        }

                        headerInfo.MainKeyLocations = table.IndexList.Select(i => comment2ColIndex[i.IndexField.Comment]).ToList();
                    }
                }
            }

            return headerInfo;
        }

        /// <summary>
        /// 建立列映射关系
        /// </summary>
        private ColumnMapping BuildColumnMapping(ISheet overrideSheet, ISheet targetSheet, SheetHeaderInfo headerInfo)
        {
            var mapping = new ColumnMapping();

            if (targetSheet == null || overrideSheet == null)
            {
                Log(LogLevel.ERROR, $" 无法建立列映射关系，工作表为空");
                return mapping;
            }

            // 建立基于主键的列映射
            var targetRow = targetSheet.GetRow(0);
            var overrideRow = overrideSheet.GetRow(0);

            if (targetRow == null || overrideRow == null) return mapping;

            var targetKeyToColumn = new Dictionary<string, int>();

            for (int j = 0; j < targetRow.LastCellNum; j++)
            {
                string targetMainKey = BuildMainKey(targetSheet, headerInfo.MainKeyLocations, j);
                if (!string.IsNullOrEmpty(targetMainKey) && !targetKeyToColumn.ContainsKey(targetMainKey))
                {
                    targetKeyToColumn[targetMainKey] = j;
                }
            }

            for (int i = 0; i < overrideRow.LastCellNum; i++)
            {
                string overrideMainKey = BuildMainKey(overrideSheet, headerInfo.MainKeyLocations, i);
                if (string.IsNullOrEmpty(overrideMainKey)) continue;

                if (targetKeyToColumn.TryGetValue(overrideMainKey, out int targetColumnIndex))
                {
                    // 避免重复映射
                    if (!mapping.OverrideToTarget.ContainsKey(i) && !mapping.TargetToOverride.ContainsKey(targetColumnIndex))
                    {
                        mapping.OverrideToTarget.Add(i, targetColumnIndex);
                        mapping.TargetToOverride.Add(targetColumnIndex, i);
                    }
                }
            }

            return mapping;
        }

        /// <summary>
        /// 构建主键字符串
        /// </summary>
        private string BuildMainKey(ISheet sheet, List<int> mainKeyLocations, int columnIndex)
        {
            string mainKey = "";
            foreach (int location in mainKeyLocations)
            {
                var cell = sheet.GetRow(location)?.GetCell(columnIndex);
                if (cell != null && !string.IsNullOrEmpty(cell.ToString()))
                {
                    mainKey += cell.ToString();
                }
            }

            return mainKey;
        }

        /// <summary>
        /// 检查是否有新增列，如果有则抛出异常
        /// </summary>
        private void CheckForNewColumns(ISheet overrideSheet, ColumnMapping columnMapping, SheetHeaderInfo headerInfo,
            string fileName, string sheetName, string playId = null)
        {
            var overrideRow = overrideSheet.GetRow(0);
            if (overrideRow == null) return;

            var unmappedColumns = new List<int>();
            for (int i = 1; i < overrideRow.LastCellNum; i++)
            {
                if (!columnMapping.OverrideToTarget.ContainsKey(i))
                {
                    var cell = overrideRow.GetCell(i);
                    if (cell != null && !string.IsNullOrEmpty(GetCellCompareValue(cell)))
                    {
                        // 获取列的主键值，检查是否以##开头
                        var mainKey = BuildMainKey(overrideSheet, headerInfo.MainKeyLocations, i);
                        // 忽略以##开头的列（如##var, ##type等）
                        if (!string.IsNullOrEmpty(mainKey) && !mainKey.StartsWith("##"))
                        {
                            unmappedColumns.Add(i);
                        }
                    }
                }
            }

            if (unmappedColumns.Count > 0)
            {
                var unmappedColumnNames = unmappedColumns.Select(i =>
                {
                    var mainKey = BuildMainKey(overrideSheet, headerInfo.MainKeyLocations, i);
                    return $"列{i}({mainKey})";
                });

                var playIdInfo = !string.IsNullOrEmpty(playId) ? $"[{playId}] " : "";

                var errorMsg = $"覆盖表 {fileName} 的工作表 {sheetName} 中存在新增列: " +
                               $"{string.Join(", ", unmappedColumnNames)}, " +
                               $"请确保覆盖表的列都存在于原表中。";
                Log(LogLevel.ERROR, errorMsg);
                AddValidationError(ErrorType.NewColumnError, $"{playIdInfo}{fileName}.{sheetName}", errorMsg);
            }
        }

        /// <summary>
        /// 应用行变更
        /// </summary>
        private void ApplyRowChanges(ISheet overrideSheet, ISheet targetSheet, ColumnMapping columnMapping,
            SheetHeaderInfo headerInfo, string fileName, string sheetName)
        {
            Log(LogLevel.DEBUG, $" ApplyRowChanges 开始 - 工作表: {sheetName}");
            Log(LogLevel.DEBUG, $" 覆盖表行数: {overrideSheet.LastRowNum + 1}, 目标表行数: {targetSheet.LastRowNum + 1}");
            Log(LogLevel.DEBUG, $" 主键位置: [{string.Join(",", headerInfo.MainKeyLocations)}]");

            // 对覆盖表的每一行进行处理
            for (int i = headerInfo.HeaderLength; i <= overrideSheet.LastRowNum; i++)
            {
                IRow overrideRow = overrideSheet.GetRow(i);
                if (overrideRow == null) continue;

                // 检查覆盖表行是否有效（所有主键列都不为空）
                if (!IsValidRowWithMainKeys(overrideRow, headerInfo.MainKeyLocations))
                    continue;

                bool foundMatchingRow = false;
                string overrideRowKey = GetRowKeyDebugString(overrideRow, headerInfo.MainKeyLocations);

                Log(LogLevel.DEBUG, $" 处理覆盖表行{i} - 主键: {overrideRowKey}");

                // 在目标表中查找匹配的行
                for (int j = headerInfo.HeaderLength; j <= targetSheet.LastRowNum; j++)
                {
                    IRow targetRow = targetSheet.GetRow(j);
                    if (targetRow == null) continue;

                    // 检查目标表行是否有效
                    if (!IsValidRowWithMainKeys(targetRow, headerInfo.MainKeyLocations))
                        continue;

                    // 检查所有主键列是否都匹配
                    if (IsRowMatching(overrideRow, targetRow, headerInfo.MainKeyLocations))
                    {
                        foundMatchingRow = true;
                        string targetRowKey = GetRowKeyDebugString(targetRow, headerInfo.MainKeyLocations);
                        Log(LogLevel.DEBUG, $" 找到匹配行 - 目标表行{j}, 主键: {targetRowKey}");
                        ApplyCellChanges(overrideRow, targetRow, columnMapping, fileName, sheetName, overrideRowKey);
                        break;
                    }
                }

                if (!foundMatchingRow)
                {
                    // 新增行
                    var newTargetRow = targetSheet.CreateRow(targetSheet.LastRowNum + 1);
                    ApplyCellChanges(overrideRow, newTargetRow, columnMapping, fileName, sheetName, overrideRowKey);

                    Log(LogLevel.DEBUG, $" 新增行 - 主键: {overrideRowKey}, 新行号: {targetSheet.LastRowNum}");

                    AddDifference(fileName, new ExcelDifference
                    {
                        SheetName = sheetName,
                        RowKey = overrideRowKey,
                        Type = DifferenceType.RowAdded,
                        Description = $"新增行: {overrideRowKey}"
                    });
                }
            }

            Log(LogLevel.DEBUG, $" ApplyRowChanges 完成 - 最终目标表行数: {targetSheet.LastRowNum + 1}");
        }

        /// <summary>
        /// 检查行是否有效（所有主键列都不为空）
        /// </summary>
        private bool IsValidRowWithMainKeys(IRow row, List<int> mainKeyLocations)
        {
            foreach (int keyLocation in mainKeyLocations)
            {
                var cell = row.GetCell(keyLocation);
                if (cell == null || string.IsNullOrEmpty(GetCellCompareValue(cell)))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 检查两行的所有主键列是否都匹配
        /// </summary>
        private bool IsRowMatching(IRow row1, IRow row2, List<int> mainKeyLocations)
        {
            foreach (int keyLocation in mainKeyLocations)
            {
                var cell1 = row1.GetCell(keyLocation);
                var cell2 = row2.GetCell(keyLocation);

                string value1 = GetCellCompareValue(cell1);
                string value2 = GetCellCompareValue(cell2);

                if (value1 != value2)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 获取行主键的调试字符串（用于日志输出）
        /// </summary>
        private string GetRowKeyDebugString(IRow row, List<int> mainKeyLocations)
        {
            var keyParts = new List<string>();
            foreach (int keyLocation in mainKeyLocations)
            {
                var cell = row.GetCell(keyLocation);
                keyParts.Add($"[{keyLocation}]={GetCellCompareValue(cell)}");
            }

            return string.Join("|", keyParts);
        }

        /// <summary>
        /// 应用单元格变更（使用列映射）
        /// </summary>
        private void ApplyCellChanges(IRow overrideRow, IRow targetRow, ColumnMapping columnMapping,
            string fileName, string sheetName, string rowKey)
        {
            Log(LogLevel.DEBUG, $" 应用单元格变更 - 行键: {rowKey}");

            for (int k = 0; k < overrideRow.LastCellNum; k++)
            {
                var overrideCell = overrideRow.GetCell(k) ?? overrideRow.CreateCell(k);
                string overrideValue = GetCellCompareValue(overrideCell);

                if (columnMapping.OverrideToTarget.ContainsKey(k))
                {
                    int targetColumnIndex = columnMapping.OverrideToTarget[k];
                    var targetCell = targetRow.GetCell(targetColumnIndex) ?? targetRow.CreateCell(targetColumnIndex);
                    string originalValue = GetCellCompareValue(targetCell);

                    if (overrideValue != originalValue)
                    {
                        Log(LogLevel.DEBUG,
                            $"   修改单元格[{rowKey}, 列{k}->列{targetColumnIndex}]: '{originalValue}' -> '{overrideValue}'");

                        CopyCell(overrideCell, targetCell);

                        AddDifference(fileName, new ExcelDifference
                        {
                            SheetName = sheetName,
                            RowKey = rowKey,
                            ColumnIndex = k,
                            Type = DifferenceType.CellModified,
                            OriginalValue = originalValue,
                            NewValue = overrideValue,
                            Description = $"单元格变更 [{rowKey}, 列{k}]: '{originalValue}' -> '{overrideValue}'"
                        });
                    }
                }
            }

            Log(LogLevel.DEBUG, $" 完成单元格变更 - 行键: {rowKey}");
        }

        /// <summary>
        /// 生成差异报告
        /// </summary>
        private void GenerateDifferenceReport(string fileName, bool detailed = false)
        {
            if (!_tableDifferences.ContainsKey(fileName) || _tableDifferences[fileName].Count == 0)
            {
                Log(LogLevel.INFO, $"  {fileName}: 无变更");
                return;
            }

            Log(LogLevel.INFO, $"  {fileName}: 发现 {_tableDifferences[fileName].Count} 处变更:");

            if (detailed)
            {
                var groupedDiffs = _tableDifferences[fileName].GroupBy(d => d.Type);
                foreach (var group in groupedDiffs)
                {
                    Log(LogLevel.INFO, $"    {GetDifferenceTypeDisplayName(group.Key)}: {group.Count()} 处");
                    foreach (var diff in group)
                    {
                        Log(LogLevel.INFO, $"      • {diff.Description}");
                    }
                }
            }
            else
            {
                foreach (var diff in _tableDifferences[fileName])
                {
                    Log(LogLevel.INFO, $"    - {diff.Description}");
                }
            }
        }

        /// <summary>
        /// 为单个覆盖表目录生成详细差异报告
        /// </summary>
        private void GenerateDetailedDifferenceReportForDirectory(string overrideDirName)
        {
            Log(LogLevel.INFO, $"\n{overrideDirName} 详细差异报告:");
            Log(LogLevel.INFO, "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

            var allDifferences = _tableDifferences.Values.SelectMany(x => x).ToList();
            if (allDifferences.Count == 0)
            {
                Log(LogLevel.INFO, "未发现任何差异，覆盖表与原表完全一致");
                return;
            }

            var groupedByType = allDifferences.GroupBy(d => d.Type);
            foreach (var typeGroup in groupedByType)
            {
                Log(LogLevel.INFO, $"\n{GetDifferenceTypeDisplayName(typeGroup.Key)} ({typeGroup.Count()} 处):");

                var groupedBySheet = typeGroup.GroupBy(d => d.SheetName);
                foreach (var sheetGroup in groupedBySheet)
                {
                    Log(LogLevel.INFO, $"  工作表: {sheetGroup.Key}");
                    foreach (var diff in sheetGroup)
                    {
                        Log(LogLevel.INFO, $"    • {diff.Description}");
                    }
                }
            }

            Log(LogLevel.INFO, "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        }

        /// <summary>
        /// 生成总体差异汇总报告
        /// </summary>
        private void GenerateOverallDifferenceReport()
        {
            Log(LogLevel.INFO, "\n覆盖表验证总体差异汇总报告:");
            Log(LogLevel.INFO, "═══════════════════════════════════════════════════════════════════");

            var allDifferences = _tableDifferences.Values.SelectMany(x => x).ToList();
            if (allDifferences.Count == 0)
            {
                Log(LogLevel.INFO, "所有覆盖表均与原表完全一致，未发现任何差异！");
                Log(LogLevel.INFO, "═══════════════════════════════════════════════════════════════════");
                return;
            }

            // 按类型统计
            Log(LogLevel.INFO, "差异类型统计:");
            var typeStats = allDifferences.GroupBy(d => d.Type)
                .Select(g => new { Type = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count);

            foreach (var stat in typeStats)
            {
                Log(LogLevel.INFO, $"  {GetDifferenceTypeDisplayName(stat.Type)}: {stat.Count} 处");
            }

            // 按文件统计
            Log(LogLevel.INFO, "\n文件差异统计:");
            var fileStats = _tableDifferences
                .Where(kvp => kvp.Value.Count > 0)
                .OrderByDescending(kvp => kvp.Value.Count);

            foreach (var fileStat in fileStats)
            {
                Log(LogLevel.INFO, $"  {fileStat.Key}: {fileStat.Value.Count} 处变更");
            }

            Log(LogLevel.INFO, $"\n总计: {allDifferences.Count} 处差异");
            Log(LogLevel.INFO, "═══════════════════════════════════════════════════════════════════");
        }

        /// <summary>
        /// 获取差异类型的显示名称
        /// </summary>
        private string GetDifferenceTypeDisplayName(DifferenceType type)
        {
            return type switch
            {
                DifferenceType.SheetAdded => "新增工作表",
                DifferenceType.RowAdded => "新增行",
                DifferenceType.CellModified => "单元格修改",
                _ => type.ToString()
            };
        }

        #endregion

        #region 数据结构定义

        /// <summary>
        /// 工作表头部信息
        /// </summary>
        private class SheetHeaderInfo
        {
            public int HeaderLength { get; set; }
            public List<int> MainKeyLocations { get; set; } = new List<int>();
            public bool IsOneValueTable { get; set; }
        }

        /// <summary>
        /// 列映射关系
        /// </summary>
        private class ColumnMapping
        {
            public Dictionary<int, int> OverrideToTarget { get; set; } = new Dictionary<int, int>();
            public Dictionary<int, int> TargetToOverride { get; set; } = new Dictionary<int, int>();
        }


        /// <summary>
        /// Excel差异类型
        /// </summary>
        public enum DifferenceType
        {
            SheetAdded,
            RowAdded,
            CellModified
        }

        /// <summary>
        /// Excel差异记录
        /// </summary>
        public class ExcelDifference
        {
            public string SheetName { get; set; }
            public string RowKey { get; set; }
            public int ColumnIndex { get; set; }
            public DifferenceType Type { get; set; }
            public string OriginalValue { get; set; }
            public string NewValue { get; set; }
            public string Description { get; set; }
        }

        /// <summary>
        /// 提取真正的错误信息，过滤掉load日志
        /// </summary>
        private string ExtractRealErrorMessage(string stdOutput, string stdError)
        {
            var errorLines = new List<string>();

            // 处理标准错误输出
            if (!string.IsNullOrEmpty(stdError))
            {
                var lines = stdError.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();
                    // 跳过load日志和其他无关信息
                    if (!trimmedLine.Contains("====== load ") &&
                        !trimmedLine.StartsWith("[info]") &&
                        !trimmedLine.StartsWith("[debug]") &&
                        !string.IsNullOrWhiteSpace(trimmedLine))
                    {
                        errorLines.Add(trimmedLine);
                    }
                }
            }

            // 处理标准输出中的错误信息
            if (!string.IsNullOrEmpty(stdOutput))
            {
                var lines = stdOutput.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();
                    // 寻找明显的错误信息
                    if (trimmedLine.Contains("[error]") ||
                        trimmedLine.Contains("Exception") ||
                        trimmedLine.Contains("Error") ||
                        trimmedLine.Contains("错误") ||
                        trimmedLine.Contains("失败") ||
                        (trimmedLine.Contains("at ") && trimmedLine.Contains(".cs:")) ||
                        (!trimmedLine.Contains("====== load ") &&
                         !trimmedLine.StartsWith("[info]") &&
                         !trimmedLine.StartsWith("[debug]") &&
                         trimmedLine.Length > 10 &&
                         !string.IsNullOrWhiteSpace(trimmedLine)))
                    {
                        errorLines.Add(trimmedLine);
                    }
                }
            }

            // 如果没有找到明确的错误信息，返回简化的消息
            if (errorLines.Count == 0)
            {
                return "CS文件生成失败，但未找到具体错误信息";
            }

            // 限制错误信息长度，避免过长
            var result = string.Join("\n", errorLines.Take(20));
            if (result.Length > 2000)
            {
                result = result.Substring(0, 2000) + "...(错误信息过长，已截断)";
            }

            return result;
        }

        /// <summary>
        /// 添加验证错误
        /// </summary>
        private void AddValidationError(ErrorType type, string source, string message)
        {
            _validationErrors.Add(new ValidationError
            {
                Type = type,
                Source = source,
                Message = message,
                Timestamp = DateTime.Now
            });
        }

        /// <summary>
        /// 报告所有验证错误
        /// </summary>
        private async Task ReportValidationErrors()
        {
            if (_validationErrors.Count == 0)
            {
                return;
            }

            Log(LogLevel.ERROR, "\n═══════════════════════════════════════════════════════════════════");
            Log(LogLevel.ERROR, $"验证错误汇总报告 - 共发现 {_validationErrors.Count} 个错误:");
            Log(LogLevel.ERROR, "═══════════════════════════════════════════════════════════════════");

            // 按错误类型分组
            var errorGroups = _validationErrors.GroupBy(e => e.Type).OrderBy(g => g.Key);

            foreach (var group in errorGroups)
            {
                Log(LogLevel.ERROR, $"\n{GetErrorTypeDisplayName(group.Key)} ({group.Count()} 个):");

                foreach (var error in group)
                {
                    Log(LogLevel.ERROR, $"  [{error.Source}] {error.Message}");
                }
            }

            Log(LogLevel.ERROR, "\n═══════════════════════════════════════════════════════════════════");

            // 可选：将错误写入文件
            await WriteErrorsToFile();
        }

        /// <summary>
        /// 将验证错误写入文件
        /// </summary>
        private async Task WriteErrorsToFile()
        {
            try
            {
                var errorFilePath = Path.Combine(Directory.GetCurrentDirectory(), "validation_errors.txt");

                var errorLines = new List<string>
                {
                    $"验证错误报告 - {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                    new string('=', 60),
                    $"总计错误数量: {_validationErrors.Count}",
                    ""
                };

                var errorGroups = _validationErrors.GroupBy(e => e.Type).OrderBy(g => g.Key);

                foreach (var group in errorGroups)
                {
                    errorLines.Add($"{GetErrorTypeDisplayName(group.Key)} ({group.Count()} 个):");

                    foreach (var error in group)
                    {
                        errorLines.Add($"  [{error.Source}] {error.Message}");
                    }

                    errorLines.Add("");
                }

                await File.WriteAllLinesAsync(errorFilePath, errorLines);
                Log(LogLevel.INFO, $"验证错误已保存到: {errorFilePath}");
            }
            catch (Exception ex)
            {
                Log(LogLevel.WARNING, $"无法保存错误报告到文件: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取错误类型的显示名称
        /// </summary>
        private string GetErrorTypeDisplayName(ErrorType type)
        {
            return type switch
            {
                ErrorType.DirectoryNotFound => "目录不存在错误",
                ErrorType.NewColumnError => "新增列错误",
                ErrorType.ExcelProcessingError => "Excel处理错误",
                ErrorType.CodeGenerationError => "代码生成错误",
                ErrorType.MD5ValidationError => "MD5验证错误",
                ErrorType.SystemError => "系统错误",
                _ => type.ToString()
            };
        }

        #endregion

        #region 验证错误数据结构

        /// <summary>
        /// 错误类型枚举
        /// </summary>
        public enum ErrorType
        {
            DirectoryNotFound,
            NewColumnError,
            ExcelProcessingError,
            CodeGenerationError,
            MD5ValidationError,
            SystemError,
            NewRowError
        }

        /// <summary>
        /// 验证错误记录
        /// </summary>
        public class ValidationError
        {
            public ErrorType Type { get; set; }
            public string Source { get; set; }
            public string Message { get; set; }
            public DateTime Timestamp { get; set; }
        }

        #endregion
    }
}