using System.Diagnostics;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;

namespace SocDataConverter
{
    public class Util
    {
        private static Util? _instance = null;
        public static Util Instance
        {
            get
            {
                _instance ??= new Util();
                return _instance;
            }
        }

        public DirectoryInfo RootDirInfo;

        public DirectoryInfo ExeDirInfo;

        private const byte CR = 0x0D; //\r
        private const byte LF = 0x0A; //\n

        private static readonly byte[] DOS_LINE_ENDING = new byte[] { CR, LF };

        public static readonly int WorldOnlyHashSalt = 0x0c;
        public static readonly int OwnClientHashSalt = 0x0a;
        public static readonly int OtherClientsHashSalt = 0x7a;
        public static readonly int AllClientsHashSalt = 0x3c;
        public static readonly int SimulatorHashSalt = 0xf0;

        private Util()
        {
            ExeDirInfo = new DirectoryInfo(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location));
            RootDirInfo = ExeDirInfo.Parent.Parent.Parent;

            Debug.Assert(ExeDirInfo != null && ExeDirInfo.Exists);
            Debug.Assert(RootDirInfo != null && RootDirInfo.Exists);
        }

        public static string CalcMD5(byte[] srcBytes)
        {
            using MD5 md5 = MD5.Create();
            var md5Bytes = md5.ComputeHash(srcBytes);
            var s = new StringBuilder(md5Bytes.Length * 2);
            foreach (var b in md5Bytes)
            {
                s.Append(b.ToString("X"));
            }
            return s.ToString();
        }

        public static void Unix2Dos(string fileName)
        {
            byte[] data = File.ReadAllBytes(fileName);
            using (FileStream fileStream = File.OpenWrite(fileName))
            {
                BinaryWriter bw = new BinaryWriter(fileStream);
                int position = 0;
                int index = 0;
                do
                {
                    index = Array.IndexOf(data, LF, position);
                    if (index >= 0)
                    {
                        if (index > 0 && data[index - 1] == CR)
                        {
                            bw.Write(data, position, index - position + 1);
                        }
                        else
                        {
                            bw.Write(data, position, index - position);
                            bw.Write(DOS_LINE_ENDING);
                        }

                        position = index + 1;
                    }
                } while (index >= 0);

                bw.Write(data, position, data.Length - position);
                fileStream.SetLength(fileStream.Position);
            }
        }

        public static void WriteTextToPathWithCRLF(string FilePath, string Text)
        {
            File.WriteAllText(FilePath, Text);
            Unix2Dos(FilePath);
        }
    }
}
