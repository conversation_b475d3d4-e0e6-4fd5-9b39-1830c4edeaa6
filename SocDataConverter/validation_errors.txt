验证错误报告 - 2025-08-26 15:03:30
============================================================
总计错误数量: 36

新增列错误 (19 个):
  [[playid_1] 08_界面表.xlsx.HUD表] 覆盖表 [playid_1] 08_界面表.xlsx 的工作表 HUD表 中存在新增列，这是不允许的。
新增的列：列2(标签)
请确保覆盖表的列都存在于原表中。
  [[playid_1] 13_单位_交互物.xlsx.交互物_宝箱表] 覆盖表 [playid_1] 13_单位_交互物.xlsx 的工作表 交互物_宝箱表 中存在新增列，这是不允许的。
新增的列：列9(交互物类型)
请确保覆盖表的列都存在于原表中。
  [[playid_1] 41_道具_总表.xlsx.道具_稀有度] 覆盖表 [playid_1] 41_道具_总表.xlsx 的工作表 道具_稀有度 中存在新增列，这是不允许的。
新增的列：列4(科技解锁消耗废料)
请确保覆盖表的列都存在于原表中。
  [[playid_1] 65_战斗_战斗模块表.xlsx.战斗_战斗模块表] 覆盖表 [playid_1] 65_战斗_战斗模块表.xlsx 的工作表 战斗_战斗模块表 中存在新增列，这是不允许的。
新增的列：列9(伤害结算关系)
请确保覆盖表的列都存在于原表中。
  [[playid_101] 05_通用提示表.xlsx.通用提示表] 覆盖表 [playid_101] 05_通用提示表.xlsx 的工作表 通用提示表 中存在新增列，这是不允许的。
新增的列：列3(提示样式), 列10(强提示背景), 列15(是否显示通用玩法提示倒计时)
请确保覆盖表的列都存在于原表中。
  [[playid_102] 05_通用提示表.xlsx.通用提示表] 覆盖表 [playid_102] 05_通用提示表.xlsx 的工作表 通用提示表 中存在新增列，这是不允许的。
新增的列：列3(提示样式), 列10(强提示背景), 列15(是否显示通用玩法提示倒计时)
请确保覆盖表的列都存在于原表中。
  [[playid_2] 08_界面表.xlsx.HUD表] 覆盖表 [playid_2] 08_界面表.xlsx 的工作表 HUD表 中存在新增列，这是不允许的。
新增的列：列2(标签)
请确保覆盖表的列都存在于原表中。
  [[playid_2] 65_战斗_战斗模块表.xlsx.战斗_战斗模块表] 覆盖表 [playid_2] 65_战斗_战斗模块表.xlsx 的工作表 战斗_战斗模块表 中存在新增列，这是不允许的。
新增的列：列9(伤害结算关系)
请确保覆盖表的列都存在于原表中。
  [[playid_4] 13_单位_交互物.xlsx.交互物_宝箱表] 覆盖表 [playid_4] 13_单位_交互物.xlsx 的工作表 交互物_宝箱表 中存在新增列，这是不允许的。
新增的列：列9(交互物类型)
请确保覆盖表的列都存在于原表中。
  [[playid_4] 90_玩法_任务表.xlsx.任务阶段表] 覆盖表 [playid_4] 90_玩法_任务表.xlsx 的工作表 任务阶段表 中存在新增列，这是不允许的。
新增的列：列7(开始执行事件), 列18(阶段结束执行事件), 列19(标点引导)
请确保覆盖表的列都存在于原表中。
  [[playid_5] 41_道具_总表.xlsx.道具_总表] 覆盖表 [playid_5] 41_道具_总表.xlsx 的工作表 道具_总表 中存在新增列，这是不允许的。
新增的列：列27(展示偏移)
请确保覆盖表的列都存在于原表中。
  [[playid_5] 42_道具_远程武器表.xlsx.数值_散布] 覆盖表 [playid_5] 42_道具_远程武器表.xlsx 的工作表 数值_散布 中存在新增列，这是不允许的。
新增的列：列24(ads空中偏移)
请确保覆盖表的列都存在于原表中。
  [[playid_5] 42_道具_远程武器表.xlsx.战斗_FOV曲线] 覆盖表 [playid_5] 42_道具_远程武器表.xlsx 的工作表 战斗_FOV曲线 中存在新增列，这是不允许的。
新增的列：列17(GunFov关镜z补偿开始)
请确保覆盖表的列都存在于原表中。
  [[playid_5] 42_道具_远程武器表.xlsx.战斗_枪械后座表现] 覆盖表 [playid_5] 42_道具_远程武器表.xlsx 的工作表 战斗_枪械后座表现 中存在新增列，这是不允许的。
新增的列：列6(前瞄位置), 列7(前瞄位置)
请确保覆盖表的列都存在于原表中。
  [[playid_5] 42_道具_远程武器表.xlsx.枪械sway表现] 覆盖表 [playid_5] 42_道具_远程武器表.xlsx 的工作表 枪械sway表现 中存在新增列，这是不允许的。
新增的列：列5(使用viewswayID), 列7(使用gunswayID)
请确保覆盖表的列都存在于原表中。
  [[playid_5] 42_道具_远程武器表.xlsx.枪械移动sway表现] 覆盖表 [playid_5] 42_道具_远程武器表.xlsx 的工作表 枪械移动sway表现 中存在新增列，这是不允许的。
新增的列：列5(使用viewswayID), 列7(使用gunswayID)
请确保覆盖表的列都存在于原表中。
  [[playid_5] 90_玩法_任务表.xlsx.任务阶段表] 覆盖表 [playid_5] 90_玩法_任务表.xlsx 的工作表 任务阶段表 中存在新增列，这是不允许的。
新增的列：列19(任务类型)
请确保覆盖表的列都存在于原表中。
  [[playid_6] 05_通用提示表.xlsx.通用提示表] 覆盖表 [playid_6] 05_通用提示表.xlsx 的工作表 通用提示表 中存在新增列，这是不允许的。
新增的列：列3(提示样式), 列10(强提示背景), 列15(是否显示通用玩法提示倒计时)
请确保覆盖表的列都存在于原表中。
  [[playid_99] 21_系统_建筑表.xlsx.建筑_结构造物表] 覆盖表 [playid_99] 21_系统_建筑表.xlsx 的工作表 建筑_结构造物表 中存在新增列，这是不允许的。
新增的列：列3(放置时自动创建摆件)
请确保覆盖表的列都存在于原表中。

代码生成错误 (8 个):
  [playid_1] at Luban.Job.Cfg.DataSources.Excel.SheetLoadUtil.LoadSheetTableDefInfo(String rawUrl, String sheetName, Stream stream) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\DataSources\Excel\SheetLoadUtil.cs:line 543
at Luban.Job.Cfg.DataSources.Excel.ExcelDataSource.LoadTableDefInfo(String rawUrl, String sheetName, Stream stream) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\DataSources\Excel\ExcelDataSource.cs:line 58
at Luban.Job.Cfg.Defs.CfgDefLoader.LoadTableValueTypeDefineFromFileAsync(Table table, String dataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\CfgDefLoader.cs:line 353
at Luban.Job.Cfg.Defs.CfgDefLoader.<>c__DisplayClass32_1.<<LoadTableValueTypeDefinesFromFileAsync>b__1>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\CfgDefLoader.cs:line 435
--- End of stack trace from previous location ---
at Luban.Job.Cfg.Defs.CfgDefLoader.LoadTableValueTypeDefinesFromFileAsync(String dataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\CfgDefLoader.cs:line 440
at Luban.Job.Cfg.Defs.CfgDefLoader.LoadDefinesFromFileAsync(String dataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\CfgDefLoader.cs:line 748
at Luban.Job.Cfg.JobController.GenAsync(RemoteAgent agent, GenJob rpc) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 170
JOB_EXCEPTION
=======================================================================
===> excel:全局变量表@02_全局配置表.xlsx sheet:全局变量表 读取失败.
===> 列:NearbyRebirthSwitch 重复
=======================================================================
  [playid_2] at Luban.Job.Cfg.Defs.TableDataInfo.BuildIndexs() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\TableDataInfo.cs:line 90
at Luban.Job.Cfg.Defs.TableDataInfo..ctor(DefTable table, List`1 mainRecords, List`1 patchRecords, Dictionary`2 playModuleRecords) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\TableDataInfo.cs:line 36
at Luban.Job.Cfg.Defs.DefAssembly.AddDataTable(DefTable table, List`1 mainRecords, List`1 patchRecords, Dictionary`2 playModuleRecords) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\DefAssembly.cs:line 119
at Luban.Job.Cfg.Utils.DataLoaderUtil.LoadTableAsync(IAgent agent, DefTable table, String dataDir, String patchName, String patchDataDir, String inputConvertDataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 253
at Luban.Job.Cfg.Utils.DataLoaderUtil.<>c__DisplayClass9_1.<<LoadCfgDataAsync>b__1>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 272
--- End of stack trace from previous location ---
at Luban.Job.Cfg.Utils.DataLoaderUtil.LoadCfgDataAsync(IAgent agent, DefAssembly ass, String dataDir, String patchName, String patchDataDir, String inputConvertDataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 280
at Luban.Job.Cfg.JobController.<>c__DisplayClass2_1.<<GenAsync>g__CheckLoadCfgDataAsync|3>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 197
--- End of stack trace from previous location ---
at Luban.Job.Cfg.JobController.GenAsync(RemoteAgent agent, GenJob rpc) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 244
JOB_EXCEPTION
=======================================================================
===> 配置表 'Ui.TbHud' 主文件 主键字段:'id' 主键值:'206' 重复.
记录1 来自文件:HUD表@08_界面表.xlsx
记录2 来自文件:HUD表@08_界面表.xlsx
================...(错误信息过长，已截断)
  [playid_3] at Luban.Job.Cfg.Defs.TableDataInfo.BuildIndexs() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\TableDataInfo.cs:line 90
at Luban.Job.Cfg.Defs.TableDataInfo..ctor(DefTable table, List`1 mainRecords, List`1 patchRecords, Dictionary`2 playModuleRecords) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\TableDataInfo.cs:line 36
at Luban.Job.Cfg.Defs.DefAssembly.AddDataTable(DefTable table, List`1 mainRecords, List`1 patchRecords, Dictionary`2 playModuleRecords) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\DefAssembly.cs:line 119
at Luban.Job.Cfg.Utils.DataLoaderUtil.LoadTableAsync(IAgent agent, DefTable table, String dataDir, String patchName, String patchDataDir, String inputConvertDataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 253
at Luban.Job.Cfg.Utils.DataLoaderUtil.<>c__DisplayClass9_1.<<LoadCfgDataAsync>b__1>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 272
--- End of stack trace from previous location ---
at Luban.Job.Cfg.Utils.DataLoaderUtil.LoadCfgDataAsync(IAgent agent, DefAssembly ass, String dataDir, String patchName, String patchDataDir, String inputConvertDataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 280
at Luban.Job.Cfg.JobController.<>c__DisplayClass2_1.<<GenAsync>g__CheckLoadCfgDataAsync|3>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 197
--- End of stack trace from previous location ---
at Luban.Job.Cfg.JobController.GenAsync(RemoteAgent agent, GenJob rpc) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 244
JOB_EXCEPTION
=======================================================================
===> 配置表 'itemReward.TbItemDrop' 主文件 主键字段:'id' 主键值:'10205' 重复.
记录1 来自文件:掉落_总表@01_掉落表.xlsx
记录2 来自文件:掉落_总表@01_掉落表.xlsx...(错误信息过长，已截断)
  [playid_5] at Luban.Job.Cfg.Defs.TableDataInfo.BuildIndexs() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\TableDataInfo.cs:line 90
at Luban.Job.Cfg.Defs.TableDataInfo..ctor(DefTable table, List`1 mainRecords, List`1 patchRecords, Dictionary`2 playModuleRecords) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\TableDataInfo.cs:line 36
at Luban.Job.Cfg.Defs.DefAssembly.AddDataTable(DefTable table, List`1 mainRecords, List`1 patchRecords, Dictionary`2 playModuleRecords) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\DefAssembly.cs:line 119
at Luban.Job.Cfg.Utils.DataLoaderUtil.LoadTableAsync(IAgent agent, DefTable table, String dataDir, String patchName, String patchDataDir, String inputConvertDataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 253
at Luban.Job.Cfg.Utils.DataLoaderUtil.<>c__DisplayClass9_1.<<LoadCfgDataAsync>b__1>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 272
--- End of stack trace from previous location ---
at Luban.Job.Cfg.Utils.DataLoaderUtil.LoadCfgDataAsync(IAgent agent, DefAssembly ass, String dataDir, String patchName, String patchDataDir, String inputConvertDataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 280
at Luban.Job.Cfg.JobController.<>c__DisplayClass2_1.<<GenAsync>g__CheckLoadCfgDataAsync|3>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 197
--- End of stack trace from previous location ---
at Luban.Job.Cfg.JobController.GenAsync(RemoteAgent agent, GenJob rpc) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 244
JOB_EXCEPTION
=======================================================================
===> 配置表 'constraction.TbConstractionCombatMapper' 主文件 主键字段:'Id' 主键值:'204' 重复.
记录1 来自文件:建筑_伤害表@21_系统_建筑表.xlsx
记录2 来自...(错误信息过长，已截断)
  [playid_7] at Luban.Job.Cfg.Defs.TableDataInfo.BuildIndexs() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\TableDataInfo.cs:line 90
at Luban.Job.Cfg.Defs.TableDataInfo..ctor(DefTable table, List`1 mainRecords, List`1 patchRecords, Dictionary`2 playModuleRecords) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\TableDataInfo.cs:line 36
at Luban.Job.Cfg.Defs.DefAssembly.AddDataTable(DefTable table, List`1 mainRecords, List`1 patchRecords, Dictionary`2 playModuleRecords) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\DefAssembly.cs:line 119
at Luban.Job.Cfg.Utils.DataLoaderUtil.LoadTableAsync(IAgent agent, DefTable table, String dataDir, String patchName, String patchDataDir, String inputConvertDataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 253
at Luban.Job.Cfg.Utils.DataLoaderUtil.<>c__DisplayClass9_1.<<LoadCfgDataAsync>b__1>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 272
--- End of stack trace from previous location ---
at Luban.Job.Cfg.Utils.DataLoaderUtil.LoadCfgDataAsync(IAgent agent, DefAssembly ass, String dataDir, String patchName, String patchDataDir, String inputConvertDataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 280
at Luban.Job.Cfg.JobController.<>c__DisplayClass2_1.<<GenAsync>g__CheckLoadCfgDataAsync|3>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 197
--- End of stack trace from previous location ---
at Luban.Job.Cfg.JobController.GenAsync(RemoteAgent agent, GenJob rpc) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 244
JOB_EXCEPTION
=======================================================================
===> 配置表 'itemReward.TbItemDrop' 主文件 主键字段:'id' 主键值:'10205' 重复.
记录1 来自文件:掉落_总表@01_掉落表.xlsx
记录2 来自文件:掉落_总表@01_掉落表.xlsx...(错误信息过长，已截断)
  [playid_8] at Luban.Job.Cfg.Defs.TableDataInfo.BuildIndexs() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\TableDataInfo.cs:line 90
at Luban.Job.Cfg.Defs.TableDataInfo..ctor(DefTable table, List`1 mainRecords, List`1 patchRecords, Dictionary`2 playModuleRecords) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\TableDataInfo.cs:line 36
at Luban.Job.Cfg.Defs.DefAssembly.AddDataTable(DefTable table, List`1 mainRecords, List`1 patchRecords, Dictionary`2 playModuleRecords) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\DefAssembly.cs:line 119
at Luban.Job.Cfg.Utils.DataLoaderUtil.LoadTableAsync(IAgent agent, DefTable table, String dataDir, String patchName, String patchDataDir, String inputConvertDataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 253
at Luban.Job.Cfg.Utils.DataLoaderUtil.<>c__DisplayClass9_1.<<LoadCfgDataAsync>b__1>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 272
--- End of stack trace from previous location ---
at Luban.Job.Cfg.Utils.DataLoaderUtil.LoadCfgDataAsync(IAgent agent, DefAssembly ass, String dataDir, String patchName, String patchDataDir, String inputConvertDataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 280
at Luban.Job.Cfg.JobController.<>c__DisplayClass2_1.<<GenAsync>g__CheckLoadCfgDataAsync|3>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 197
--- End of stack trace from previous location ---
at Luban.Job.Cfg.JobController.GenAsync(RemoteAgent agent, GenJob rpc) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 244
JOB_EXCEPTION
=======================================================================
===> 配置表 'units.TbMonsterScientistBehavior' 主文件 主键字段:'id' 主键值:'90003' 重复.
记录1 来自文件:新_行为模板表_科学家@12_单位_怪物.xlsx
记录2 来自文...(错误信息过长，已截断)
  [playid_9] at Luban.Job.Cfg.Defs.TableDataInfo.BuildIndexs() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\TableDataInfo.cs:line 90
at Luban.Job.Cfg.Defs.TableDataInfo..ctor(DefTable table, List`1 mainRecords, List`1 patchRecords, Dictionary`2 playModuleRecords) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\TableDataInfo.cs:line 36
at Luban.Job.Cfg.Defs.DefAssembly.AddDataTable(DefTable table, List`1 mainRecords, List`1 patchRecords, Dictionary`2 playModuleRecords) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\DefAssembly.cs:line 119
at Luban.Job.Cfg.Utils.DataLoaderUtil.LoadTableAsync(IAgent agent, DefTable table, String dataDir, String patchName, String patchDataDir, String inputConvertDataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 253
at Luban.Job.Cfg.Utils.DataLoaderUtil.<>c__DisplayClass9_1.<<LoadCfgDataAsync>b__1>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 272
--- End of stack trace from previous location ---
at Luban.Job.Cfg.Utils.DataLoaderUtil.LoadCfgDataAsync(IAgent agent, DefAssembly ass, String dataDir, String patchName, String patchDataDir, String inputConvertDataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 280
at Luban.Job.Cfg.JobController.<>c__DisplayClass2_1.<<GenAsync>g__CheckLoadCfgDataAsync|3>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 197
--- End of stack trace from previous location ---
at Luban.Job.Cfg.JobController.GenAsync(RemoteAgent agent, GenJob rpc) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 244
JOB_EXCEPTION
=======================================================================
===> 配置表 'units.TbMonsterScientistBehavior' 主文件 主键字段:'id' 主键值:'90003' 重复.
记录1 来自文件:新_行为模板表_科学家@12_单位_怪物.xlsx
记录2 来自文...(错误信息过长，已截断)
  [playid_99] at Luban.Job.Cfg.Defs.TableDataInfo.BuildIndexs() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\TableDataInfo.cs:line 90
at Luban.Job.Cfg.Defs.TableDataInfo..ctor(DefTable table, List`1 mainRecords, List`1 patchRecords, Dictionary`2 playModuleRecords) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\TableDataInfo.cs:line 36
at Luban.Job.Cfg.Defs.DefAssembly.AddDataTable(DefTable table, List`1 mainRecords, List`1 patchRecords, Dictionary`2 playModuleRecords) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Defs\DefAssembly.cs:line 119
at Luban.Job.Cfg.Utils.DataLoaderUtil.LoadTableAsync(IAgent agent, DefTable table, String dataDir, String patchName, String patchDataDir, String inputConvertDataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 253
at Luban.Job.Cfg.Utils.DataLoaderUtil.<>c__DisplayClass9_1.<<LoadCfgDataAsync>b__1>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 272
--- End of stack trace from previous location ---
at Luban.Job.Cfg.Utils.DataLoaderUtil.LoadCfgDataAsync(IAgent agent, DefAssembly ass, String dataDir, String patchName, String patchDataDir, String inputConvertDataDir) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\Utils\DataLoaderUtil.cs:line 280
at Luban.Job.Cfg.JobController.<>c__DisplayClass2_1.<<GenAsync>g__CheckLoadCfgDataAsync|3>d.MoveNext() in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 197
--- End of stack trace from previous location ---
at Luban.Job.Cfg.JobController.GenAsync(RemoteAgent agent, GenJob rpc) in E:\world\SocRes\SocData\ToolCode\luban-6.0\Luban.Job.Cfg\Source\JobController.cs:line 244
JOB_EXCEPTION
=======================================================================
===> 配置表 'itemReward.TbItemDrop' 主文件 主键字段:'id' 主键值:'50003' 重复.
记录1 来自文件:掉落_总表@01_掉落表.xlsx
记录2 来自文件:掉落_总表@01_掉落表.xlsx...(错误信息过长，已截断)

MD5验证错误 (9 个):
  [playid_101] 覆盖表 playid_101 生成的CS文件与原配置不一致:
  - global\GlobalConfig.cs
  - global\TbGlobalConfig.cs
  [playid_102] 覆盖表 playid_102 生成的CS文件与原配置不一致:
  - global\GlobalConfig.cs
  - global\TbGlobalConfig.cs
  [playid_2] 覆盖表 playid_2 生成的CS文件与原配置不一致:
  - global\GlobalConfig.cs
  - global\TbGlobalConfig.cs
  [playid_3] 覆盖表 playid_3 生成的CS文件与原配置不一致:
  - global\GlobalConfig.cs
  - global\TbGlobalConfig.cs
  [playid_6] 覆盖表 playid_6 生成的CS文件与原配置不一致:
  - global\GlobalConfig.cs
  - global\TbGlobalConfig.cs
  [playid_7] 覆盖表 playid_7 生成的CS文件与原配置不一致:
  - global\GlobalConfig.cs
  - global\TbGlobalConfig.cs
  [playid_8] 覆盖表 playid_8 生成的CS文件与原配置不一致:
  - global\GlobalConfig.cs
  - global\TbGlobalConfig.cs
  [playid_9] 覆盖表 playid_9 生成的CS文件与原配置不一致:
  - global\GlobalConfig.cs
  - global\TbGlobalConfig.cs
  [playid_99] 覆盖表 playid_99 生成的CS文件与原配置不一致:
  - global\GlobalConfig.cs
  - global\TbGlobalConfig.cs

