<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net6.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>annotations</Nullable>
		<OutputPath>..\..\..\Tool\</OutputPath>
		<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
		<StartupObject>SocDataConverter.Program</StartupObject>
		<AssemblyName>ConsoleApp1</AssemblyName>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="CommandLineParser" Version="2.9.1" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
		<PackageReference Include="NPOI" Version="2.7.4" />
		<PackageReference Include="Scriban" Version="4.1.0" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\..\..\..\..\Soc\SocConst\SocConst.csproj" />
	  <ProjectReference Include="..\Luban.ClientServer\Luban.ClientServer.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <None Update="TableTemplates\BizId2SystemType.tpl">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	  <None Update="TableTemplates\BluePrintConst.tpl">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	  <None Update="TableTemplates\Const.tpl">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	  <None Update="TableTemplates\SkinIdConverter.tpl">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	  <None Update="TableTemplates\ContainerSlot2Config.tpl">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
    <None Update="TableTemplates\NormalConst.tpl">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TableTemplates\TaskIdGroupConst.tpl">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
	</ItemGroup>
</Project>
