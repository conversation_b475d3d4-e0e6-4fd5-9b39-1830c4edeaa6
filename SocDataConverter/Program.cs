using CommandLine;
using SocDataConverter.Table;

namespace SocDataConverter
{
    class Program
    {
        public static string SocResourceCommonDir = Path.Combine(Util.Instance.RootDirInfo.FullName, "Soc", "SocCommon", "Soc.Common", "Soc.Common");
        public static string SocCodeCommonDir = Path.Combine(Util.Instance.RootDirInfo.FullName, "Soc", "SocServer", "SocWorld", "src", "Hotfix", "Share");
        public static string HotfixCodeDir = Path.Combine(Util.Instance.RootDirInfo.FullName, "Soc", "SocServer", "SocWorld", "src", "Hotfix");

        [Verb("excelexport", HelpText = "Run Luban Excel Export Code.")]
        class ExcelExportArguments
        {
            [Option('p', "datapath", Required = true, HelpText = "Path to excel baranch folder")]
            public string DataPath { get; set; }

            [Option('l', "language", Required = false, Default = "zh_CN", HelpText = "Select Language")]
            public string Language { get; set; }

            [Option("outputCodePrefix", Required = false, Default = "..",
                HelpText = "Path to other code output folder relative to \"SocData\" folder")]
            public string CodePrefix { get; set; }

            [Option("outputJsonPrefix", Required = false, Default = "..",
                HelpText = "Path to other json output folder relative to \"SocData\" folder")]
            public string JsonPrefix { get; set; }

            [Option("hotfix", Required = false, Default = false, HelpText = "enable hotfix export mode")]
            public bool IsHotFix { get; set; }

            [Option('s', "server", Required = false, HelpText = "只转换服务端表")]
            public bool OnlyConvertServer { get; set; }

            [Option('t', "postprocess", Required = false, HelpText = "只跑后处理")]
            public bool OnlyCodeParser { get; set; }

            [Option('d', "diff", Required = false, Default = false, HelpText = "打印覆盖表差异")]
            public bool PrintDiff { get; set; }

            [Option("validateOverride", Required = false, Default = true, HelpText = "使用智能差异检测验证DataPath下playid_*覆盖表目录生成的CS文件一致性")]
            public bool ValidateOverride { get; set; }

            [Option("detailedDiff", Required = false, Default = false, HelpText = "输出详细的覆盖表差异报告")]
            public bool DetailedDiff { get; set; }
        }

        static void Main(string[] args)
        {
            var parseResult = Parser.Default.ParseArguments<ExcelExportArguments>(args);
            parseResult.Value.OnlyCodeParser = false;
            var ret = RunExcelExportCode(parseResult.Value).GetAwaiter().GetResult();
            if (ret)
            {
                Console.WriteLine("OK，导表通过");
            }
            else
            {
                Console.WriteLine("Failed! 失败了，请查看日志，不要提交哦！！！");
            }
            Environment.Exit(ret ? 0 : 1);
        }

        private static async Task<bool> RunExcelExportCode(ExcelExportArguments arg)
        {
            if (!Directory.Exists(arg.DataPath))
            {
                Console.WriteLine($"Input path: [{arg.DataPath}] is illegal!");
                return false;
            }

            if (arg.IsHotFix)
            {
                return (await RunSingleExcelExportCodeAsync(arg, ExcelExporter.ExportType.hotfix) && RunCodeParser("Hotfix", arg));
            }
            else if (arg.OnlyConvertServer)
            {
                return (await RunSingleExcelExportCodeAsync(arg, ExcelExporter.ExportType.server) && RunCodeParser("Server", arg));
            }
            else if (arg.OnlyCodeParser)
            {
                return RunCodeParser("Server", arg);
            }
            else
            {
                var success = (await RunSingleExcelExportCodeAsync(arg, ExcelExporter.ExportType.server)
                    && await RunSingleExcelExportCodeAsync(arg, ExcelExporter.ExportType.client)
                    && RunCodeParser("Server", arg));

                // 如果启用了覆盖表验证，则执行验证
                if (success && arg.ValidateOverride)
                {
                    success = await RunOverrideTableValidation(arg);
                }

                return success;
            }
        }

        private static async Task<bool> RunSingleExcelExportCodeAsync(ExcelExportArguments arg, ExcelExporter.ExportType exportType)
        {
            var ret = await ExcelExporter.Run(arg.DataPath, arg.Language, exportType);

            var success = ret.Item1;

            if (!success)
            {
                Console.WriteLine(ret.Item2);
                Console.Error.WriteLine(ret.Item3);
            }
            return success;
        }

        private static bool RunCodeParser(string lubanDir, ExcelExportArguments arg)
        {
            try
            {
                var templateDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "TableTemplates");
                var lubanJsonDir = Path.Combine(SocResourceCommonDir, "Resources", "Data", lubanDir);
                var serverOutDir = Path.Combine(HotfixCodeDir, "NodeSystem", "AutoGenerate");
                var commonOutDir = Path.Combine(SocCodeCommonDir, "NodeSystem", "AutoGenerate");
                Console.WriteLine("Start to process server");
                TableConverterEntry.RunServer(lubanJsonDir, templateDir, serverOutDir, commonOutDir, Path.Combine(lubanJsonDir, "PostProcess"), arg.PrintDiff);
                Console.WriteLine("Finish process server");
                Console.WriteLine("Start to process client");
                TableConverterEntry.RunClient(Path.Combine(SocResourceCommonDir, "Resources", "Data", "Client"));
                Console.WriteLine("Finish process client");
                return true;
            }
            catch (Exception ex)
            {
                var errorMsg = ex.ToString();
                if (errorMsg.Contains("because it is being used by another process"))
                {
                    var msg = @$"
=======================================================================
导表后处理失败！
        文件: 无法获取
        错误位置: 无法获取
        Err: 文件被其它进程占用，请重新再跑一下
        字段: 无
=======================================================================

";
                    Console.WriteLine(msg);
                    Console.Error.WriteLine(msg);
                }
                else
                {
                    var msg = @$"
=======================================================================
导表后处理失败！
        文件: 无法获取
        错误位置: 无法获取
        Err: 请到编译报错群找服务端同学协助定位
        字段: {ex}
=======================================================================

";
                    Console.WriteLine(msg);
                    Console.Error.WriteLine(msg);
                }
                return false;
            }
        }

        /// <summary>
        /// 执行智能覆盖表验证
        /// </summary>
        private static async Task<bool> RunOverrideTableValidation(ExcelExportArguments arg)
        {
            try
            {
                Console.WriteLine("=======================================================================");
                Console.WriteLine("开始执行覆盖表验证...");
                Console.WriteLine("=======================================================================");

                var validator = new OverrideTableValidator(arg.DataPath, enableDebugLog: false); // 设置为true以启用DEBUG日志
                
                Console.WriteLine(" 验证服务端表...");
                await validator.ValidateAsync(arg.Language, ExcelExporter.ExportType.server, arg.DetailedDiff);

                // 如果需要，验证客户端表
                if (!arg.OnlyConvertServer && !arg.IsHotFix)
                {
                    Console.WriteLine(" 验证客户端表...");
                    await validator.ValidateAsync(arg.Language, ExcelExporter.ExportType.client, arg.DetailedDiff);
                }

                Console.WriteLine("=======================================================================");
                Console.WriteLine(" 覆盖表验证完成！");
                if (arg.DetailedDiff)
                {
                    Console.WriteLine(" 详细差异报告: 已启用详细分析和统计");
                }
                Console.WriteLine("=======================================================================");
                return true;
            }
            catch (Exception ex)
            {
                var errorMsg = @$"
=======================================================================
 覆盖表验证失败！
=======================================================================

详细信息: {ex}

如需技术支持，请联系服务端同学
=======================================================================

";
                Console.Error.WriteLine(errorMsg);
                return false;
            }
        }
    }
}