using Bright.Net.Codecs;
using Luban.Client.Common.Utils;
using Luban.Common;
using Luban.Common.Protos;
using Luban.Common.Utils;
using Luban.Job.Cfg.Cache;
using Luban.Job.Common.Tpl;
using Luban.Server.Common;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.Collections.Concurrent;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using YamlDotNet.RepresentationModel;

namespace SocDataConverter
{
    public class LocalSessionWrapper : SessionWrapper
    {
        public bool IsError { get; private set; }
        public StringBuilder Output = new();
        public StringBuilder ErrorOutput = new();
        public GenJobRes Res { get; private set; }
        
        public override Task<TRes> CallRpcAsync<TRpc, TArg, TRes>(TArg arg, int timeout)
        {
            throw new NotImplementedException();
        }

        public override void Send(Protocol proto)
        {
            if (proto is PushLog logProto)
            {
                Output.AppendLine($"[{logProto.Level}] {logProto.LogContent}");
            }
            else
                throw new NotImplementedException();
        }

        public override void ReplyRpc<TRpc, TArg, TRes>(TRpc rpc, TRes res)
        {
            if (res is GenJobRes genJobRes)
            {
                Res = genJobRes;
                if (genJobRes.ErrCode == EErrorCode.OK)
                {
                    Output.AppendLine("OK");
                }
                else
                {
                    IsError = true;
                    ErrorOutput.AppendLine(genJobRes.StackTrace?.ToString() ?? "");
                    ErrorOutput.AppendLine(genJobRes.ErrCode.ToString());
                    ErrorOutput.AppendLine(genJobRes.ErrMsg);
                }
            }
            else
                throw new NotImplementedException();
        }

        public override void LogError(EErrorCode code, string msg)
        {
            IsError = true;
            ErrorOutput.AppendLine(code.ToString());
            ErrorOutput.AppendLine(msg);
        }

        public static async Task DownloadGeneratedFiles(string outputDir, List<Luban.Common.Protos.FileInfo> newFiles)
        {
            List<Task> tasks = new();
            foreach (var file in newFiles)
            {
                if (!await CacheMetaManager.Ins.CheckFileChangeAsync(outputDir, file.FilePath, file.MD5))
                {
                    continue;
                }
                tasks.Add(Task.Run(async () =>
                {
                    var cache = CacheManager.Ins.FindCache(file.MD5);
                    await FileUtil.SaveFileAsync(outputDir, file.FilePath, cache.Content);
                    await CacheMetaManager.Ins.UpdateFileAsync(outputDir, file.FilePath, file.MD5);
                }));
            }
            await Task.WhenAll(tasks);

            FileCleaner.Clean(outputDir, newFiles);
        }

        public static async Task DownloadGeneratedFile(Luban.Common.Protos.FileInfo file)
        {
            if (!await CacheMetaManager.Ins.CheckFileChangeAsync(null, file.FilePath, file.MD5))
            {
                return;
            }
            var cache = CacheManager.Ins.FindCache(file.MD5);
            await FileUtil.SaveFileAsync(null, file.FilePath, cache.Content);
            await CacheMetaManager.Ins.UpdateFileAsync(null, file.FilePath, file.MD5);
        }
    }

    public class LocalSessionAgent : LocalAgent
    {
        private static readonly Regex _subResPattern = new Regex(@"(.+)\[(.+)\]$");
        
        private readonly ConcurrentDictionary<string, YamlDocument> _cacheYamlDocs = new();
        
        public LocalSessionAgent(SessionWrapper session) : base(session)
        {
        }

        public override async Task<GetImportFileOrDirectoryRes> GetFileOrDirectoryAsync(string file, params string[] searchPatterns)
        {
            var suffixes = searchPatterns;
            var re = new GetImportFileOrDirectoryRes()
            {
                SubFiles = new List<Luban.Common.Protos.FileInfo>(),
            };

            try
            {
                if (Directory.Exists(file))
                {
                    re.Err = 0;
                    re.IsFile = false;
                    foreach (var subFile in Directory.GetFiles(file, "*", SearchOption.AllDirectories))
                    {
                        if (FileUtil.IsValidInputFile(subFile) && (suffixes.Length == 0 || suffixes.Any(s => subFile.EndsWith(s))))
                        {
                            var md5 = await CacheMetaManager.Ins.GetOrUpdateFileMd5Async(subFile);
                            re.SubFiles.Add(new Luban.Common.Protos.FileInfo() { FilePath = FileUtil.Standardize(subFile), MD5 = md5 });
                        }
                    }

                }
                else if (File.Exists(file))
                {
                    re.IsFile = true;
                    re.Md5 = await CacheMetaManager.Ins.GetOrUpdateFileMd5Async(file);
                }
                else
                {
                    re.Err = Luban.Common.EErrorCode.FILE_OR_DIR_NOT_EXISTS;
                }
            }
            catch (Exception e)
            {
                re.Err = EErrorCode.READ_FILE_FAIL;
                Session.LogError(re.Err, e.ToString());
            }
            return re;
        }

        public async override Task<XElement> OpenXmlAsync(string xmlFile)
        {
            return XElement.Load(new MemoryStream(await ReadAllBytesAsync(xmlFile)));
        }

        private YamlDocument GetCacheYamlDoc(string mainResFileName)
        {
            return _cacheYamlDocs.GetOrAdd(mainResFileName, (file) =>
            {
                var yamlStream = new YamlStream();
                yamlStream.Load(new StreamReader(new FileStream(mainResFileName, FileMode.Open, FileAccess.Read, FileShare.ReadWrite)));
                return yamlStream.Documents[0];
            });
        }
        
        private bool CheckSubResourceExists(string mainResFileName, string subResName)
        {
            if (!FileUtil.IsFileExistsSenseCase(mainResFileName))
            {
                return false;
            }
            try
            {
                var yamlNode = (YamlMappingNode)GetCacheYamlDoc(mainResFileName).RootNode;
                var yamlSubResName = new YamlScalarNode(subResName);
                foreach (var (resType, node) in yamlNode)
                {

                    switch (resType.ToString())
                    {
                        case "SpriteAtlas":
                            {
                                var mnode = (YamlMappingNode)node;
                                var r = (YamlSequenceNode)mnode[new YamlScalarNode("m_PackedSpriteNamesToIndex")];
                                if (r == null)
                                {
                                    return false;
                                }
                                return r.Contains(yamlSubResName);
                            }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Session.LogError(EErrorCode.JOB_EXCEPTION, ex.ToString());
                return false;
            }
        }
        
        public async override Task<QueryFilesExistsRes> QueryFileExistsAsync(QueryFilesExistsArg arg)
        {
            var root = arg.Root;
            var files = arg.Files;
            var re = new QueryFilesExistsRes() { Exists = new List<bool>(files.Count) };

            var tasks = new List<Task<bool>>();

            foreach (var f in files)
            {
                var match = _subResPattern.Match(f);
                if (match.Success)
                {
                    var groups = match.Groups;
                    tasks.Add(Task.Run(() => CheckSubResourceExists(Path.Join(root, groups[1].Value), groups[2].Value)));
                }
                else
                {
                    tasks.Add(Task.Run(() => FileUtil.IsFileExistsSenseCase(Path.Combine(root, f))));
                }
            }

            await Task.WhenAll(tasks);
            foreach (var task in tasks)
            {
                re.Exists.Add(task.Result);
            }
            return re;
        }
    }

    public class ExcelExporter
    {
        public enum ExportType
        {
            client,
            server,
            hotfix,
            check,
        }

        /// <summary>
        /// 代码输出文件夹的路径
        /// </summary>
        public static Dictionary<ExportType, string> CodeOutputPath = new Dictionary<ExportType, string>
        {
            { ExportType.client, Path.Combine("Soc", "SocCommon", "Soc.Common", "Soc.Common", "Data", "Client") },
            { ExportType.server, Path.Combine("Soc", "SocCommon", "Soc.Common", "Soc.Common", "Data", "Server") },
            { ExportType.hotfix, string.Empty },
        };

        /// <summary>
        /// 数据输出文件夹的路径
        /// </summary>
        public static Dictionary<ExportType, string> JsonOutputPath = new Dictionary<ExportType, string>
        {
            { ExportType.client, Path.Combine("Soc", "SocCommon", "Soc.Common", "Soc.Common", "Resources", "Data", "Client") },
            { ExportType.server, Path.Combine("Soc", "SocCommon", "Soc.Common", "Soc.Common", "Resources", "Data", "Server") },
            { ExportType.hotfix, Path.Combine("Soc", "SocCommon", "Soc.Common", "Soc.Common", "Resources", "Data", "HotFix") },
        };

        /// <summary>
        /// 模板文件夹的路径
        /// </summary>
        private static Dictionary<ExportType, string> TemplatePath = new Dictionary<ExportType, string>
        {
            { ExportType.client, @"ClientTemplates"},
            { ExportType.server, @"ServerTemplates"},
            { ExportType.hotfix, string.Empty },
        };

        /// <summary>
        /// 执行转表的相关代码
        /// </summary>
        /// <param name="excelDir">Excel分支文件夹路径，可能是相对路径，也可能是绝对路径</param>
        /// <param name="language">导出语言</param>
        /// <param name="exportType">导出类型</param>
        /// <param name="checkOutputDir">差异检查输出路径</param>
        public static async Task<(bool, string, string)> Run(string excelDir, string language, ExportType exportType, string checkOutputDir = null)
        {
            Console.OutputEncoding = Encoding.UTF8;
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            var excelDirectoryInfo = new DirectoryInfo(excelDir);
            excelDir = excelDirectoryInfo.FullName;

            var dataDirInfo = new DirectoryInfo(Path.Combine(Util.Instance.RootDirInfo.FullName, JsonOutputPath[exportType]));
            var oldMd5Dict = dataDirInfo.GetFiles("*.json").ToDictionary(fileInfo => fileInfo.Name, fileInfo => Util.CalcMD5(File.ReadAllBytes(fileInfo.FullName)));

            //拼接参数
            var arguments = new List<string>();
            var outputType = new List<string>();
            //cache文件名
            arguments.Add("-c");
            arguments.Add($"{exportType}.cache.meta");
            //自定义template文件夹路径
            if (exportType != ExportType.hotfix)
            {
                arguments.Add("-t");
                arguments.Add($"{Path.Combine(Util.Instance.ExeDirInfo.FullName, TemplatePath[exportType])}");
            }
            //以上是luban参数，以下是生成参数
            arguments.Add("-j");
            arguments.Add("cfg");
            arguments.Add("--");
            //根定义文件名
            arguments.Add("-d");
            arguments.Add($"{Path.Combine(excelDir, "Defines", "__root__.xml")}");
            //Excel文件目录
            arguments.Add("--input_data_dir");

            arguments.Add($"{Path.Combine(excelDir, "Datas")}");
            //生成分组目标
            arguments.Add("-s");
            arguments.Add($"{(exportType == ExportType.hotfix ? "all" : exportType.ToString())}");
            //生成代码文件的目录
            if (exportType != ExportType.hotfix)
            {
                arguments.Add("--output_code_dir");
                arguments.Add($"{Path.Combine(Util.Instance.RootDirInfo.FullName, CodeOutputPath[exportType])}");
            }
            //生成数据文件的目录
            arguments.Add("--output_data_dir");
            arguments.Add($"{Path.Combine(Util.Instance.RootDirInfo.FullName, JsonOutputPath[exportType])}");
            //生成类型
            arguments.Add("--gen_types");
            arguments.Add($"{(exportType == ExportType.hotfix ? string.Empty : "code_cs_unity_json,")}data_json");
            //强制使用无前缀的Vector2、3、4，方便在代码开头使用Using指定Vector2、3、4到底是Unity的Vector还是System的Vector
            //注：非Luban原始功能
            arguments.Add($"--cs:use_pure_vector");
            arguments.Add("--export_type");
            arguments.Add($"{exportType}");

            //本地化，有翻译文件再进行本地化
            var allL10nExcelFiles = Directory.EnumerateFiles(Path.Combine(excelDir, "Datas", "l10n"), "*.xlsx")
                .Where(filepath => !filepath.Contains("~$"));
            if (allL10nExcelFiles.Any())
            {
                //本地化的文本映射表
                arguments.Add("--l10n:input_text_files");

                arguments.Add($"{string.Join(",", allL10nExcelFiles)}");
                //使用的语言
                arguments.Add("--l10n:text_field_name");
                arguments.Add($"{language}");
                //未翻译成功的文本，列表输出文件
                arguments.Add("--l10n:output_not_translated_text_file");
                arguments.Add($"{Path.Combine(Util.Instance.RootDirInfo.FullName, "SocRes", "SocData", $"{exportType}_not_translated.txt")}");
            }

            var zoneConfig = GetTimeZoneConfig(excelDir);
            if (zoneConfig != null)
            {
                arguments.Add("--l10n:timezone");
                arguments.Add($"{zoneConfig}");
            }
            TimeZoneUtil.InitDefaultTimeZone(zoneConfig);

            //测试代码：输出参数
            var argumentsString = string.Join(" ", arguments);
            Console.WriteLine(argumentsString);
            bool success;

            var parseResult = Luban.ClientServer.Program.ParseArgs(arguments.ToArray());
            if (parseResult.Item1 != null)
            {
                success = false;
                return (success, string.Empty, parseResult.Item1.ToString());
            }

            var sessionWrapper = new LocalSessionWrapper();
            var agent = new LocalSessionAgent(sessionWrapper);
            var job = new GenJob
            {
                Arg = new()
            };
            job.Arg.JobArguments = parseResult.Item2.JobArguments;

            CacheManager.Ins.Reset();
            FileRecordCacheManager.Ins.Init(true);
            StringTemplateManager.Ins.Init(true);
            if (!string.IsNullOrEmpty(parseResult.Item2.TemplateSearchPath))
            {
                StringTemplateManager.Ins.AddTemplateSearchPath(parseResult.Item2.TemplateSearchPath);
            }
            StringTemplateManager.Ins.AddTemplateSearchPath(FileUtil.GetPathRelateApplicationDirectory("Templates"));

            CacheMetaManager.Ins.Load($"{exportType}.cache.meta");
            await new Luban.Job.Cfg.JobController().GenAsync(agent, job);

            var tasks = new List<Task>();
            var res = sessionWrapper.Res;
            foreach (var fg in res.FileGroups)
            {
                // [ALB] 保存.cs代码和.Json数据文件
                tasks.Add(LocalSessionWrapper.DownloadGeneratedFiles(fg.Dir, fg.Files));
            }

            foreach (var f in res.ScatteredFiles)
            {
                tasks.Add(LocalSessionWrapper.DownloadGeneratedFile(f));
            }
            Task.WaitAll(tasks.ToArray());

            CacheMetaManager.Ins.Save();
            CacheMetaManager.Ins.Reset();
            success = !sessionWrapper.IsError;

            //如果成功
            if (success)
            {
                //记录本次修改的文件列表
                var newMd5Dict = dataDirInfo.GetFiles("*.json").ToDictionary(fileInfo => fileInfo.Name, fileInfo => Util.CalcMD5(File.ReadAllBytes(fileInfo.FullName)));
                var dirtyDatas = new List<string>();
                foreach (var (fileName, md5) in newMd5Dict)
                {
                    if (oldMd5Dict.GetValueOrDefault(fileName) != md5)
                    {
                        dirtyDatas.Add(fileName);
                    }
                }
                File.WriteAllLines(Path.Combine(excelDir, $"{exportType}_dirty.txt"), dirtyDatas);

                //移动language_default.json到指定目录
                if (File.Exists(Path.Combine(dataDirInfo.FullName, "language_default.json")))
                {
                    File.Move(Path.Combine(dataDirInfo.FullName, "language_default.json"), Path.Combine(dataDirInfo.FullName, "..", "Language", "Default", "language_default.json"), true);
                }
            }

            return (success, sessionWrapper.Output.ToString(), sessionWrapper.ErrorOutput.ToString());
        }

        public static string? GetTimeZoneConfig(string excelDir)
        {
            string filePath = Path.Combine(excelDir, "Datas", "02_全局配置表.xlsx");
            // 创建一个Workbook对象
            IWorkbook workbook;
            using (var file = new System.IO.FileStream(filePath, System.IO.FileMode.Open, System.IO.FileAccess.Read))
            {
                workbook = new XSSFWorkbook(file); // 支持新的.XLSX格式
            }
            // 获取第一个工作表
            ISheet sheet = workbook.GetSheetAt(0);

            // 遍历行和列，并读取单元格的数据
            for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
            {
                IRow row = sheet.GetRow(rowIndex);

                if (row != null)
                {
                    for (int columnIndex = 0; columnIndex < row.LastCellNum; columnIndex++)
                    {
                        ICell cell = row.GetCell(columnIndex);

                        if (cell != null && cell.CellType == CellType.String)
                        {
                            string cellValue = cell.StringCellValue;
                            if (cellValue == "TimeZone")
                            {
                                var id = (int)row.GetCell(columnIndex + 3)?.NumericCellValue;
                                var timeZoneStr = GetTimeZoneStringId(id);
                                workbook.Close();
                                return timeZoneStr;
                            }
                        }
                    }
                }
            }

            // 关闭Workbook对象
            workbook.Close();
            return null;
        }

        private static string? GetTimeZoneStringId(int? id)
        {
            if (id == null)
            {
                throw new Exception("配置的时区为空！！！");
            }
            if (id < -12 || id > 14) throw new Exception("配置的时区错了！！！");
            var timeZones = TimeZoneInfo.GetSystemTimeZones();
            foreach (TimeZoneInfo timeZone in timeZones)
            {
                int offsetHours = (int)timeZone.BaseUtcOffset.TotalHours;
                if (offsetHours == id)
                {
                    return timeZone.Id;
                }
            }

            return null;

        }
    }
}
