namespace WizardGames.Soc.SocWorld.NodeSystem
{
    public static class {{classname}}
    {
        public static long ShortToLong(long shortId)
        {
            return shortId switch
            {
                {{~for table_line in converted_table~}}
                {{table_line.ShortId}} => {{table_line.LongId}},
                {{~end~}}
                _ => shortId,
            };
        }
        public static long LongToShort(long longId)
        {
            return longId switch
            {
                {{~for table_line in converted_table~}}
                {{table_line.LongId}} => {{table_line.ShortId}},
                {{~end~}}
                _ => longId,
            };
        }

    }
}