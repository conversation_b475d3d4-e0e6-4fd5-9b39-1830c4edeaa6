using SocDataConverter.Table.Checker;
using SocDataConverter.Table.Converter;
using SocDataConverter.Table.PostFill;

namespace SocDataConverter.Table
{
    public class TableConverterEntry
    {
        public static void RunServer(string lubanJsonPath, string templatePath, string serverOutPath, string commonOutPath, string jsonOutPath, bool printDiff)
        {
            ConvertSheetInfo.ClearCache();
            ConvertSheetInfo.lubanJsonPath = lubanJsonPath;

            CreateDiretoryIfNotExsit(serverOutPath);
            CreateDiretoryIfNotExsit(commonOutPath);
            CreateDiretoryIfNotExsit(jsonOutPath);

            InitConverters(templatePath, serverOutPath, commonOutPath, jsonOutPath);
            InitPostFills(lubanJsonPath);
            InitCheckers();

            // new ScatterTableChecker().Check(lubanJsonPath);
        }

        public static void RunClient(string lubanJsonPath)
        {
            ConvertSheetInfo.ClearCache();
            ConvertSheetInfo.lubanJsonPath = lubanJsonPath;
            InitPostFills(lubanJsonPath);
        }

        private static void InitConverters(string templatePath, string serverOutPath, string commonOutPath, string jsonOutPath)
        {
            BluePrintTableConfig.AddToConvertList();
            ConstConfig.AddToConvertList();
            BizId2SystemTypeTableConfig.AddToConvertList();
            TechnologyConfig.AddToConvertList();
            ConstructionConverterConfig.AddToConvertList();
            PermissionConverterConfig.AddToConvertList();
            TaskTableConfig.AddToConvertList();
            ContainerConfig.AddToConvertList();
            SheetConverter.AllConverts.ForEach(info => ParseOneSheet(serverOutPath, commonOutPath, jsonOutPath, templatePath, info));
        }

        private static void InitPostFills(string lubanJsonPath)
        {
            SheetPostFill.AddSheetPostFill(new LobbyMainTaskPostFill());
            SheetPostFill.AddSheetPostFill(new MedalPostFill());
            SheetPostFill.AllPostFills.ForEach(info => info.Execute(lubanJsonPath));
        }

        private static void InitCheckers()
        {
            SheetChecker.AllSheetCheckers.Clear();
            SheetChecker.AddSheetChecker(new ItemDropChecker());
            SheetChecker.AddSheetChecker(new ItemDropLimitChecker());
            SheetChecker.AddSheetChecker(new PlayScoreStatChecker());
            SheetChecker.AddSheetChecker(new PlayChecker());
            SheetChecker.AddSheetChecker(new RecipeChecker());
            SheetChecker.AddSheetChecker(new TechnolgyChecker());
            SheetChecker.AddSheetChecker(new VehicleChecker());
            SheetChecker.AddSheetChecker(new BuffTableChecker());
            SheetChecker.AddSheetChecker(new SurprisePlayChecker());
            SheetChecker.AddSheetChecker(new WaterContainerChecker());
            SheetChecker.AddSheetChecker(new ItemChecker());
            SheetChecker.AddSheetChecker(new BotChecker());
            SheetChecker.AddSheetChecker(new PreseBuildRewardChecker());
            SheetChecker.AddSheetChecker(new DeadSheepPcuChecker());
            SheetChecker.AddSheetChecker(new ConstructionConstantChecker());
            SheetChecker.AddSheetChecker(new ConstructionCountLimitChecker());
            SheetChecker.AddSheetChecker(new PackagesChecker());
            SheetChecker.AddSheetChecker(new DeathDropChecker());
            SheetChecker.AddSheetChecker(new GlobalConstChecker());
            SheetChecker.AddSheetChecker(new GestureSprayWheelChecker());
            SheetChecker.AddSheetChecker(new LobbySkinChecker());
            SheetChecker.AddSheetChecker(new PoiTaskChecker());
            SheetChecker.AddSheetChecker(new DailyTaskChecker());
            SheetChecker.AddSheetChecker(new ShopChecker());
            SheetChecker.AddSheetChecker(new LobbyMainTaskChecker());
            SheetChecker.AddSheetChecker(new TrainCarChecker());
            SheetChecker.AddSheetChecker(new TaskChecker());
            SheetChecker.AddSheetChecker(new BlueprintChecker());
            SheetChecker.AddSheetChecker(new SpawnChecker());
            SheetChecker.AddSheetChecker(new IdGroupChecker());
            SheetChecker.AddSheetChecker(new ConditionChecker());
            SheetChecker.AddSheetChecker(new TalentChecker());
            SheetChecker.AddSheetChecker(new StoryStageChecker());
            SheetChecker.AllSheetCheckers.ForEach(checker => checker.ParseAndCheck());
        }

        private static void CreateDiretoryIfNotExsit(string path)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
        }

        internal static void ParseOneSheet(string serverCodePath, string commonCodePath, string jsonOutPath, string templatePath, ConvertSheetInfo info)
        {
            try
            {
                info.ReadJsonData();

                var fileInfo = info.GetOutputFileInfo(serverCodePath, commonCodePath, jsonOutPath);
                if (!Directory.Exists(fileInfo.Directory.FullName))
                {
                    Directory.CreateDirectory(fileInfo.Directory.FullName);
                }

                Util.WriteTextToPathWithCRLF(fileInfo.FullName, info.Convert(templatePath));
                Console.WriteLine($"Create File : {fileInfo.FullName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ParseOneSheet {info.OutputName} error {ex}");
                throw;
            }
        }
    }
}
