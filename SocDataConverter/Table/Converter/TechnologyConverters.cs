using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Converter
{
    public class TechnologyConfig
    {
        internal static void AddToConvertList()
        {
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(new List<string>() { "dataitem_tbtechnology" }, string.Empty, "TechnologyConst", new TechnologyConverter(), "s"));
        }
    }

    internal class TechnologyConverter : SheetConvertToJson
    {
        private Dictionary<int, HashSet<int>> ChildsRecord = new();

        public override JToken ConvertJson(ConvertSheetInfo info)
        {
            var techDict = IdKeyData.GetData(info.GetInputInfo(0));

            var childList = new JArray();
            foreach (var (id, data) in techDict)
            {
                var parentIds = IdKeyData.GetIntList(data, "parentId");
                foreach (var parentId in parentIds)
                {
                    if (id == parentId)
                    {
                        throw new Exception(String.Format("97科技表, parent:{0} == self:{1}", parentId, id));
                    }

                    if (ChildsRecord.TryGetValue(parentId, out var childs))
                    {
                        if (childs.Contains(id))
                        {
                            throw new Exception(String.Format("parent:{0}, already add child:{1}", parentId, id));
                        }
                        else
                        {
                            childs.Add(id);
                        }
                    }
                    else
                    {
                        ChildsRecord.Add(parentId, new HashSet<int>() { id });
                    }
                }
            }

            foreach (var (id, data) in techDict)
            {
                if (!ChildsRecord.TryGetValue(id, out var childs))
                {
                    childs = new HashSet<int>();
                }

                var tempList = new JArray();
                foreach (var child in childs) tempList.Add(child);

                var childJson = new JObject()
                {
                    ["childs"] = tempList,
                    ["id"] = id,
                };
                childList.Add(childJson);
            }

            return new JObject()
            {
                ["converted_table"] = childList,
            };
        }
    }
}
