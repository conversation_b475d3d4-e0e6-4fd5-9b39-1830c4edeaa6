using Newtonsoft.Json.Linq;
using Scriban.Runtime;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace SocDataConverter.Table.Converter
{
    internal class PermissionConverterConfig
    {
        internal static void AddToConvertList()
        {
            SheetConverter.AddSheetConverter(
                new ConvertSheetInfo(new List<string>() { "constraction_tbpermissionsysgroup" }, "NormalConst", "PermissionConst", new PermissionConverter(), "cs"));
        }
    }
    internal class PermissionConverter : SheetConvertToCsharp
    {
        public override ScriptObject ConvertCode(ConvertSheetInfo info)
        {
            var input0 = info.GetInputInfo(0).InputJson;
            var permissionList = new List<ScriptObject>();
            Dictionary<int, ulong> mk = new();
            foreach (var data in input0)
            {
                var permGrpId = data.Value<int>("PermGrpId");
                var permUnitQuoted = data["PermUnitQuoted"] as JArray;
                var permList = permUnitQuoted.ToObject<List<int>>();
                var permMask = CalcMask(permList);
                mk[permGrpId] = permMask;
            }
            
            permissionList.Add(new ScriptObject()
            {
                ["param_type"] = "static Dictionary<int, ulong>",
                ["key"] = "PermissionGroupMask",
                ["value"] = $"new(){{ {string.Join(", ", mk.Select(kv => $"{{ {kv.Key}, {kv.Value} }}")) }}}",
            });

            return new ScriptObject()
            {
                ["ns"] = "WizardGames.Soc.Common.Construction",
                ["classname"] = "PermissionConst",
                ["converted_table"] = permissionList,
            };
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public ulong CalcMask(IEnumerable<int> permissionUnitIds)
        {
            ulong mask = 0;
            foreach (int permissionUnitId in permissionUnitIds)
            {
                Debug.Assert(permissionUnitId >= 1 && permissionUnitId <= 64, $"permissionUnitId {permissionUnitId}");
                mask |= (ulong)1 << (permissionUnitId - 1);
            }
            return mask;
        }
    }
}
