using Newtonsoft.Json.Linq;
using Scriban.Runtime;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Converter
{
    internal class FrequencyItemConverter : SheetConvertToCsharp
    {
        const int RF_RECEIVER = 137;
        const int RF_TRANSMITTER = 138;

        public override ScriptObject ConvertCode(ConvertSheetInfo info)
        {
            List<ScriptObject> constList = new();

            var itemFlagsDict = IdKeyData.GetData(info.GetInputInfo(0));
            HashSet<long> receivers = new();
            HashSet<long> transmitter = new();
            foreach (var (itemId, obj) in itemFlagsDict)
            {
                var ja = obj["Itemflags"] as JArray;
                if (IsInList(ja, RF_RECEIVER))
                {
                    receivers.Add(itemId);
                }
                if (IsInList(ja, RF_TRANSMITTER))
                {
                    transmitter.Add(itemId);
                }
            }
            constList.Add(new ScriptObject()
            {
                ["param_type"] = "static HashSet<long>",
                ["key"] = "RFReceivers",
                ["value"] = $"new(){{ {string.Join(',', receivers)} }}",
            });

            constList.Add(new ScriptObject()
            {
                ["param_type"] = "static HashSet<long>",
                ["key"] = "RFTransmitters",
                ["value"] = $"new(){{ {string.Join(',', transmitter)} }}",
            });

            return new ScriptObject()
            {
                ["classname"] = "FrequencyItems",
                ["converted_table"] = constList,
            };
        }
        private bool IsInList(JArray ja, int id)
        {
            foreach (var item in ja)
            {
                if (item.ToObject<int>() == id)
                {
                    return true;
                }
            }
            return false;
        }
    }
}
