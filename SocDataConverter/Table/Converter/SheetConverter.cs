using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Scriban;
using Scriban.Runtime;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Converter
{
    internal interface ISheetConverter
    {
        string Convert(ConvertSheetInfo info, string templatePath);
    }

    internal abstract class SheetConvertToCsharp : ISheetConverter
    {
        public abstract ScriptObject ConvertCode(ConvertSheetInfo info);

        public string Convert(ConvertSheetInfo info, string templatePath)
        {
            var templateFileInfo = new FileInfo(Path.Combine(templatePath, $"{info.TemplateName}.tpl"));
            var templateString = File.ReadAllText(templateFileInfo.FullName);
            if (info.OutputNamespaces.Contains('c'))
            {
                templateString = templateString.Replace("namespace WizardGames.Soc.SocWorld", "namespace WizardGames.Soc.Common");
            }

            var templateCtx = new TemplateContext()
            {
                LoopLimit = 10000,
            };
            var sObject = ConvertCode(info);
            templateCtx.PushGlobal(sObject);

            var template = Template.Parse(templateString);
            return template.Render(templateCtx);
        }
    }

    internal abstract class SheetConvertToJson : ISheetConverter
    {
        public abstract JToken ConvertJson(ConvertSheetInfo info);

        public string Convert(ConvertSheetInfo info, string templatePath)
        {
            return ConvertJson(info).ToString();
        }
    }

    internal class ConvertSheetInfo
    {
        internal static string lubanJsonPath;

        public readonly List<string> InputNames;
        public readonly string TemplateName;
        public readonly string OutputName;
        public readonly ISheetConverter Converter;
        public readonly string OutputNamespaces;

        private static Dictionary<string, JArray> inputCache = new();
        private static Dictionary<string, int> inputHash = new();

        public JArray[] InputJsonList;
        public ConvertSheetInfo(List<string> inputNames, string templateName, string outputName, ISheetConverter converter, string outputNamespaces = "s")
        {
            InputNames = inputNames;
            TemplateName = templateName;
            OutputName = outputName;
            Converter = converter;
            OutputNamespaces = outputNamespaces;
        }

        public string Convert(string templatePath) => Converter.Convert(this, templatePath);

        public static void ClearCache()
        {
            inputCache.Clear();
            inputHash.Clear();
        }

        public static JArray getJsonFromCache(string filePath, string inputName)
        {
            if (inputCache.TryGetValue(filePath, out var result)) { return result; }
            var jsonFileInfo = new FileInfo(filePath);
            var jsonString = File.ReadAllText(jsonFileInfo.FullName);
            var raw = JsonConvert.DeserializeObject<JObject>(jsonString);
            var data = raw["data"] as JArray;
            inputHash.Add(inputName, int.Parse(raw["hash"].ToString()));
            inputCache.Add(filePath, data);
            return data;
        }

        public static int GetInputHash(string inputName) => inputHash[inputName];

        public void ReadJsonData()
        {
            InputJsonList = new JArray[InputNames.Count];
            for (var i = 0; i < InputNames.Count; i++)
            {
                try
                {
                    InputJsonList[i] = getJsonFromCache(Path.Combine(lubanJsonPath, $"{InputNames[i]}.json"), InputNames[i]);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"ReadJsonData {InputNames[i]} error {ex}");
                    throw;
                }
            }
        }

        public InputInfo GetInputInfo(int seq)
        {
            return new InputInfo()
            {
                Name = InputNames[seq],
                InputJson = InputJsonList[seq]
            };
        }

        public FileInfo GetOutputFileInfo(string serverCodePath, string commonCodePath, string jsonOutPath)
        {
            if (Converter is SheetConvertToCsharp)
            {
                var outputPath = serverCodePath;
                if (OutputNamespaces.Contains('c'))
                    outputPath = commonCodePath;
                return new FileInfo(Path.Combine(outputPath, $"{OutputName}_AutoGenerate.cs"));
            }
            else if (Converter is SheetConvertToJson)
            {
                return new FileInfo(Path.Combine(jsonOutPath, $"{OutputName}_AutoGenerate.json"));
            }
            else
            {
                throw new ArgumentException(nameof(Converter));
            }
        }

        public override string ToString()
        {
            return OutputName;
        }
    }

    internal class SheetConverter
    {
        internal static List<ConvertSheetInfo> AllConverts = new();

        public static void AddSheetConverter(ConvertSheetInfo converter)
        {
            AllConverts.Add(converter);
        }
    }
}
