using Scriban.Runtime;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Converter
{
    internal abstract class  ShortLongMapperConverter : SheetConvertToCsharp
    {
        public abstract string FileName { get; }
        public abstract string KeyName { get; }
        public abstract string TableInfo { get; }
        public string Template => "ShortLongMapper";


        private Dictionary<long, long> Short2LongMapper = new();

        private Dictionary<long, long> Long2ShortMapper = new();
        private long NowMaxId = 0;

        private void DealData()
        {
            var OpCodeDir = Path.Combine(Program.SocCodeCommonDir, "NodeSystem", "AutoGenerate");
            var files = Directory.GetFiles(OpCodeDir, $"{FileName}_AutoGenerate.cs");

            NowMaxId = 0;
            foreach (var file in files)
            {
                var lines = File.ReadAllLines(file);
                foreach (var line in lines)
                {
                    if (line.Contains(" => "))
                    {
                        var strs = line.Split(" => ");
                        strs[0] = strs[0].Trim();
                        if (strs[0].Contains('_')) { break; }
                        if (long.TryParse(strs[0], out var param1)
                            && long.TryParse(strs[1].Trim().Replace(",", "").Trim(), out var param2))
                        {
                            Short2LongMapper[param1] = param2;
                            Long2ShortMapper[param2] = param1;
                            NowMaxId = Math.Max(param1, NowMaxId);
                        }
                    }
                }
            }
        }

        public override ScriptObject ConvertCode(ConvertSheetInfo info)
        {
            DealData();

            var buildingCoreDict = IdKeyData.GetData(KeyName, info.GetInputInfo(0));
            foreach (var (partId, _) in buildingCoreDict)
            {
                if (Long2ShortMapper.ContainsKey(partId)) { continue; }
                ++NowMaxId;
                Short2LongMapper.Add(NowMaxId, partId);
            }

            List<ScriptObject> constList = new();
            foreach (var (shortId, longId) in Short2LongMapper)
            {
                constList.Add(new ScriptObject
                {
                    ["ShortId"] = shortId.ToString(),
                    ["LongId"] = longId.ToString()
                });
            }
            return new ScriptObject()
            {
                ["classname"] = FileName,
                ["converted_table"] = constList,
            };
        }

        public void AddToConverts()
        {
            SheetConverter.AddSheetConverter(
                new ConvertSheetInfo(new List<string>() { TableInfo }, Template, FileName, this, "cs"));
        }
    }

    internal class ConstructionOutsideTemplateIdConverter : ShortLongMapperConverter
    {
        public override string FileName => "ConstructionOutsideMapper_Template";
        public override string KeyName => "partId";
        public override string TableInfo => "constraction_tbbuildingcore";
    }


    internal class ConstructionOutsideSkinIdConverter : ShortLongMapperConverter
    {
        public override string FileName => "ConstructionOutsideMapper_Skin";
        public override string KeyName => "ID";
        public override string TableInfo => "resource_tbskin";
    }
}