using Newtonsoft.Json.Linq;
using Scriban.Runtime;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Converter
{
    internal class ContainerConfig
    {
        internal static void AddToConvertList()
        {
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(new List<string>() { "dataitem_tbcontainer" },
                "ContainerSlot2Config", "ContainerSlot2ConfigConst", new ContainerSheetConverter(), "cs"
            ));
        }
    }

    internal class ContainerSheetConverter : SheetConvertToCsharp
    {
        public override ScriptObject ConvertCode(ConvertSheetInfo info)
        {
            var itemDict = IdKeyData.GetData(info.GetInputInfo(0));
            List<ScriptObject> constList = new();
            foreach (var (id, data) in itemDict)
            {
                if (!data.TryGetValue("specificItemSlots", out var slots)) continue;
                var slotList = slots as JArray;
                foreach (var slotId in slotList)
                {
                    constList.Add(new ScriptObject()
                    {
                        ["id"] = int.Parse(slotId.ToString()),
                        ["value"] = id,
                    });
                }
            }
            return new ScriptObject()
            {
                ["converted_table"] = constList,
            };
        }
    }
}
