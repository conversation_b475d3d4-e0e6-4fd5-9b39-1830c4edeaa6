using Newtonsoft.Json.Linq;
using Scriban.Runtime;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Converter
{
    internal class ConstructionConverterConfig
    {
        internal static void AddToConvertList()
        {
            SheetConverter.AddSheetConverter(
                new ConvertSheetInfo(new List<string>() { "constraction_tbbuildingcore" }, "Const", "BuildingConst", new BoxStorageConverter()));
            
            SheetConverter.AddSheetConverter(
                new ConvertSheetInfo(new List<string>() { "construction_tbconstructioncombochildgroupconfig" },
                "ConstructionChangeConfig", "ConstructionChangeConst", new ConstructionChangeConverter(), "cs"));

            new ConstructionOutsideTemplateIdConverter().AddToConverts();
            new ConstructionOutsideSkinIdConverter().AddToConverts();
            //TableConverterMain.AllConverts.Add(
            //    new ConvertSheetInfo(new List<string>() { "resource_tbskin" },
            //    "SkinIdConverter", "SkinIdConverter", new SkinIdConverter(), "cs"));

            SheetConverter.AddSheetConverter(new ConvertSheetInfo(new List<string>() { "construction_tbconstructioncombochildgroupconfig", "blueprintdata_tbblueprint", "constraction_tbconstractiongrade" }, "ConstructionIngredient", "ConstructionIngredients", new ConstructionIngredientConverter()));
        }
    }

    internal class BoxStorageConverter : SheetConvertToCsharp
    {
        const int ExtraFunctionContainer = 1;

        public override ScriptObject ConvertCode(ConvertSheetInfo info)
        {
            List<ScriptObject> constList = new();

            // 无功能的容器盒子id常量
            var buildingCoreDict = IdKeyData.GetData("partId", info.GetInputInfo(0));
            List<int> containerBizIds = new();
            foreach (var (partId, data) in buildingCoreDict)
            {
                var extraFunc = IdKeyData.TryGetIntVal(data, "extraFunction");
                if (ExtraFunctionContainer == extraFunc)
                    containerBizIds.Add(partId);
            }
            constList.Add(new ScriptObject()
            {
                ["param_type"] = "static HashSet<long>",
                ["key"] = "BoxContainerIds",
                ["value"] = $"new(){{ {string.Join(',', containerBizIds)} }}",
            });

            return new ScriptObject()
            {
                ["classname"] = "BuildingConst",
                ["converted_table"] = constList,
            };
        }
    }
    
    internal class ConstructionChangeConverter : SheetConvertToCsharp
    {
        private Dictionary<long, int> gradeDt = new();

        // 下一个等级的升级分支
        private Dictionary<long, List<long>> nextGradeInfo = new();
        private Dictionary<long, List<long>> comboChilds = new();
        public override ScriptObject ConvertCode(ConvertSheetInfo info)
        {
            var input0 = info.GetInputInfo(0).InputJson;
            foreach (var data in input0)
            {
                var singleData = data as JObject;
                var comboGroupIdStr = singleData["partTypeId"].ToString();
                var comboGroupId = long.Parse(comboGroupIdStr);

                var gradeS = singleData["Grade"].ToString();
                var grade = int.Parse(gradeS);
                gradeDt.Add(comboGroupId, grade);

                var childPartTypeList = singleData["ComboId"] as JArray;
                if (childPartTypeList.Count > 0)
                {
                    var comboIds = new List<long>();
                    foreach(var cid in childPartTypeList)
                    {
                        var childPartType = long.Parse(cid.ToString());
                        comboIds.Add(childPartType);
                    }
                    comboChilds.Add(comboGroupId, comboIds);
                }
                else
                {
                    throw new Exception($"[建筑_子建筑表] 子建筑配置错误 {comboGroupId}, ComboId is null");
                }

                if (singleData["upgradeBranch"] == null)
                    continue;
                var upgradeBranch = singleData["upgradeBranch"] as JArray;
                if (upgradeBranch.Count == 0)
                    continue;
                List<long> upgradeBranchIds = new List<long>();
                foreach (var ubId in upgradeBranch)
                {
                    var upgradeBranchId = long.Parse(ubId.ToString());
                    upgradeBranchIds.Add(upgradeBranchId);
                }
                if (upgradeBranchIds.Count == 0)
                    continue;
                nextGradeInfo.Add(comboGroupId, upgradeBranchIds);
            }
            // 所有等级的升级情况
            Dictionary<long, Dictionary<int, HashSet<long>>> result = new();

            foreach (var comboGroupId in nextGradeInfo.Keys)
            {
                Dictionary<int, HashSet<long>> upgradePartTypeDt = new();
                try
                {
                    SearchUpgradeBranch(comboGroupId, upgradePartTypeDt, 0);
                }
                catch (Exception ex)
                {
                    throw new Exception($"[建筑_子建筑表] 可能升级有环 {comboGroupId}, 内部错误: {ex.Message}");
                }

                result.Add(comboGroupId, upgradePartTypeDt);
            }

            List<ScriptObject> partList = new();
            foreach (var partId in result.Keys)
            {
                var partInfo = result[partId];

                partList.Add(new ScriptObject()
                {
                    ["id"] = partId,
                    ["value"] = CreateListString(partInfo),
                });
            }

            return new ScriptObject()
            {
                ["classname"] = "ConstructionChangeConst",
                ["converted_table"] = partList
            };
        }
        public string CreateListString(Dictionary<int, HashSet<long>> partInfo)
        {
            var hs = new HashSet<long> { 1, 2, 3 };
            List<string> ltS = new();
            foreach (var lt in partInfo)
            {
                var grade = lt.Key;
                var parts = lt.Value.ToList();
                parts.Sort();
                var s = $"[{grade}] = " + "new() {" + string.Join(",", parts) + "}";
                ltS.Add(s);
            }
            var tm = $@" new() 
            {{ 
                {string.Join(",\r\n                ", ltS)}
            }},
";
            return tm;
        }

        public void SearchUpgradeBranch(long comboGroupId, Dictionary<int, HashSet<long>> upgradePartTypeDt, int depth)
        {
            if (depth >= 100)
            {
                throw new Exception($"未结束的检查点 {comboGroupId}");
            }
            if (!nextGradeInfo.ContainsKey(comboGroupId))
                return;
            var ptUpgradeBranch = nextGradeInfo[comboGroupId];
            if (ptUpgradeBranch.Count == 0)
            {
                return;
            }
            for (int i = 0; i < ptUpgradeBranch.Count; i++)
            {
                var pt = ptUpgradeBranch[i];
                var grade = gradeDt[pt];
                if (!upgradePartTypeDt.ContainsKey(grade))
                    upgradePartTypeDt.Add(grade, new());
                upgradePartTypeDt[grade].Add(pt);
                SearchUpgradeBranch(pt, upgradePartTypeDt, depth + 1);
            }
        }
    }

    internal class SkinIdConverter : SheetConvertToCsharp
    {
        public override ScriptObject ConvertCode(ConvertSheetInfo info)
        {
            Dictionary<long, long> lobbySkinIdMap = new();
            var input0 = info.GetInputInfo(0).InputJson;
            foreach (var data in input0)
            {
                var singleData = data as JObject;
                var IDs = singleData["ID"].ToString();
                var id = long.Parse(IDs);

                var battleSkinIDs = singleData["BattleSkinID"].ToString();
                var battleSkinID = long.Parse(battleSkinIDs);

                lobbySkinIdMap.Add(battleSkinID, id);
            }

            List<ScriptObject> partList = new();
            foreach (var (battleSkinId, lobbySkinId) in lobbySkinIdMap)
            {
                partList.Add(new ScriptObject()
                {
                    ["id"] = battleSkinId,
                    ["value"] = lobbySkinId,
                });
            }

            return new ScriptObject()
            {
                ["converted_table"] = partList
            };
        }
    }

    internal class ConstructionIngredientConverter : SheetConvertToCsharp
    {
        public override ScriptObject ConvertCode(ConvertSheetInfo info)
        {
            HashSet<string> ingredientIds = new();

            var buildingcoreTb = info.GetInputInfo(0).InputJson;
            var blueprintTb = info.GetInputInfo(1).InputJson;
            var constructionGradeTb = info.GetInputInfo(2).InputJson;

            foreach (var data in buildingcoreTb)
            {
                var singleData = data as JObject;
                var childPartTypeList = singleData["ComboId"] as JArray;
                if (childPartTypeList.Count > 0)
                {
                    foreach (var cid in childPartTypeList)
                    {
                        var childPartType = long.Parse(cid.ToString());
                        AddIngredientId(childPartType, ingredientIds, blueprintTb);
                    }
                }
            }

            foreach (var data in constructionGradeTb)
            {
                var singleData = data as JObject;
                var itemIdStr = singleData["itemId"].ToString();
                ingredientIds.Add(itemIdStr);
            }

            List<ScriptObject> ingredientIdList = new();
            foreach (var ingredientId in ingredientIds)
            {
                ingredientIdList.Add(new ScriptObject()
                {
                    ["value"] = ingredientId,
                });
            }

            return new ScriptObject()
            {
                ["classname"] = "ConstructionIngredientConst",
                ["converted_table"] = ingredientIdList
            };
        }

        private void AddIngredientId(long partTypeId, HashSet<string> ingredientIds, JArray blueprintTb)
        {
            foreach (var data in blueprintTb)
            {
                var singleData = data as JObject;
                var itemIdStr = singleData["ItemId"].ToString();
                var itemId = long.Parse(itemIdStr);
                if (itemId == partTypeId)
                {
                    var ingredientList = singleData["Ingredients"] as JArray;
                    if (ingredientList.Count > 0)
                    {
                        foreach (var ingredient in ingredientList)
                        {
                            ingredientIds.Add(ingredient.ToString());
                        }
                    }
                }
            }
        }
    }
}
