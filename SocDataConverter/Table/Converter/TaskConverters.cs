using Newtonsoft.Json.Linq;
using Scriban.Runtime;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Converter
{
    internal class TaskObject
    {
        public int TaskId { get; set; }
        public int NextTaskId { get; set; }
        public int FinalTaskId { get; set; }
    }

    public class TaskTableConfig
    {
        internal static void AddToConvertList()
        {
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(new List<string>() { "play_tbquestphase" }, "TaskDicConst", "TaskDicConst", new TaskDicConverter()));
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(new List<string>() { "play_tbtaskidgroup" }, "TaskIdGroupConst", "BizId2GroupIdSet", new TaskIdGroupConverter()));
        }
    }

    internal class TaskDicConverter : SheetConvertToJson
    {
        public override JToken ConvertJson(ConvertSheetInfo info)
        {
            var outputList = new JArray();
            var taskDic = IdKeyData.GetData(info.GetInputInfo(0));

            // 排序 理清任务阶段顺序
            Dictionary<int, List<(int, JObject)>> allTaskDic = new();
            foreach (var (keyId, data) in taskDic)
            {
                var taskId = IdKeyData.GetIntVal(data, "taskId");
                var id = IdKeyData.GetIntVal(data, "id");
                if (!allTaskDic.ContainsKey(taskId))
                {
                    allTaskDic.Add(taskId, new List<(int, JObject)>() { (id, data) });
                }
                else
                {
                    allTaskDic[taskId].Add((id, data));
                }
            }

            foreach (var (taskId, item) in allTaskDic)
            {
                allTaskDic[taskId] = item.OrderBy(t => IdKeyData.GetIntVal(t.Item2, "taskNumber")).ToList();
            }

            var id2FirstTaskId = new JArray();
            foreach (var (taskId, item) in allTaskDic)
            {
                if (item.Count < 0) throw new Exception($"任务阶段表中任务id {taskId} 没有配置任务阶段！！！");
                if (taskId != 0)
                {
                    id2FirstTaskId.Add(JToken.FromObject(new TaskObject()
                    {
                        TaskId = taskId,
                        NextTaskId = item[0].Item1,
                        FinalTaskId = item[item.Count - 1].Item1,
                    }));
                    for (int i = 0; i < item.Count; i++)
                    {
                        var taskObject = new TaskObject();
                        if (i == item.Count - 1)
                        {
                            taskObject.TaskId = item[i].Item1;
                            taskObject.NextTaskId = -1;
                            taskObject.FinalTaskId = item[i].Item1;
                        }
                        else
                        {
                            taskObject.TaskId = item[i].Item1;
                            taskObject.NextTaskId = item[i + 1].Item1;
                            taskObject.FinalTaskId = item[item.Count - 1].Item1;
                        }
                        outputList.Add(JToken.FromObject(taskObject));
                    }
                }
                else
                {
                    for (int i = 0; i < item.Count; i++)
                    {
                        var taskObject = new TaskObject();
                        taskObject.TaskId = item[i].Item1;
                        taskObject.NextTaskId = -1;
                        outputList.Add(JToken.FromObject(taskObject));
                    }
                }

            }

            var output = new JObject
            {
                { "task", outputList },
                { "firstTask", id2FirstTaskId }
            };

            return output;
        }
    }

    internal class TaskIdGroupConverter : SheetConvertToCsharp
    {
        public override ScriptObject ConvertCode(ConvertSheetInfo info)
        {
            var idGroupDic = IdKeyData.GetData("Id", info.GetInputInfo(0));
            var bizId2GroupIdSet = new Dictionary<long, List<long>>();
            foreach (var (_, line) in idGroupDic)
            {
                var groupId = IdKeyData.GetIntVal(line, "Id");
                var idList = IdKeyData.GetIntList(line, "IdList");
                foreach (var bizId in idList)
                {
                    if (!bizId2GroupIdSet.TryGetValue(bizId, out var existingGroup))
                    {
                        existingGroup = new List<long>();
                        bizId2GroupIdSet[bizId] = existingGroup;
                    }
                    existingGroup.Add(groupId);
                }
            }

            var retList = new ScriptArray();
            foreach (var (bizId, groupIdList) in bizId2GroupIdSet)
            {
                var obj = new ScriptObject();
                obj["id"] = bizId;
                var arr = new ScriptArray();
                foreach (var groupId in groupIdList)
                {
                    arr.Add(groupId);
                }
                obj["group"] = arr;
                retList.Add(obj);
            }

            return new ScriptObject()
            {
                ["classname"] = "BizId2GroupIdSet_AutoGenerate",
                ["converted_table"] = retList,
            };
        }
    }
}
