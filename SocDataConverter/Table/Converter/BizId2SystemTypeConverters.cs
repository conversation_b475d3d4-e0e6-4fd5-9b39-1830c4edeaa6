using Newtonsoft.Json.Linq;
using Scriban.Runtime;
using Soc.Common.CodeParser.Table.Intermedia;
using WizardGames.Soc.Common.Const;
using WizardGames.SocConst.Soc.Const;

namespace SocDataConverter.Table.Converter
{
    public class BizId2SystemTypeTableConfig
    {
        internal static void AddToConvertList()
        {
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(new List<string>()
            {
                "dataitem_tbitemconfig",
                "blueprintdata_tbblueprint" ,
                "dataitem_tbtechnology",
                "reputation_tbreputationconvertconfig",
                "play_tbquestphase",
                "money_tbmoneyconfig",
                "dataitem_tbskinconfig",
                "reputation_tbreputationbadgeconfig",
                "plant_tbplantseedconfig",
                "charactor_tbbuff",
                "constraction_tbbuildingcoreskin",
                // TODO: 与上面Name2SystemType对应
            },

            "BizId2SystemType",
            "BizId2SystemTypeConst", new BizId2SystemTypeConverter("BizId2SystemTypeConst")));
        }
    }

    internal class BizId2SystemTypeConverter : SheetConvertToCsharp
    {
        public Dictionary<string, Action<IDictionary<int, JObject>, List<ScriptObject>, int>> funcs = new();
        public Dictionary<string, IDictionary<int, JObject>> tableName2ItemDict = new();

        // 表格对应的系统类型
        public static readonly Dictionary<string, int> Name2SystemType = new()
        {
            { "dataitem_tbitemconfig", NodeSystemType.PlayerInventory },
            { "blueprintdata_tbblueprint", NodeSystemType.Blueprint },
            { "dataitem_tbtechnology", NodeSystemType.Technology },
            { "money_tbmoneyconfig", NodeSystemType.ReputationSystem },
            { "play_tbquestphase", NodeSystemType.TaskSystem },
            { "dataitem_tbskinconfig", NodeSystemType.Skin },
            { "reputation_tbreputationbadgeconfig", NodeSystemType.ReputationBadge },
            { "charactor_tbbuff", NodeSystemType.Buff},
            { "constraction_tbbuildingcoreskin", NodeSystemType.Skin }
            //TODO: 新类型添加
        };
        public Dictionary<string, string> Name2KeyName = new()
        {
            { "constraction_tbbuildingcoreskin", "Id"}
        };
        private readonly string OutputClassName;

        private Dictionary<long, int> Record = new();

        private string curConverterTableName = string.Empty;

        public BizId2SystemTypeConverter(string outputClassName)
        {
            funcs.Add("dataitem_tbitemconfig", this.InnerItemConverter);
            funcs.Add("blueprintdata_tbblueprint", this.InnerBluePrintConverter);
            funcs.Add("dataitem_tbtechnology", this.InnerCommonConverter);
            funcs.Add("money_tbmoneyconfig", this.InnerCommonConverter);
            funcs.Add("play_tbquestphase", this.InnerTaskConverter);
            funcs.Add("dataitem_tbskinconfig", this.InnerCommonConverter);
            funcs.Add("reputation_tbreputationbadgeconfig", this.InnerCommonConverter);
            funcs.Add("charactor_tbbuff", this.InnerCommonConverter);
            funcs.Add("constraction_tbbuildingcoreskin", this.InnerCommonConverter);
            this.OutputClassName = outputClassName;
        }

        public void InnerBluePrintConverter(IDictionary<int, JObject> itemDict, List<ScriptObject> constList, int systemType)
        {
            foreach (var (id, data) in itemDict)
            {
                if (Record.TryGetValue(id, out var retType))
                {
                    throw new Exception(String.Format("id:{0}, already define type:{1}", id, retType));
                }

                constList.Add(new ScriptObject()
                {
                    ["id"] = id,
                    ["value"] = systemType,
                });
                AddRecord(id, systemType);
            }
        }

        public void InnerItemConverter(IDictionary<int, JObject> itemDict, List<ScriptObject> constList, int systemType)
        {
            var reputationConvertDict = tableName2ItemDict["reputation_tbreputationconvertconfig"];
            var seedConvertDict = tableName2ItemDict["plant_tbplantseedconfig"];

            var reputationOutputIds = new HashSet<long>();
            foreach (var (_, data) in reputationConvertDict)
            {
                var outputId = IdKeyData.GetLongVal(data, "converOutputId");
                reputationOutputIds.Add(outputId);
            }
            
            foreach (var (id, data) in itemDict)
            {
                if (Record.TryGetValue(id, out var retType))
                {
                    throw new Exception(String.Format("id:{0}, already define type:{1}", id, retType));
                }

                var systemTypeId = systemType;
                var manufacturing = int.Parse(data["manufacturing"].ToString());
                var secondaryClassification = int.Parse(data["SecondaryClassification"].ToString());

                if (reputationOutputIds.Contains(id))
                {
                    systemTypeId = NodeSystemType.PlayerInventory;  //背包系统
                }
                else if (seedConvertDict.ContainsKey(id))
                {
                    systemTypeId = NodeSystemType.SeedBackpackSystem;  //种子背包系统
                }
                else if (manufacturing == ItemType.Misc && secondaryClassification == MiscSubType.PoiTaskItem)
                {
                    systemTypeId = NodeSystemType.TaskSystem;
                }

                constList.Add(new ScriptObject()
                {
                    ["id"] = id,
                    ["value"] = systemTypeId,
                });
                AddRecord(id, systemType);
            }
        }

        public void InnerCommonConverter(IDictionary<int, JObject> itemDict, List<ScriptObject> constList, int systemType)
        {
            foreach (var (id, data) in itemDict)
            {
                if (Record.TryGetValue(id, out var retType))
                {
                    throw new Exception(String.Format("id:{0}, already define type:{1}", id, retType));
                }

                constList.Add(new ScriptObject()
                {
                    ["id"] = id,
                    ["value"] = systemType,
                });
                AddRecord(id, systemType);
            }
        }

        public void InnerTaskConverter(IDictionary<int, JObject> itemDict, List<ScriptObject> constList, int systemType)
        {
            foreach (var (id, data) in itemDict)
            {
                if (Record.TryGetValue(id, out var retType))
                {
                    throw new Exception(String.Format("id:{0}, already define type:{1}", id, retType));
                }

                constList.Add(new ScriptObject()
                {
                    ["id"] = id,
                    ["value"] = systemType,
                });
                AddRecord(id, systemType);
            }
        }

        public override ScriptObject ConvertCode(ConvertSheetInfo info)
        {
            if (info.InputJsonList.Length != info.InputNames.Count)
            {
                throw new Exception(String.Format("jsonCount:{0} != nameCount:{1}", info.InputJsonList.Length, info.InputNames.Count));
            }

            List<ScriptObject> constList = new();

            for (int i = 0; i < info.InputJsonList.Length; i++)
            {
                if (!Name2KeyName.TryGetValue(info.InputNames[i], out var keyName))
                    keyName = "id";
                var input = info.GetInputInfo(i);
                var itemDict = IdKeyData.GetData(keyName, input);
                tableName2ItemDict.Add(info.InputNames[i], itemDict);
            }

            for (int i = 0; i < info.InputJsonList.Length; i++)
            {
                if (!Name2SystemType.ContainsKey(info.InputNames[i])) continue;

                var systemType = Name2SystemType[info.InputNames[i]];
                var func = funcs[info.InputNames[i]];
                curConverterTableName = info.InputNames[i];
                func.Invoke(tableName2ItemDict[info.InputNames[i]], constList, systemType);
            }

            return new ScriptObject()
            {
                ["classname"] = OutputClassName,
                ["converted_table"] = constList,
            };
        }

        private void AddRecord(long id, int systemType)
        {
            foreach (var segment in CustomBizIdConst.CustomBizIdSegments)
            {
                if (id >= segment[0] && id <= segment[1])
                {
                    throw new ArgumentException($"id:{id} 配置表名称:{curConverterTableName} 侵入程序自定义BizId范围:{segment[0]} - {segment[1]}.");
                }
            }

            if (id <= NodeConst.IdGroupEnd && id >= NodeConst.IdGroupStart)
            {
                throw new ArgumentException($"id:{id} 配置表名称:{curConverterTableName} 侵入组Id范围:{NodeConst.IdGroupStart} - {NodeConst.IdGroupEnd}");
            }

            Record.Add(id, systemType);
        }
    }
}
