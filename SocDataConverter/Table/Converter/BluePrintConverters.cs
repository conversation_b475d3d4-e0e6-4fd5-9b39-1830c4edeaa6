using Scriban.Runtime;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Converter
{
    public class BluePrintTableConfig
    {
        internal static void AddToConvertList()
        {
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "blueprintdata_tbblueprint", "dataitem_tbresearchbench", "dataitem_tbtechnology",
                "constraction_tbbuildingcore", "constraction_tbconstructionbueprint", "dataitem_tbitemconfig"},
                "BluePrintConst", "CommonBluePrintConst", new BluePrintConverter("BluePrintConst"), "cs"));
        }
    }

    internal class BluePrintConverter : SheetConvertToCsharp
    {
        private readonly string outputClassName;
        public BluePrintConverter(string outputClassName)
        {
            this.outputClassName = outputClassName;
        }
        public override ScriptObject ConvertCode(ConvertSheetInfo info)
        {

            var bpDict = IdKeyData.GetData(info.GetInputInfo(0));
            var researchBenchDict = IdKeyData.GetData(info.GetInputInfo(1));
            var techDict = IdKeyData.GetData(info.GetInputInfo(2));
            var buildcoreDict = IdKeyData.GetData("partId", info.GetInputInfo(3));
            var constructionDict = IdKeyData.GetData("bpId", info.GetInputInfo(4));
            var itemDict = IdKeyData.GetData(info.GetInputInfo(5));
            // 初始蓝图
            List<ScriptObject> constList = new();
            // 蓝图id对应的道具Id
            List<ScriptObject> bp2ItemList = new();
            // 道具id对应蓝图Id
            List<ScriptObject> item2bpList = new();
            // 道具对应道具蓝图Id
            List<ScriptObject> item2ItembpList = new();
            // 道具蓝图ID对应道具Id
            List<ScriptObject> itembp2itemList = new();
            // 蓝图对应科技ID
            List<ScriptObject> bpId2TechList = new();
            // 蓝图ID对应子建筑ID
            List<ScriptObject> bpId2BuildCoreList = new();
            // 子建筑ID对应蓝图ID
            List<ScriptObject> buildCoreId2bpList = new();
            // 蓝图ID对应官方小屋ID
            List<ScriptObject> bpId2ConstructionList = new();
            // 官方小屋ID对应蓝图ID
            List<ScriptObject> ConstructionldCoreId2bpList = new();
            Dictionary<long, List<long>> bpId2Tech = new();
            foreach (var (id, data) in bpDict)
            {
                var type = IdKeyData.GetIntVal(data, "type");

                // 只有道具蓝图需要Default 和 UserCraftable 都为True 才是默认解锁蓝图

                if (data.TryGetValue("Default", out var isDefault) && data.TryGetValue("baseCount", out var baseCount) && data.TryGetValue("UserCraftable", out var UserCraftable))
                {
                    var defaultString = isDefault.ToString().Trim();
                    var baseCountString = baseCount.ToString().Trim();
                    var UserCraftableString = UserCraftable.ToString().Trim();

                    if (type == 1 && defaultString == "True" && UserCraftableString == "True" && baseCountString.Length > 0)
                    {
                        constList.Add(new ScriptObject()
                        {
                            ["id"] = id,
                            ["value"] = baseCountString,
                        });
                    }
                    else
                    {
                        if (type != 1 && defaultString == "True")
                        {
                            constList.Add(new ScriptObject()
                            {
                                ["id"] = id,
                                ["value"] = baseCountString,
                            });
                        }
                    }
                }


                if (IdKeyData.GetLongVal(data, "ItemId") == 0)
                {
                    throw new System.Exception($"Blueprint {id} has no ItemId");
                }

                if (type == 1)// 道具蓝图
                {
                    bp2ItemList.Add(new ScriptObject()
                    {
                        ["id"] = id,
                        ["value"] = IdKeyData.GetLongVal(data, "ItemId"),
                    });

                    item2bpList.Add(new ScriptObject()
                    {
                        ["id"] = IdKeyData.GetLongVal(data, "ItemId"),
                        ["value"] = id,
                    });
                }
                else if (type == 2) // 子建筑蓝图
                {
                    var buildcoreId = (int)IdKeyData.GetLongVal(data, "ItemId");
                    bool craft = IdKeyData.GetBoolVal(data, "UserCraftable");
                    if (craft)
                    {
                        throw new System.Exception($"98蓝图表中，Id:{id}, 子建筑蓝图不能制造");
                    }

                    if (!buildcoreDict.TryGetValue(buildcoreId, out var temp))
                    {
                        throw new System.Exception($"98蓝图表中，item类内容:{buildcoreId}, 在建筑表中找不到对应的id");
                    }


                    bpId2BuildCoreList.Add(new ScriptObject()
                    {
                        ["id"] = id,
                        ["value"] = IdKeyData.GetLongVal(data, "ItemId"),
                    });

                    buildCoreId2bpList.Add(new ScriptObject()
                    {
                        ["id"] = IdKeyData.GetLongVal(data, "ItemId"),
                        ["value"] = id,
                    });
                }
                else if (type == 3)// 官方小屋蓝图
                {
                    var constructionId = (int)IdKeyData.GetLongVal(data, "ItemId");
                    if (!constructionDict.TryGetValue(constructionId, out var temp))
                    {
                        throw new System.Exception($"98蓝图表中，item类内容:{constructionId}, 在官方小屋表中找不到对应的id");
                    }

                    bool craft = IdKeyData.GetBoolVal(data, "UserCraftable");
                    if (craft)
                    {
                        throw new System.Exception($"98蓝图表中，Id:{id}, 官方小屋蓝图不能制造");
                    }

                    bpId2ConstructionList.Add(new ScriptObject()
                    {
                        ["id"] = id,
                        ["value"] = IdKeyData.GetLongVal(data, "ItemId"),
                    });

                    ConstructionldCoreId2bpList.Add(new ScriptObject()
                    {
                        ["id"] = IdKeyData.GetLongVal(data, "ItemId"),
                        ["value"] = id,
                    });
                }
                else
                {
                    throw new System.Exception($"98蓝图表中 {id}, type 未定义类型");
                }
            }

            foreach (var (itemId, data) in researchBenchDict)
            {
                if (IdKeyData.GetLongVal(data, "blueprintItemId") == 0)
                {
                    throw new System.Exception($"research {itemId} has no bp item Id");
                }
            }

            foreach (var (id, data) in itemDict)
            {
                // 暂时只记录一对一的蓝图道具 - 道具, 当前策划也会配置1对多的蓝图道具，暂时功能用不到。
                // gwf保证配表的正确性
                var blueprintIds = IdKeyData.GetLongList(data, "blueprintIds");
                if (blueprintIds.Count == 1)
                {
                    item2ItembpList.Add(new ScriptObject()
                    {
                        ["id"] = blueprintIds[0],
                        ["value"] = id,
                    });

                    itembp2itemList.Add(new ScriptObject()
                    {
                        ["id"] = id,
                        ["value"] = blueprintIds[0],
                    });

                }
            }

            foreach (var (techId, data) in techDict)
            {
                var bpIds = IdKeyData.GetLongList(data, "blueprintIds");
                foreach (var bpId in bpIds)
                {
                    if (!bpId2Tech.TryGetValue(bpId, out var records))
                    {
                        records = new();
                        bpId2Tech.Add(bpId, records);
                    }

                    records.Add(techId);
                }
            }


            foreach (var (bpId, techIds) in bpId2Tech)
            {
                bpId2TechList.Add(new ScriptObject()
                {
                    ["id"] = bpId,
                    ["value"] = "new List<long>() {" + string.Join(",", techIds) + "}",
                });
            }

            return new ScriptObject()
            {
                ["classname"] = outputClassName,
                ["converted_table"] = constList,
                ["converted_table1"] = bp2ItemList,
                ["converted_table2"] = item2bpList,
                ["converted_table3"] = item2ItembpList,
                ["converted_table4"] = itembp2itemList,
                ["converted_table5"] = bpId2TechList,
                ["converted_table6"] = bpId2BuildCoreList,
                ["converted_table7"] = buildCoreId2bpList,
                ["converted_table8"] = bpId2ConstructionList,
                ["converted_table9"] = ConstructionldCoreId2bpList,
            };
        }
    }
}
