using Luban.Job.Cfg.Cache;
using Newtonsoft.Json.Linq;
using Scriban.Runtime;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Converter
{
    public class ConstConfig
    {
        internal static void AddToConvertList()
        {
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "dataitem_tbitemconfig" },
                "Const", "ItemConst", new ConstConverter("ItemConst", "id"), "cs"
            ));
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "dataitem_tbcontainer" },
                "Const", "ContainerConst", new ConstConverter("ContainerConst", "id"), "cs"
            ));
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "units_tbtreasurebox" },
                "Const", "TreasureBoxConst", new ConstConverter("TreasureBoxConst", "id"), "cs"
            ));

            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "money_tbmoneyconfig" },
                "Const", "MoneyConst", new ConstConverter("MoneyConst", "id"), "cs"
            ));
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "language_tbcodelanguage" },
                "CommentConst", "LanguageConst", new LanguageConstConverter(), "cs"
            ));
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "tips_tbcommontip" },
                "Const", "CommonTipConst", new ConstConverter("CommonTipConst", "id"), "cs"
            ));
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "dataitem_tbitemtipsbuttonmanagement" },
                "Const", "ItemTipsBtnConst", new ConstConverter("ItemTipsBtnConst", "id"), "cs"
            ));
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "tips_tbforcepopconfig" },
                "Const", "ForcePopConst", new ConstConverter("ForcePopConst", "id"), "cs"
            ));
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "common_tbpushmessagecontent" },
                "Const", "AppPushTipConst", new ConstConverter("AppPushTipConst", "id"), "cs"
            ));
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "tips_tbmsgboxconfig" },
                "Const", "MsgBoxConst", new ConstConverter("MsgBoxConst", "id"), "cs"
            ));
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "constraction_tbpermissionsysgroup" },
                "Const", "PermissionGroupConst", new ConstConverter("PermissionGroupConst", "PermGrpId"), "cs"
            ));
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "vehicle_tbvehicleinfo" },
                "Const", "VehicleConst", new ConstConverter("VehicleConst", "id"), "cs"
            ));
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "vehicle_tbvehicleinfo" },
                "Const", "VehicleConst", new ConstConverter("VehicleConst", "id"), "cs"
            ));
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "resource_tbcurrency" },
                "Const", "CurrencyConst", new ConstConverter("CurrencyConst", "ID"), "cs"
            ));
            SheetConverter.AddSheetConverter(new ConvertSheetInfo(
                new List<string>() { "reportsystemabnormal" },
                "Const", "AbnormalReportConst", new ConstConverter("AbnormalReportConst", "id"), "cs"
            ));
        }
    }

    internal class ConstConverter : SheetConvertToCsharp
    {
        private readonly string outputClassName;
        private string keyName;
        public ConstConverter(string outputClassName, string keyName)
        {
            this.outputClassName = outputClassName;
            this.keyName = keyName;
        }

        public override ScriptObject ConvertCode(ConvertSheetInfo info)
        {
            var itemDict = IdKeyData.GetData(keyName, info.GetInputInfo(0));
            // [ALB] 生成除LanguageConst的常量类
            HashSet<string> constNames = new HashSet<string>(itemDict.Count);
            List<ScriptObject> constList = new();
            foreach (var (id, data) in itemDict)
            {
                if (data.TryGetValue("constName", out var constName))
                {
                    var constNameString = constName.ToString().Trim();
                    if (constNameString.Length == 0)
                        continue;

                    if (!constNames.Add(constNameString))
                    {
                        var input = info.InputNames[0];
                        if (FileRecordCacheManager.Ins.TryGetTableByOutputFile(input, out var table))
                            throw new Exception($"表格'{table.InputFiles[0]}'的constName'{constNameString}'重复");
                        else
                            throw new Exception($"constName'{constNameString}'重复，请通过'{input}'在'__tables__.xlsx'查询所在具体表格");
                    }

                    constList.Add(new ScriptObject()
                    {
                        ["param_type"] = "const int",
                        ["key"] = constNameString.ToString(),
                        ["value"] = id,
                    });
                }
            }
            return new ScriptObject()
            {
                ["classname"] = outputClassName,
                ["converted_table"] = constList,
            };
        }
    }

    internal class LanguageConstConverter : SheetConvertToCsharp
    {
        public override ScriptObject ConvertCode(ConvertSheetInfo info)
        {
            var itemDict = IdKeyData.GetData(info.GetInputInfo(0));
            // [ALB] 生成常量类LanguageConst
            HashSet<string> constNames = new HashSet<string>(itemDict.Count);
            List<ScriptObject> constList = new();
            foreach (var (id, data) in itemDict)
            {
                if (!data.TryGetValue("constName", out var constName)) continue;
                if (!data.TryGetValue("text", out var text)) continue;
                if (!(text as JObject).TryGetValue("index", out var constValue)) continue;
                if (!(text as JObject).TryGetValue("text", out var textValue)) continue;

                var constNameString = constName.ToString().Trim();
                var comment = textValue.ToString().ReplaceLineEndings(string.Empty); // 删除换行符
                if (constNameString.Length == 0)
                    continue;

                if (!constNames.Add(constNameString))
                {
                    var input = info.InputNames[0];
                    if (FileRecordCacheManager.Ins.TryGetTableByOutputFile(input, out var table))
                        throw new Exception($"表格'{table.InputFiles[0]}'的constName'{constNameString}'重复");
                    else
                        throw new Exception($"constName'{constNameString}'重复，请通过'{input}'在'__tables__.xlsx'查询所在具体表格");
                }

                constList.Add(new ScriptObject()
                {
                    ["param_type"] = "const int",
                    ["key"] = constNameString.ToString(),
                    ["value"] = constValue.ToString(),
                    ["comment"] = comment,
                });
            }
            return new ScriptObject()
            {
                ["classname"] = "LanguageConst",
                ["converted_table"] = constList,
            };
        }
    }
}
