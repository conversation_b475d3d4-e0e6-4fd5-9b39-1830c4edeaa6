using Newtonsoft.Json.Linq;
using SocDataConverter.Table.Converter;

namespace SocDataConverter.Table.PostFill
{
    internal class ConstructionPostFill : JsonPostFill
    {
        public ConstructionPostFill() : base(new List<string>() { "constraction_tbbuildingcore", "program_part_config" }, "constraction_tbbuildingcore")
        {
        }

        public override JObject Convert()
        {
            var buildingCoreInfo = GetInputInfo(0);
            var programConfigInfo = GetInputInfo(1);

            Dictionary<long, JToken> programConfig = new();
            foreach (var json in JToken.FromObject(programConfigInfo.InputJson))
            {
                var templateId = long.Parse(json["partId"].ToString());
                programConfig[templateId] = json;
            }
            var result = new JObject();
            var data = new JArray();

            foreach (var json in JArray.FromObject(buildingCoreInfo.InputJson))
            {
                var templateId = long.Parse(json["partId"].ToString());
                if (programConfig.TryGetValue(templateId, out var pc))
                {
                    json["ConstructionClientModule"] = pc["ConstructionClientModule"];
                    json["ConstructionServerModule"] = pc["ConstructionServerModule"];
                    json["ConstructionSimulatorModule"] = pc["ConstructionSimulatorModule"];
                    json["collectionType"] = pc["collectionType"];
                }
                data.Add(json);
            }

            result.Add("hash", ConvertSheetInfo.GetInputHash("constraction_tbbuildingcore"));
            result.Add("data", data);
            return result;
        }
    }
}
