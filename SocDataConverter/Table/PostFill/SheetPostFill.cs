using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table.Intermedia;
using SocDataConverter.Table.Converter;

namespace SocDataConverter.Table.PostFill
{
    internal abstract class JsonPostFill
    {
        public readonly List<string> InputNames;
        public readonly string OutputName;
        public JArray[] InputJsonList;

        public JsonPostFill(List<string> inputNames, string outputName)
        {
            InputNames = inputNames;
            OutputName = outputName;
            ReadJsonData();
        }

        protected void ReadJsonData()
        {
            InputJsonList = new JArray[InputNames.Count];
            for (var i = 0; i < InputNames.Count; i++)
            {
                InputJsonList[i] = ConvertSheetInfo.getJsonFromCache(Path.Combine(ConvertSheetInfo.lubanJsonPath, $"{InputNames[i]}.json"), InputNames[i]);
            }
        }

        public InputInfo GetInputInfo(int seq)
        {
            return new InputInfo()
            {
                Name = InputNames[seq],
                InputJson = InputJsonList[seq]
            };
        }

        public void Execute(string jsonOutPath)
        {
            Console.WriteLine($"Execute {GetType().Name} for {jsonOutPath} {OutputName}.json");
            Util.WriteTextToPathWithCRLF(Path.Combine(jsonOutPath, $"{OutputName}.json"), Convert().ToString());
        }

        public abstract JObject Convert();
    }

    internal class SheetPostFill
    {
        internal static List<JsonPostFill> AllPostFills = new();

        public static void AddSheetPostFill(JsonPostFill postFill)
        {
            AllPostFills.Add(postFill);
        }
    }
}
