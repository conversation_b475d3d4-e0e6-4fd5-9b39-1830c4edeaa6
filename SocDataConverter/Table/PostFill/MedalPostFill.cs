using Newtonsoft.Json.Linq;
using SocDataConverter.Table.Converter;

namespace SocDataConverter.Table.PostFill;

/// <summary>
/// 勋章数据后处理类
/// 功能：按照 组队上限 -> 玩法模式 双键进行分组导出
/// 初始化勋章任务容器时加载 勋章-组队上限-玩法模式 对应的Json
/// Load后的结构为 任务ID -> (勋章ID, 风格ID, 勋章等级, 勋章积分)
/// </summary>
internal class MedalPostFill : JsonPostFill
{
    public MedalPostFill() : base(
        new List<string>() { "play_tbmedal" },
        "play_tbmedal_reconstruct"
    )
    {
    }

    public override JObject Convert()
    {
        var medalInput = GetInputInfo(0);
        var medalData = medalInput.InputJson;

        // 按 组队上限 -> 玩法模式 双键分组的结果结构
        // 结构: { 组队上限: { 玩法模式: { 任务ID: { medalID, styleID, level, points } } } }
        var groupedResult = new JObject();

        foreach (JObject medal in medalData)
        {
            var medalID = medal["medalID"]?.Value<int>() ?? 0;
            var styleID = medal["styleID"]?.Value<int>() ?? 0;
            var level = medal["level"]?.Value<int>() ?? 0;

            // 处理 task 字段: 组队上限(人数)->任务ID 的映射
            var tasksArray = medal["task"] as JArray;
            if (tasksArray != null)
            {
                foreach (JArray taskEntry in tasksArray)
                {
                    if (taskEntry.Count >= 2)
                    {
                        var teamSizeLimit = taskEntry[0].Value<int>();
                        var taskID = taskEntry[1].Value<long>();

                        // 处理 styleRankPoint 字段: 玩法模式->风格段位积分 的映射
                        var styleRankPointsArray = medal["styleRankPoints"] as JArray;
                        if (styleRankPointsArray != null)
                        {
                            foreach (JArray pointEntry in styleRankPointsArray)
                            {
                                if (pointEntry.Count >= 2)
                                {
                                    var gameMode = pointEntry[0].Value<int>();
                                    var points = pointEntry[1].Value<int>();

                                    // 确保层级结构存在
                                    if (groupedResult[teamSizeLimit.ToString()] == null)
                                    {
                                        groupedResult[teamSizeLimit.ToString()] = new JObject();
                                    }

                                    var teamSizeGroup = groupedResult[teamSizeLimit.ToString()] as JObject;
                                    if (teamSizeGroup![gameMode.ToString()] == null)
                                    {
                                        teamSizeGroup[gameMode.ToString()] = new JObject();
                                    }

                                    var gameModeGroup = teamSizeGroup[gameMode.ToString()] as JObject;

                                    // 添加任务ID对应的勋章信息
                                    gameModeGroup![taskID.ToString()] = new JObject
                                    {
                                        ["medalID"] = medalID,
                                        ["styleID"] = styleID,
                                        ["level"] = level,
                                        ["points"] = points
                                    };
                                }
                            }
                        }
                    }
                }
            }
        }

        // 构造标准输出格式
        var result = new JObject();
        result.Add("hash", ConvertSheetInfo.GetInputHash("play_tbmedal"));
        result.Add("data", groupedResult);

        return result;
    }
}