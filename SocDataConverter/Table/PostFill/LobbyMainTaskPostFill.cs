using DocumentFormat.OpenXml.EMMA;
using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table.Intermedia;
using SocDataConverter.Table.Checker;
using SocDataConverter.Table.Converter;
using System.Collections;

namespace SocDataConverter.Table.PostFill
{
    internal class TargetTask
    {
        public int Id;
        public int MainTaskId;
        public int EventId;
        public List<long> EventParams;
        public List<int> SubTask;

        public JToken? TaskJsonData;
        public JToken? taskPhaseDescribe;

        public TargetTask(JToken? taskJsonData = null)
        {
            TaskJsonData = taskJsonData;
            Id = 0;
            MainTaskId = 0;
            EventId = 0;
            EventParams = new List<long>();
            SubTask = new List<int>();
            taskPhaseDescribe = null;
        }

        public TargetTask(int id, int mainTaskId, int eventId, List<long> eventParams, JToken taskPhaseDescribe,
            List<int> subTask, JToken taskJsonData)
        {
            this.Id = id;
            this.MainTaskId = mainTaskId;
            this.EventId = eventId;
            this.EventParams = eventParams ?? throw new ArgumentNullException(nameof(eventParams), "事件参数列表不能为空");
            this.SubTask = subTask ?? throw new ArgumentNullException(nameof(subTask), "子任务列表不能为空");
            TaskJsonData = taskJsonData ?? throw new ArgumentNullException(nameof(taskJsonData), "任务JSON数据不能为空");
            this.taskPhaseDescribe = taskPhaseDescribe;
        }
    }

    internal class LobbyMainTaskPostFill : JsonPostFill
    {
        public const int MAX_LOBBY_MAIN_TASK_EVENT_COUNT = 5;

        public LobbyMainTaskPostFill() : base(new List<string>() { "play_tbquestphase", "task_tbtaskdata", "task_tbtaskgroupdata" }, "play_tbquestphase")
        {
        }

        public override JObject Convert()
        {
            var questPhaseInfo = GetInputInfo(0);
            var taskDataInfo = GetInputInfo(1);
            var taskGroupDic = IdKeyData.GetData(GetInputInfo(2));

            if (taskDataInfo.InputJson == null)
            {
                throw new InvalidOperationException("taskData的InputJson不能为空");
            }

            // 遍历taskDataInfo，忽略isDSTask字段为false的项，取id和targetEventsList字段创建TargetTask数组
            var targetTasks = new Dictionary<int, TargetTask>();

            var taskJsonArray = JToken.FromObject(taskDataInfo.InputJson);
            if (taskJsonArray == null)
            {
                throw new InvalidOperationException("无法将taskData转换为JToken");
            }

            foreach (var json in taskJsonArray)
            {
                if (json == null)
                {
                    throw new InvalidOperationException("任务JSON数据项不能为空");
                }

                var isDsTaskToken = json["isDSTask"] ?? throw new InvalidOperationException("任务数据缺少isDSTask字段");
                if (isDsTaskToken.ToObject<bool>())
                {
                    var mainTask = new TargetTask(json);

                    var idToken = json["id"] ?? throw new InvalidOperationException("任务数据缺少id字段");

                    mainTask.Id = idToken.ToObject<int>();

                    var targetEventsListToken = json["targetEventsList"];
                    var targetEventsList = targetEventsListToken?.ToObject<JArray>() ??
                                           throw new InvalidOperationException("任务数据缺少targetEventsList字段");
                    var firstEvent = targetEventsList[0];
                    var eventIdToken = firstEvent["id"] ??
                                       throw new InvalidOperationException($"任务ID为{mainTask.Id}的第一个事件缺少id字段");

                    mainTask.EventId = eventIdToken.ToObject<int>();

                    var eventParamsToken = firstEvent["params"];
                    mainTask.EventParams = eventParamsToken?.ToObject<List<long>>() ?? new List<long>();

                    mainTask.taskPhaseDescribe = json["taskDescribe1"];
                    mainTask.SubTask = new List<int>();

                    for (int i = 1; i < targetEventsList.Count; i++)
                    {
                        if (i >= MAX_LOBBY_MAIN_TASK_EVENT_COUNT)
                        {
                            throw new NotSupportedException(
                                $"任务ID为{mainTask.Id}的大厅任务事件列表不允许超过{MAX_LOBBY_MAIN_TASK_EVENT_COUNT}个");
                        }

                        var targetEvent = targetEventsList[i];
                        var subEventIdToken = targetEvent["id"] ??
                                              throw new InvalidOperationException($"任务ID为{mainTask.Id}的第{i + 1}个事件缺少id字段");
                        var eventId = subEventIdToken.ToObject<int>();

                        var eventParams = targetEvent["params"]?.ToObject<List<long>>() ?? new List<long>();

                        var task = new TargetTask(
                            id: mainTask.Id + i,
                            mainTaskId: mainTask.Id,
                            eventId: eventId,
                            eventParams: eventParams,
                            taskPhaseDescribe: json[$"taskDescribe{i + 1}"] ??
                                               throw new InvalidOperationException(
                                                   $"任务ID为{mainTask.Id}的第{i + 1}个事件缺少taskDescribe{i + 1}字段"),
                            subTask: new List<int>(),
                            json
                        );
                        mainTask.SubTask.Add(task.Id);
                        targetTasks.Add(task.Id, task);
                    }

                    targetTasks.Add(mainTask.Id, mainTask);
                }
            }

            // 遍历targetTasks创建play_tbquestphase表的json行数据
            var jsonData = questPhaseInfo.InputJson;
            if (jsonData == null)
            {
                throw new InvalidOperationException("questPhase的InputJson不能为空");
            }

            foreach (var targetTask in targetTasks.Values)
            {
                if (targetTask == null)
                {
                    throw new InvalidOperationException("目标任务不能为空");
                }

                var questPhase = new JObject();
                questPhase["id"] = targetTask.Id;
                questPhase["taskPhaseEndCondition"] = targetTask.EventId;
                questPhase["endConditionParameter"] = JArray.FromObject(targetTask.EventParams);
                questPhase["subTasks"] = JArray.FromObject(targetTask.SubTask);
                questPhase["type"] = JToken.FromObject(MainTaskType.LobbyMainTask);
                // 策划要求填充的表给客户端用
                questPhase["taskPhaseDescribe"] = targetTask.taskPhaseDescribe;

                // 为可能为null的TaskJsonData字段添加检查
                if (targetTask.TaskJsonData == null)
                {
                    throw new InvalidOperationException($"任务ID为{targetTask.Id}的TaskJsonData不能为空");
                }

                questPhase["taskLongDescribe"] = targetTask.TaskJsonData["taskLongDescribe"] ?? string.Empty;
                questPhase["taskType"] = targetTask.TaskJsonData["taskType"] ?? 0;
                questPhase["searchIds"] = targetTask.TaskJsonData["searchIds"] ?? new JArray();
                questPhase["markerType"] = targetTask.TaskJsonData["markerType"] ?? 0;
                questPhase["goalDisplay"] = targetTask.TaskJsonData["goalDisplay"] ?? string.Empty;
                questPhase["guideId"] = targetTask.TaskJsonData["guideId"] ?? 0;
                questPhase["guideItem"] = targetTask.TaskJsonData["guideItem"] ?? 0;
                questPhase["guideBlueprint"] = targetTask.TaskJsonData["guideBlueprint"] ?? string.Empty;
                questPhase["resetCondition"] = targetTask.TaskJsonData["resetCondition"] ?? 0;
                questPhase["resetConditionParameter"] = targetTask.TaskJsonData["resetConditionParameter"] ?? string.Empty;
                questPhase["isteam"] = targetTask.TaskJsonData["isteam"];
                questPhase["taskId"] = GetTaskGroupId(targetTask.Id);
                jsonData.Add(questPhase);
            }

            return new JObject
            {
                { "hash", ConvertSheetInfo.GetInputHash("play_tbquestphase") },
                { "data", jsonData }
            };

            int GetTaskGroupId(int taskId)
            {
                foreach (var (id, jsonData) in taskGroupDic)
                {
                    var taskList = IdKeyData.GetIntList(jsonData, "tasklist");
                    if (null != taskList && taskList.Contains(taskId))
                    {
                        return id;
                    }
                }

                return 0;
            }
        }
    }
}