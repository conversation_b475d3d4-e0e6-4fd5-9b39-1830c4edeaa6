using ExcelDataReader;
using System.Text;
using Luban.Job.Cfg.Cache;
using Luban.Job.Cfg.Defs;
using SocDataConverter.Table.Converter;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace SocDataConverter.Table
{
    public class ScatterTableChecker
    {
        private static readonly Dictionary<string, List<string>> _tableKeyMap = new();

        private static readonly HashSet<string> _dontScatterTable = new();

        public static List<string> GetKeyFromTableName(string tableName)
        {
            return _tableKeyMap.GetValueOrDefault(tableName);
        }

        public void Init()
        {
            var tableNames = new HashSet<string>();
            // 从 ConvertSheetInfo 中获取所有已转换的表格并去重
            SheetConverter.AllConverts.ForEach(info => tableNames.UnionWith(info.InputNames));
            foreach (var tableName in tableNames)
            {
                if (FileRecordCacheManager.Ins.TryGetTableByOutputFile(tableName, out var table) && !table.IsOneValueTable)
                {
                    _tableKeyMap.Add(tableName, ParseTableIndex(table));
                }
            }

            InitDontScatterTable();
        }

        // 不允许覆盖表修改的表
        private void InitDontScatterTable()
        {
            _dontScatterTable.UnionWith(BizId2SystemTypeConverter.Name2SystemType.Keys);
            // _dontScatterTable.Add("itemreward_tbitemdrop");
        }

        private List<string> ParseTableIndex(DefTable table)
        {
            var keys = new List<string>();

            if (table.IsUnionIndex)
            {
                keys.AddRange(table.IndexList.Select(idx => idx.IndexField.Name));
            }
            else
            {
                keys.Add(table.Index);
            }

            return keys;
        }

        public void Check(string lubanJsonPath)
        {
            Init();
            // 1. 加载所有 lubanJsonPath 下的 JSON，按去掉下划线的表名组织数据
            var originalTables = LoadOriginalTables(lubanJsonPath);

            // 2. 遍历所有覆盖表目录
            var scatterDirs = Directory.GetDirectories(lubanJsonPath, "playid_*");

            foreach (var scatterDir in scatterDirs)
            {
                var dirName = Path.GetFileName(scatterDir);
                Console.WriteLine($"\n=== Checking scatter directory: {dirName} ===");

                CheckScatterDirectory(scatterDir, originalTables, dirName);
            }
        }

        private Dictionary<string, JArray> LoadOriginalTables(string serverJsonDirPath)
        {
            var originalTables = new Dictionary<string, JArray>();

            // 加载所有 JSON 文件
            var jsonFiles = Directory.GetFiles(serverJsonDirPath, "*.json", SearchOption.TopDirectoryOnly);

            foreach (var jsonFile in jsonFiles)
            {
                try
                {
                    var fileName = Path.GetFileNameWithoutExtension(jsonFile);
                    var content = File.ReadAllText(jsonFile);
                    var jsonObj = JObject.Parse(content);

                    if (jsonObj["data"] is JArray dataArray)
                    {
                        // 去掉表名中的下划线进行标准化
                        var normalizedTableName = fileName.Replace("_", "");
                        originalTables[normalizedTableName] = dataArray;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error loading {jsonFile}: {ex.Message}");
                }
            }

            Console.WriteLine($"Loaded {originalTables.Count} original tables");
            return originalTables;
        }

        private void CheckScatterDirectory(string scatterDir, Dictionary<string, JArray> originalTables, string dirName)
        {
            var scatterFiles = Directory.GetFiles(scatterDir, "*.json");

            foreach (var scatterFile in scatterFiles)
            {
                CheckScatterTable(scatterFile, originalTables, dirName);
            }
        }

        private void CheckScatterTable(string scatterFile, Dictionary<string, JArray> originalTables, string dirName)
        {
            var fileName = Path.GetFileNameWithoutExtension(scatterFile);

            // 从文件名中提取表名（移除 _playid_X 后缀）
            var tableName = ExtractTableNameFromScatterFile(fileName, dirName);

            // 2.2 如果表名在 _dontScatterTable 中则抛出异常
            if (_dontScatterTable.Contains(tableName))
            {
                throw new Exception($"表 '{tableName}' 不允许被覆盖, 但是在 {dirName} 中覆盖了");
            }

            // 2.3 根据表名获取主键
            if (!_tableKeyMap.TryGetValue(tableName, out var keyFields))
            {
                // 说明是客户端表 或者 常量表
                return;
            }
            var isConstantTable = keyFields == null || keyFields.Count == 0;

            // 加载覆盖表数据
            var content = File.ReadAllText(scatterFile);
            var scatterObj = JObject.Parse(content);

            if (!(scatterObj["data"] is JArray scatterArray))
            {
                Console.WriteLine($"Warning: No data array found in {scatterFile}");
                return;
            }

            // 获取原始表数据
            var normalizedTableName = tableName.Replace("_", "");
            if (!originalTables.TryGetValue(normalizedTableName, out var originalArray))
            {
                Console.WriteLine($"Warning: Original table '{normalizedTableName}' not found for scatter table '{tableName}'");
                return;
            }

            // 比较覆盖表和原始表的数据
            if (isConstantTable)
            {
                CompareConstantTableData(tableName, originalArray, scatterArray, dirName);
            }
            else
            {
                CompareTableData(tableName, keyFields, originalArray, scatterArray, dirName);
            }
        }

        private string ExtractTableNameFromScatterFile(string fileName, string dirName)
        {
            // 文件名格式：{表名}_{dirName}.json
            // 例如：global_tbglobalconfig_playid_1.json
            var suffix = $"_{dirName}";
            if (fileName.EndsWith(suffix))
            {
                return fileName.Substring(0, fileName.Length - suffix.Length);
            }

            // 如果没有找到预期的后缀，尝试其他方式
            var lastUnderscoreIndex = fileName.LastIndexOf('_');
            if (lastUnderscoreIndex > 0)
            {
                return fileName.Substring(0, lastUnderscoreIndex);
            }

            return fileName;
        }

        private void CompareConstantTableData(string tableName, JArray originalArray, JArray scatterArray, string dirName)
        {
            Console.WriteLine($"\n--- Constant Table: {tableName} ---");

            if (originalArray.Count == 0 || scatterArray.Count == 0)
            {
                Console.WriteLine($"  Warning: Empty data array");
                return;
            }

            // 常量表通常只有一个记录
            var originalRecord = originalArray[0] as JObject;
            var scatterRecord = scatterArray[0] as JObject;

            if (originalRecord == null || scatterRecord == null)
            {
                Console.WriteLine($"  Warning: Invalid record format");
                return;
            }

            var diffCount = 0;

            // 遍历覆盖表中的所有属性
            foreach (var property in scatterRecord.Properties())
            {
                var fieldName = property.Name;

                var scatterValue = property.Value;
                var originalValue = originalRecord[fieldName];

                // 比较值是否相同
                if (!JToken.DeepEquals(originalValue, scatterValue))
                {
                    diffCount++;
                    Console.WriteLine(
                        $"  {fieldName}: {originalValue?.ToString() ?? "null"} -> {scatterValue?.ToString() ?? "null"}");
                }
            }

            if (diffCount == 0)
            {
                Console.WriteLine($"  No differences found");
            }
            else
            {
                Console.WriteLine($"  Total differences: {diffCount}");
            }
        }

        private void CompareTableData(string tableName, List<string> keyFields, JArray originalArray, JArray scatterArray,
            string dirName)
        {
            // 构建原始数据的索引
            var originalIndex = BuildTableIndex(originalArray, keyFields);

            Console.WriteLine($"\n--- Table: {tableName} ---");
            var diffCount = 0;

            foreach (JObject scatterRecord in scatterArray)
            {
                // 获取主键值
                var keyValues = GetKeyValues(scatterRecord, keyFields);
                var keyString = string.Join(" + ", keyValues);

                // 在原始数据中查找对应记录
                if (originalIndex.TryGetValue(keyString, out var originalRecord))
                {
                    // 比较记录的所有字段
                    var differences = CompareRecords(originalRecord, scatterRecord);

                    if (differences.Count > 0)
                    {
                        diffCount++;
                        Console.WriteLine($"  Key: {keyString}");

                        foreach (var diff in differences)
                        {
                            Console.WriteLine($"    {diff.Field}: {diff.OriginalValue} -> {diff.OverrideValue}");
                        }
                    }
                }
                else
                {
                    Console.WriteLine($"  Key: {keyString} (NEW RECORD in {dirName})");
                    diffCount++;
                }
            }

            if (diffCount == 0)
            {
                Console.WriteLine($"  No differences found");
            }
            else
            {
                Console.WriteLine($"  Total differences: {diffCount}");
            }
        }

        private Dictionary<string, JObject> BuildTableIndex(JArray dataArray, List<string> keyFields)
        {
            var index = new Dictionary<string, JObject>();

            foreach (JObject record in dataArray)
            {
                var keyValues = GetKeyValues(record, keyFields);
                var keyString = string.Join(" + ", keyValues);
                index[keyString] = record;
            }

            return index;
        }

        private List<string> GetKeyValues(JObject record, List<string> keyFields)
        {
            var keyValues = new List<string>();

            foreach (var keyField in keyFields)
            {
                var value = record[keyField]?.ToString() ?? "";
                keyValues.Add(value);
            }

            return keyValues;
        }

        private List<FieldDifference> CompareRecords(JObject originalRecord, JObject scatterRecord)
        {
            var differences = new List<FieldDifference>();

            foreach (var property in scatterRecord.Properties())
            {
                var fieldName = property.Name;

                var scatterValue = property.Value;
                var originalValue = originalRecord[fieldName];

                // 比较值是否相同
                if (!JToken.DeepEquals(originalValue, scatterValue))
                {
                    differences.Add(new FieldDifference
                    {
                        Field = fieldName,
                        OriginalValue = originalValue?.ToString() ?? "null",
                        OverrideValue = scatterValue?.ToString() ?? "null"
                    });
                }
            }

            return differences;
        }

        private class FieldDifference
        {
            public string Field { get; set; }
            public string OriginalValue { get; set; }
            public string OverrideValue { get; set; }
        }
    }
}