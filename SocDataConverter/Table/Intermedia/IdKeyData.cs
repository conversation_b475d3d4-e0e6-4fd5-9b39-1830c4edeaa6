using Newtonsoft.Json.Linq;

namespace Soc.Common.CodeParser.Table.Intermedia
{
    public struct InputInfo
    {
        public string Name;
        public JArray InputJson;
    }

    internal static class IdKeyData
    {
        private static Dictionary<string, Dictionary<int, JObject>> data = new();
        private static Dictionary<string, Dictionary<long, JObject>> dataLong = new();

        private static Dictionary<int, JObject> generateNew(JArray inputJson, string name, string keyName)
        {
            var result = new Dictionary<int, JObject>(inputJson.Count);
            foreach (var blueprintItemJson in inputJson)
            {
                var blueprintItem = blueprintItemJson as JObject;
                var id = GetIntVal(blueprintItem, keyName);
                result[id] = blueprintItem;
            }
            data[name] = result;
            return result;
        }

        private static IDictionary<int, JObject> GetData(string keyName, JArray inputJson, string name) => data.TryGetValue(name, out var result) ? result : generateNew(inputJson, name, keyName);

        public static IDictionary<int, JObject> GetData(InputInfo info) => GetData("id", info.InputJson, info.Name);

        public static IDictionary<int, JObject> GetData(string keyName, InputInfo info) => GetData(keyName, info.InputJson, info.Name);

        private static Dictionary<long, JObject> generateLong(JArray inputJson, string name, string keyName)
        {
            var result = new Dictionary<long, JObject>(inputJson.Count);
            foreach (var nodeJson in inputJson)
            {
                var jObj = nodeJson as JObject;
                var id = GetLongVal(jObj, keyName);
                result[id] = jObj;
            }
            dataLong[name] = result;
            return result;
        }

        private static IDictionary<long, JObject> GetDataLong(string keyName, JArray inputJson, string name)
        {
            if (dataLong.TryGetValue(name, out var result)) return result;
            return generateLong(inputJson, name, keyName);
        }

        public static IDictionary<long, JObject> GetDataLong(InputInfo info) => GetDataLong("id", info.InputJson, info.Name);

        public static int? TryGetIntVal(JObject obj, string key)
        {
            if (obj.TryGetValue(key, out var val))
            {
                if (val != null) return int.Parse(val.ToString());
            }
            return null;
        }

        public static int GetIntVal(JObject obj, string key)
        {
            if (obj.TryGetValue(key, out var output) && output != null)
            {
                return int.Parse(output.ToString());
            }
            else
            {
                return -1;
            }
        }

        public static bool GetBoolVal(JObject obj, string key) => bool.Parse(obj[key].ToString());

        public static long GetLongVal(JObject obj, string key) => long.Parse(obj[key].ToString());

        public static List<int> GetIntList(JObject obj, string key) => obj[key].Select(obj => int.Parse(obj.ToString())).ToList();

        public static List<float> GetFloatList(JObject obj, string key) => obj[key].Select(obj => float.Parse(obj.ToString())).ToList();

        public static List<long> GetLongList(JObject obj, string key) => obj[key].Select(obj => long.Parse(obj.ToString())).ToList();

        public static List<List<long>> GetLongLongList(JObject obj, string key) => obj[key].Select(obj => obj.Select(obj => long.Parse(obj.ToString())).ToList()).ToList();

        public static List<string> GetStringList(JObject obj, string key) => obj[key].Select(obj => obj.ToString()).ToList();

        public static Dictionary<TKey, TValue> GetDict<TKey, TValue>(JObject obj, string key)
        {
            Dictionary<TKey, TValue> dict = new();
            foreach (var item in obj[key].Children<JArray>())
            {
                TKey dicKey = item[0].ToObject<TKey>();
                TValue value = item[1].ToObject<TValue>();
                dict[dicKey] = value;
            }
            return dict;
        }
    }
}
