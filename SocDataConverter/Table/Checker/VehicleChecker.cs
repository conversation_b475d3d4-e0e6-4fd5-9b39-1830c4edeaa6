using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class VehicleChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "vehicle_tbmodularcar";
            yield return "vehicle_tbvehiclemodule";
            yield return "vehicle_tbenginepart";
            yield return "vehicle_tbvehicleinfo";
        }

        public bool Check(SheetChecker info)
        {
            var modularCarDic = IdKeyData.GetData(info.GetInputInfo(0));
            var vechileModuleDic = IdKeyData.GetData(info.GetInputInfo(1));
            var vehiclePartDic = IdKeyData.GetData(info.GetInputInfo(2));
            var vehicleInfoDic = IdKeyData.GetData(info.GetInputInfo(3));

            foreach (var (_, modularCarConfig) in modularCarDic)
            {
                var name = IdKeyData.GetLongVal(modularCarConfig, "name");
                if (!vehicleInfoDic.ContainsKey((int)name))
                {
                    throw new Exception($"载具配置表中不存在底盘id {name}");
                }

                var moduleIds = IdKeyData.GetLongList(modularCarConfig, "ModuleList");
                foreach (var moduleId in moduleIds)
                {
                    if (!vechileModuleDic.ContainsKey((int)moduleId))
                    {
                        throw new Exception($"载具配置表中不存在模块id {moduleId}");
                    }
                }

                var moduleSetList = IdKeyData.GetLongLongList(modularCarConfig, "ModuleSetList");
                if (moduleIds.Count != moduleSetList.Count)
                {
                    throw new Exception($"载具配置表中模块数量{moduleIds.Count}与模块组数量{moduleSetList.Count}不等");
                }

                var engineIndex = IdKeyData.GetLongVal(modularCarConfig, "EngineID");
                var engineId = moduleIds[(int)engineIndex];
                if (!vechileModuleDic.TryGetValue((int)engineId, out var moduleConfig))
                {
                    throw new Exception($"载具配置表中不存在引擎id {engineId}");
                }

                var moduleType = IdKeyData.GetIntList(moduleConfig, "ModuleType");
                // 引擎模块是1 
                if (!moduleType.Contains(1))
                {
                    throw new Exception($"载具配置表中引擎id {engineId} 不是引擎模块");
                }

                var engineModuleList = IdKeyData.GetLongList(modularCarConfig, "EngineModuleList");
                foreach (var engineModuleId in engineModuleList)
                {
                    if (!vehiclePartDic.ContainsKey((int)engineModuleId))
                    {
                        throw new Exception($"载具配置表中不存在零件id {engineModuleId}");
                    }
                }

                var moduleListHp = IdKeyData.GetFloatList(modularCarConfig, "ModuleListHP");
                foreach (var hp in moduleListHp)
                {
                    if (hp < 0 || hp > 1)
                    {
                        throw new Exception($"载具配置表中模块hp {hp} 小于0或者大于0");
                    }
                }

                var engineModuleHp = IdKeyData.GetFloatList(modularCarConfig, "EngineModuleHP");
                foreach (var hp in engineModuleHp)
                {
                    if (hp < 0 || hp > 1)
                    {
                        throw new Exception($"载具配置表中零件hp {hp} 小于0或者大于0");
                    }
                }

                if (engineModuleList.Count != engineModuleHp.Count)
                {
                    throw new Exception($"载具配置表中引擎模块数量{engineModuleList.Count}与引擎模块hp数量{engineModuleHp.Count}不等");
                }
            }
            return true;
        }
    }
}
