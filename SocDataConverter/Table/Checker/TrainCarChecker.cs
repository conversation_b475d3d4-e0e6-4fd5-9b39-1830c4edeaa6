using Bright.Collections;
using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    /// <summary>
    /// 检查火车车厢相关配置的完整性
    /// </summary>
    internal class TrainCarChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "play_tbtraincarproperty";
            yield return "itemreward_tbitemdrop";
            yield return "play_tbtraincarunload";
        }

        /// <summary>
        /// 检查火车车厢配置的完整性
        /// </summary>
        public bool Check(SheetChecker info)
        {
            var trainCarPropertyTable = IdKeyData.GetData(info.GetInputInfo(0));
            var itemDropTable = IdKeyData.GetData(info.GetInputInfo(1));
            var trainCarUnloadTable = IdKeyData.GetData(info.GetInputInfo(2));

            // 收集所有战斗掉落ID及其对应的车厢模板ID
            var (dropIds, dropIdToTrainTemplateIds) = collectDropIdsAndTrainTemplates(trainCarPropertyTable);

            // 收集所有物品ID及其对应的掉落ID
            var (allItemIds, itemIdToDropIds) = CollectItemIdsAndDropIds(dropIds, itemDropTable);

            // 收集所有车厢卸载物品ID
            var allUnloadItemIds = collectUnloadItemIds(trainCarUnloadTable);

            // 验证所有掉落物品是否都在卸载列表中
            validateAllItemsInUnloadList(allItemIds, allUnloadItemIds, itemIdToDropIds, dropIdToTrainTemplateIds);

            return true;
        }

        /// <summary>
        /// 从车厢属性表中收集掉落ID和车厢模板ID的映射关系
        /// </summary>
        private static (List<int> dropIds, Dictionary<int, List<int>> dropIdToTrainTemplateIds)
            collectDropIdsAndTrainTemplates(IDictionary<int, JObject> trainCarPropertyTable)
        {
            var dropIds = new List<int>();
            var dropIdToTrainTemplateIds = new Dictionary<int, List<int>>();

            foreach (var (templateId, trainProperty) in trainCarPropertyTable)
            {
                if (trainProperty.TryGetValue("combatId", out var combatId))
                {
                    var combatIdInt = combatId.ToObject<int>();
                    if (combatIdInt > 0)
                    {
                        dropIds.Add(combatIdInt);
                        dropIdToTrainTemplateIds.GetOrAdd(combatIdInt).Add(templateId);
                    }
                }
            }

            return (dropIds, dropIdToTrainTemplateIds);
        }

        /// <summary>
        /// 从掉落表中收集物品ID和掉落ID的映射关系
        /// </summary>
        private (HashSet<int> allItemIds, Dictionary<int, List<int>> itemIdToDropIds)
            CollectItemIdsAndDropIds(List<int> dropIds, IDictionary<int, JObject> itemDropTable)
        {
            var allItemIds = new HashSet<int>();
            var itemIdToDropIds = new Dictionary<int, List<int>>();

            foreach (var dropId in dropIds)
            {
                validateDropTableEntry(dropId, itemDropTable);

                var dropData = itemDropTable[dropId];
                var rewards = dropData["rewards"] as JArray;
                validateRewardsList(dropId, rewards);

                foreach (var reward in rewards)
                {
                    var rewardDic = reward as JObject;
                    validateDropRewards(dropId, rewardDic);

                    foreach (var dropReward in rewardDic["dropRewards"])
                    {
                        var dropRewardDic = dropReward as JObject;
                        var itemId = dropRewardDic["ItemID"].ToObject<int>();
                        allItemIds.Add(itemId);
                        itemIdToDropIds.GetOrAdd(itemId).Add(dropId);
                    }
                }
            }

            return (allItemIds, itemIdToDropIds);
        }

        /// <summary>
        /// 从车厢卸载表中收集所有物品ID
        /// </summary>
        private static HashSet<int> collectUnloadItemIds(IDictionary<int, JObject> trainCarUnloadTable)
        {
            var allUnloadItemIds = new HashSet<int>();

            foreach (var (_, trainUnload) in trainCarUnloadTable)
            {
                var produceIds = trainUnload["produceID"] as JArray;
                foreach (var produceId in produceIds)
                {
                    allUnloadItemIds.Add(produceId.ToObject<int>());
                }
            }

            return allUnloadItemIds;
        }

        /// <summary>
        /// 验证所有掉落物品是否都在卸载列表中
        /// </summary>
        private static void validateAllItemsInUnloadList(
            HashSet<int> allItemIds,
            HashSet<int> allUnloadItemIds,
            Dictionary<int, List<int>> itemIdToDropIds,
            Dictionary<int, List<int>> dropIdToTrainTemplateIds)
        {
            foreach (var itemId in allItemIds)
            {
                if (!allUnloadItemIds.Contains(itemId))
                {
                    var dropIds = itemIdToDropIds[itemId];
                    var trainTemplateIds = dropIdToTrainTemplateIds[dropIds[0]];

                    throw new Exception(
                        $"车厢卸载配置错误：物品ID {itemId} 未在卸载列表中\n" +
                        $"相关掉落组ID: {string.Join(", ", dropIds)}\n" +
                        $"相关车厢模板ID: {string.Join(", ", trainTemplateIds)}");
                }
            }
        }

        private static void validateDropTableEntry(int dropId, IDictionary<int, JObject> itemDropTable)
        {
            if (!itemDropTable.TryGetValue(dropId, out _))
            {
                throw new Exception($"掉落配置表中不存在掉落ID: {dropId}");
            }
        }

        private static void validateRewardsList(int dropId, JArray rewards)
        {
            if (rewards == null)
            {
                throw new Exception($"掉落配置表中掉落ID {dropId} 的rewards配置为空");
            }
        }

        private static void validateDropRewards(int dropId, JObject rewardDic)
        {
            if (!rewardDic.TryGetValue("dropRewards", out _))
            {
                throw new Exception($"掉落配置表中掉落ID {dropId} 缺少dropRewards配置");
            }
        }
    }
}