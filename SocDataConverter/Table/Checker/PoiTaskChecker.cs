using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class PoiTaskChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "play_tbtreasuretask";
            yield return "play_tbquestphase";
        }

        public bool Check(SheetChecker info)
        {
            var poiTaskConfigDic = IdKeyData.GetData(info.GetInputInfo(0));
            var taskConfigDic = IdKeyData.GetData(info.GetInputInfo(1));

            foreach (var (id, poiTaskConfig) in poiTaskConfigDic)
            {
                if (!taskConfigDic.ContainsKey(id))
                {
                    throw new Exception($"任务配置表中不存在POI任务id {id}");
                }
            }

            return true;
        }
    }
}
