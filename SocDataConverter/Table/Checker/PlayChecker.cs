using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class PlayChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "play_tbstage";
        }

        public bool Check(SheetChecker info)
        {
            var OpCodeDir = Path.Combine(Program.SocResourceCommonDir, "Data", "Server", "Play1");
            var files = Directory.GetFiles(OpCodeDir, "StageType.cs");
            int stageCount = 0;
            foreach (var file in files)
            {
                var lines = File.ReadAllLines(file);
                foreach (var line in lines)
                {
                    if (line.Contains("="))
                    {
                        var temp = line.Split('=')[1].Trim();
                        temp = temp.Substring(0, temp.Length - 1);
                        int value = int.Parse(temp);
                        stageCount = value > stageCount ? value : stageCount;
                    }
                }
            }

            var playDic = IdKeyData.GetData("battleId", info.GetInputInfo(0));
            foreach (var (_, data) in playDic)
            {
                var battleId = IdKeyData.GetIntVal(data, "battleId");
                var stages = data["stage"] as JArray;

                if (stages.Count != stageCount)
                {
                    throw new Exception($"战场Id：{battleId}, stage数量：{stages.Count} 与定义的枚举数量：{stageCount}不等");
                }
            }

            return true;
        }
    }
}
