using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table;
using Soc.Common.CodeParser.Table.Intermedia;
using SocDataConverter.Table.PostFill;

namespace SocDataConverter.Table.Checker
{
    internal class LobbyMainTaskChecker : ISheetChecker
    {
        private const string TASK_TABLE_NAME = "task_tbtaskdata";
        private const string TASK_CONDITION_TABLE_NAME = "task_tbtaskcondition";
        private const string QUEST_PHASE_TABLE_NAME = "play_tbquestphase";

        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "task_tbtaskdata";
            yield return "play_tbquestphase";
            yield return "play_tbquestcondition";
        }

        public bool Check(SheetChecker info)
        {
            var taskDataDic = IdKeyData.GetData(info.GetInputInfo(0));
            var questPhaseDic = IdKeyData.GetData(info.GetInputInfo(1));
            var taskConditionDic = IdKeyData.GetData(info.GetInputInfo(2));

            var onlyDsTaskDataDic = taskDataDic.Where(task => task.Value["isDSTask"]?.ToObject<bool>() == true)
                                              .ToDictionary(pair => pair.Key, pair => pair.Value);

            CheckIdConflict(onlyDsTaskDataDic, questPhaseDic);
            CheckIdSpan(onlyDsTaskDataDic);
            CheckUnsupportedConfig(onlyDsTaskDataDic);
            CheckEventIdMustInTaskCondition(onlyDsTaskDataDic, taskConditionDic);
            CheckPreTaskMustBeExist(taskDataDic);

            return true;
        }
        private static void CheckPreTaskMustBeExist(IDictionary<int, JObject> taskDataDic)
        {
            foreach (var task in taskDataDic)
            {
                if (task.Value["pretask"] == null || task.Value["pretask"].ToObject<int>() == 0) continue;

                var preTaskId = task.Value["pretask"].ToObject<int>();
                if (!taskDataDic.ContainsKey(preTaskId))
                {
                    throw new Exception($"{TASK_TABLE_NAME}表ID {task.Key} pretask {preTaskId} 必须在{TASK_TABLE_NAME}表中存在");
                }
            }
        }

        private static void CheckEventIdMustInTaskCondition(
            IDictionary<int, JObject> onlyDsTaskDataDic,
            IDictionary<int, JObject> taskConditionDic)
        {
            foreach (var task in onlyDsTaskDataDic)
            {
                if (task.Value["targetEventsList"] == null) continue;

                foreach (var taskEvent in task.Value["targetEventsList"].ToObject<JArray>())
                {
                    var eventId = taskEvent["id"]?.ToObject<int>();
                    if (eventId == null) continue;

                    if (!taskConditionDic.ContainsKey(eventId.Value))
                    {
                        throw new Exception(
                            $"{TASK_TABLE_NAME}表ID {task.Key} targetEventsList.id {eventId} 必须在{TASK_CONDITION_TABLE_NAME}表中存在");
                    }
                }
            }
        }

        private static void CheckUnsupportedConfig(IDictionary<int, JObject> onlyDsTaskDataDic)
        {
            var unsupportedConfig = new List<string>
            {
                "canCountWhenLocked", "canCountWhenPreTaskUnfinished", "inheritglobal", "eventtrigger"
            };

            foreach (var task in onlyDsTaskDataDic)
            {
                foreach (var config in unsupportedConfig)
                {
                    if (task.Value[config] != null && task.Value[config].ToObject<bool>())
                    {
                        throw new Exception($"{TASK_TABLE_NAME}表ID {task.Key} 存在不支持的配置 {config} 必须设置为false");
                    }
                }
            }
        }

        private static void CheckIdSpan(IDictionary<int, JObject> onlyDsTaskDataDic)
        {
            foreach (var task in onlyDsTaskDataDic)
            {
                var taskId = task.Key;
                var idRange = Enumerable.Range(taskId, LobbyMainTaskPostFill.MAX_LOBBY_MAIN_TASK_EVENT_COUNT);
                var existingIds = onlyDsTaskDataDic.Keys.Where(id => idRange.Contains(id)).ToArray();

                // 只存在自身一个ID（正常情况 检查通过）
                if (existingIds.Length == 1) continue;

                var conflictIds = string.Join(", ", existingIds.Skip(1));
                throw new Exception(
                    $"{TASK_TABLE_NAME}表ID {taskId} 缺少{LobbyMainTaskPostFill.MAX_LOBBY_MAIN_TASK_EVENT_COUNT - 1}个空闲ID, 存在ID: {conflictIds}");
            }
        }

        private static void CheckIdConflict(IDictionary<int, JObject> onlyDSTaskDataDic, IDictionary<int, JObject> questPhaseDic)
        {
            var taskIdRanges = onlyDSTaskDataDic
                .SelectMany(pair => Enumerable.Range(pair.Key, LobbyMainTaskPostFill.MAX_LOBBY_MAIN_TASK_EVENT_COUNT))
                .ToHashSet();

            var conflictIds = questPhaseDic.Keys
                .Where(questPhaseId => taskIdRanges.Contains(questPhaseId))
                .ToList();

            if (conflictIds.Any())
            {
                throw new Exception($"ID冲突 {QUEST_PHASE_TABLE_NAME}表ID {string.Join(", ", conflictIds)} 与{TASK_TABLE_NAME}表ID范围重叠");
            }
        }
    }
}