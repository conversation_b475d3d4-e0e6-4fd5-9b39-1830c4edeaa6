using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class SurprisePlayChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "surpriseplay_tbsurpriseplaygroupconfig";
            yield return "surpriseplay_tbsurpriseplayconstconfig";
        }

        public bool Check(SheetChecker info)
        {
            var dict = IdKeyData.GetData(info.GetInputInfo(0));
            var constConfig = IdKeyData.GetData(info.GetInputInfo(1));

            var LaunchTime = IdKeyData.GetIntVal(constConfig[-1], "RocketLaunchMinute");
            var enterInterval = IdKeyData.GetIntVal(constConfig[-1], "EnterNightIntervalMinute");
            var leaveInterval = IdKeyData.GetIntVal(constConfig[-1], "LeaveNightIntervalMinute");

            if (leaveInterval < 0)
            {
                throw new Exception($"解密常量表, leaveInterval:{leaveInterval}, 配置错误了");
            }

            if (LaunchTime < 0)
            {
                throw new Exception($"解密常量表, LaunchTime:{LaunchTime}, 配置错误了");
            }

            if (enterInterval > LaunchTime)
            {
                throw new Exception($"解密常量表, LaunchTime:{LaunchTime} < enterInterval：{enterInterval}");
            }

            if ((LaunchTime - enterInterval) >= (LaunchTime + leaveInterval))
            {
                throw new Exception($"解密常量表, enterInterval:{LaunchTime - enterInterval} >= leaveInterval：{LaunchTime + leaveInterval}");
            }

            var count = 0;
            foreach (var (id, data) in dict)
            {
                var password = IdKeyData.GetIntList(data, "password");
                if (password.Count == 0)
                {
                    throw new Exception(String.Format("解密密码表, id:{0}, password is empty", id));
                }

                if (count == 0)
                {
                    count = password.Count;
                }

                if (count != password.Count)
                {
                    throw new Exception(String.Format("解密密码表, id:{0}, password count:{1}, not equal to:{2}", id, password.Count, count));
                }
            }

            return true;
        }
    }
}
