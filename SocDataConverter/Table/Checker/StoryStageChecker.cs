using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class StoryStageChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "play_tbstorystage";
            yield return "play_tbpoispawnpool";
            yield return "monument_tbboybandspawner";
            yield return "play_tbdeathdropcontainerratio";
            yield return "play_tbdeathdropitemtyperatio";
            yield return "play_tbdeathdropspecialitemratio";
            yield return "spawn_tbstoryspawngroup";
            yield return "play_tbdeathdropparam";
        }

        private void CheckInTable(InputInfo inputInfo, long templateId)
        {
            JToken infos = inputInfo.InputJson;
            string tableName = inputInfo.Name;
            bool find = false;
            foreach (var data in infos)
            {
                JObject singleData = (JObject)data;
                var id = 0L;
                if (tableName == "play_tbdeathdropcontainerratio")
                {
                    id = (long)singleData["ContainerGroupId"];
                }
                else if (tableName == "play_tbdeathdropitemtyperatio")
                {
                    id = (long)singleData["ItemGroupId"];
                }
                else if (tableName == "play_tbdeathdropspecialitemratio")
                {
                    id = (long)singleData["SpecialGroupId"];
                }
                else if (tableName == "spawn_tbstoryspawngroup")
                {
                    id = (long)singleData["groupId"];
                }
                else
                {
                    id = (long)singleData["id"];
                }

                if (id == templateId || templateId == 0)
                {
                    find = true;
                    break;
                }
            }

            if (!find)
            {
                throw new Exception($"168_玩法_剧本总表, 在：{inputInfo.Name} 中没有找到相关联 {templateId}");
            }
        }

        public bool Check(SheetChecker info)
        {
            var inputJson = info.GetInputInfo(0).InputJson;

            foreach (var data in inputJson)
            {
                var jObject = data as JObject;

                var stagePOIPools = IdKeyData.GetIntList(jObject, "stagePOIPools");
                foreach (var poiPoolId in stagePOIPools)
                {
                    CheckInTable(info.GetInputInfo(1), poiPoolId);
                }

                var stageBoybands = IdKeyData.GetIntList(jObject, "stageBoybands");
                foreach (var boybandId in stageBoybands)
                {
                    CheckInTable(info.GetInputInfo(2), boybandId);
                }

                var debrisDropGroupId = IdKeyData.GetIntVal(jObject, "debrisDropGroupId");
                if (debrisDropGroupId > 0)
                {
                    CheckInTable(info.GetInputInfo(3), debrisDropGroupId);
                    CheckInTable(info.GetInputInfo(4), debrisDropGroupId);
                    CheckInTable(info.GetInputInfo(5), debrisDropGroupId);
                }

                var DeathDroupGroupId = IdKeyData.GetIntVal(jObject, "DeathDroupGroupId");
                if (DeathDroupGroupId > 0)
                {
                    CheckInTable(info.GetInputInfo(3), DeathDroupGroupId);
                    CheckInTable(info.GetInputInfo(4), DeathDroupGroupId);
                    CheckInTable(info.GetInputInfo(5), DeathDroupGroupId);
                }

                var SpawnRuleGroupId = IdKeyData.GetIntList(jObject, "SpawnRuleGroupId");
                foreach (var ruleGroupId in SpawnRuleGroupId)
                {
                    CheckInTable(info.GetInputInfo(6), ruleGroupId);
                }
            }

            inputJson = info.GetInputInfo(7).InputJson;
            foreach (var data in inputJson)
            {
                var jObject = data as JObject;
                var DefaultDroupGroupId = IdKeyData.GetIntVal(jObject, "DefaultDropGroupID");
                CheckInTable(info.GetInputInfo(3), DefaultDroupGroupId);
                CheckInTable(info.GetInputInfo(4), DefaultDroupGroupId);
                CheckInTable(info.GetInputInfo(5), DefaultDroupGroupId);
            }

            return true;
        }
    }
}
