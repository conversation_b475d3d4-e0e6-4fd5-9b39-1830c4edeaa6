namespace SocDataConverter.Table.Checker
{
    internal class WaterContainerChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "dataitem_tbwatercontainer";
        }

        public bool Check(SheetChecker info)
        {
            var infos = info.GetInputInfo(0).InputJson;
            foreach (var singleData in infos)
            {
                var itemID = long.Parse(singleData["itemID"].ToString());
                var pureWaterMaxStack = int.Parse(singleData["pureWaterMaxStack"].ToString());
                var saltWaterMaxStack = int.Parse(singleData["saltWaterMaxStack"].ToString());
                var anyWaterMaxStack = int.Parse(singleData["anyWaterMaxStack"].ToString());


                if (pureWaterMaxStack == 0 && saltWaterMaxStack == 0 && anyWaterMaxStack == 0)
                {
                    throw new Exception($"系统_水利容器表 容量配置错误, itemID:{itemID}, pureWaterMaxStack:{pureWaterMaxStack}, saltWaterMaxStack:{saltWaterMaxStack}, anyWaterMaxStack:{anyWaterMaxStack} ");
                }
            }

            return true;
        }
    }
}
