using WizardGames.Soc.Common.Const;

namespace SocDataConverter.Table.Checker
{
    internal class ItemChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "dataitem_tbitemconfig";
        }

        internal static readonly Dictionary<int, HashSet<int>> ItemStackType = new()
        {
            {ItemType.Build, new(){ BuildingSubType.CoreBuildings } },
            {ItemType.Resources, new(){ ResourceSubType.Animal, ResourceSubType.Ore, ResourceSubType.Plant, ResourceSubType.IndustryWood, ResourceSubType.IndustryProducts}},
            {ItemType.Weapon, new(){ WeaponSubType.Throwing} },
            {ItemType.Ammo, new(){}  },
            {ItemType.Electric,  new(){ ElectricSubType.Power, ElectricSubType.Circuit, ElectricSubType.Appliances}},
            {ItemType.Misc,  new(){ MiscSubType.PoiTaskItem}},
            {ItemType.Expendable,  new(){ ExpendableSubType.Meat, ExpendableSubType.Fruit, ExpendableSubType.Tea, ExpendableSubType.Snack, ExpendableSubType.Medicine, ExpendableSubType.Seed, ExpendableSubType.TreasureMap}},
            {ItemType.Gameplay,  new(){ GameplaySubType.Tool, GameplaySubType.Exhibition}},
            {ItemType.Item,  new(){ ItemSubType.Functional, ItemSubType.Decorative}},
        };

        public static bool InStackWhiteList(int type, int subType)
        {
            if (type == ItemType.Ammo || type == ItemType.Bundle) return true;

            if (!ItemStackType.TryGetValue(type, out var subTypeSet)) return false;

            return subTypeSet.Contains(subType);
        }

        public bool Check(SheetChecker info)
        {
            var infos = info.GetInputInfo(0).InputJson;
            foreach (var singleData in infos)
            {
                var id = long.Parse(singleData["id"].ToString());
                var manufacturing = int.Parse(singleData["manufacturing"].ToString());
                var secondaryClassification = int.Parse(singleData["SecondaryClassification"].ToString());
                var stack = int.Parse(singleData["stack"].ToString());

                if (stack > 0 && !InStackWhiteList(manufacturing, secondaryClassification) && stack != 1)
                {
                    throw new Exception($"道具_总表：{id} 应配置为不可堆叠，stack:{stack} 必须配置为1");
                }

                if (manufacturing == ItemType.Misc && secondaryClassification == MiscSubType.PoiTaskItem && stack == 1)
                {
                    throw new Exception($"道具_总表：poi任务道具 {id} 应配置为可堆叠，stack:{stack} 必须配置为大于1");
                }
            }
            return true;
        }
    }
}
