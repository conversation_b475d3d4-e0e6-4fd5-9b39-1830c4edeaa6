using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class BuffTableChecker : ISheetChecker
    {
        private static readonly List<int> testIds = new() { 2302001, 2302002, 2302003, 2302004, 2302005, 2302006, 2302007, 2302008 };
        
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "charactor_tbbuff";
        }

        public bool Check(SheetChecker info)
        {
            Dictionary<int, List<int>> buffGroupId2BuffId = new();
            Dictionary<int, List<int>> buffType2BuffId = new();
            var buffDict = IdKeyData.GetData(info.GetInputInfo(0));
            foreach (var (_, buffConfig) in buffDict)
            {
                var id = IdKeyData.GetIntVal(buffConfig, "id");
                if (testIds.Contains(id)) continue;
                var buffGroupId = IdKeyData.GetIntVal(buffConfig, "buffGroup");
                if (buffGroupId2BuffId.TryGetValue(buffGroupId, out var buffList))
                {
                    buffList.Add(id);
                }
                else
                {
                    buffGroupId2BuffId[buffGroupId] = new List<int> { id};
                }

                var buffType = IdKeyData.GetIntVal(buffConfig, "buffType");
                if (buffType2BuffId.TryGetValue(buffType, out var typeList))
                {
                    typeList.Add(id);
                }
                else
                {
                    buffType2BuffId[buffType] = new List<int> { id };
                }
            }

            foreach (var (buffGroupId, buffList) in buffGroupId2BuffId)
            {
                var buffId = buffList[0];
                var buffType = IdKeyData.GetIntVal(buffDict[buffId], "buffType");
                var buffGroupOperation = IdKeyData.GetIntVal(buffDict[buffId], "buffGroupOperation");
                var buffGroupStack = IdKeyData.GetIntVal(buffDict[buffId], "buffGroupStackMax");

                foreach (var id in buffList)
                {
                    var type = IdKeyData.GetIntVal(buffDict[id], "buffType");
                    var operation = IdKeyData.GetIntVal(buffDict[id], "buffGroupOperation");
                    var stack = IdKeyData.GetIntVal(buffDict[id], "buffGroupStackMax");
                    if (type != buffType)
                    {
                        throw new Exception($"同一buff组中的buff类型不一致，请检查！buffGroup {buffGroupId} buffType {buffType} type {type}");
                    }

                    if (operation != buffGroupOperation)
                    {
                        throw new Exception($"同一buff组中的buffGroupOperation不一致，请检查！buffGroup {buffGroupId} buffGroupOperation {buffGroupOperation} operation {operation}");
                    }

                    if (buffGroupStack != stack)
                    {
                        throw new Exception($"同一buff组中的buffGroupStackMax不一致，请检查！buffGroup {buffGroupId} buffGroupStackMax {buffGroupStack} stack {stack}");
                    }
                }
            }

            foreach (var (buffType, buffList) in buffType2BuffId)
            {
                var buffId = buffList[0];
                var buffSelfOperation = IdKeyData.GetIntVal(buffDict[buffId], "buffSelfOperation");
                foreach (var id in buffList)
                {
                    var selfOperation = IdKeyData.GetIntVal(buffDict[id], "buffSelfOperation");
                    if (selfOperation != buffSelfOperation)
                    {
                        throw new Exception($"同一buff类型中的buffSelfOperation不一致，请检查！buffType {buffType} buffSelfOperation {buffSelfOperation} selfOperation {selfOperation}");
                    }
                }
            }

            return true;
        }
    }
}
