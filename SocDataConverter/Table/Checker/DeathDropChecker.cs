namespace SocDataConverter.Table.Checker
{
    internal class DeathDropChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "play_tbdeathdropcontainerratio";
            yield return "play_tbdeathdropitemtyperatio";
            yield return "play_tbdeathdropspecialitemratio";
            yield return "play_tbdeathdropgroup";
        }

        public bool Check(SheetChecker info)
        {
            var containerDropInput = info.GetInputInfo(0);
            foreach (var data in containerDropInput.InputJson)
            {
                var containerId = data["ContainerId"].ToObject<int>();
                var ratio = data["DropRatio"].ToObject<int>();
                CheckVal(containerDropInput.Name, containerId, ratio);
            }
            var itemDropInput = info.GetInputInfo(1);
            foreach(var data in itemDropInput.InputJson)
            {
                var itemType = data["ItemType"].ToObject<int>();
                var ratio = data["DropRatio"].ToObject<int>();
                CheckVal(itemDropInput.Name, itemType, ratio);
                var minPercent = data["MinPercent"].ToObject<int>();
                var maxPercent = data["MaxPercent"].ToObject<int>();
                CheckPercent(itemDropInput.Name, itemType, minPercent, maxPercent);
            }
            var specialItemDrop = info.GetInputInfo(2);
            foreach (var data in specialItemDrop.InputJson)
            {
                var itemId = data["ItemId"].ToObject<int>();
                var ratio = data["DropRatio"].ToObject<int>();
                CheckVal(specialItemDrop.Name, itemId, ratio);
                var minPercent = data["MinPercent"].ToObject<int>();
                var maxPercent = data["MaxPercent"].ToObject<int>();
                CheckPercent(specialItemDrop.Name, itemId, minPercent, maxPercent);
            }

            var deathDropGroup = info.GetInputInfo(3);
            foreach (var data in deathDropGroup.InputJson)
            {
                var inhandDropRatio = data["InHandDropRatio"].ToObject<int>();
                if (inhandDropRatio < 0 || inhandDropRatio > 100)
                {
                    throw new System.Exception($"{deathDropGroup.Name} InHandDropRatio {inhandDropRatio} is not in [0, 100]");
                }
            }

            return true;
        }

        private void CheckVal(string name, long id, int val)
        {
            if (val < 0 || val > 100)
            {
                throw new System.Exception($"{name} {id} DropRatio {val} is not in [0, 100]");
            }
        }
        private void CheckPercent(string name, long id, int minPercent, int maxPercent)
        {
            CheckVal(name, id, minPercent);
            CheckVal(name, id, maxPercent);
            if(minPercent > maxPercent)
            {
                throw new System.Exception($"{name} {id} MinPercent {minPercent} > MaxPercent {maxPercent}");
            }
        }

    }
}
