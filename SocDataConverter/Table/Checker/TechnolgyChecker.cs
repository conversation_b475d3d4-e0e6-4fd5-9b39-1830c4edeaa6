using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class TechnolgyChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "dataitem_tbtechnology";
        }

        public bool Check(SheetChecker info)
        {
            var techConfig = IdKeyData.GetData(info.GetInputInfo(0));
            foreach (var (id, item) in techConfig)
            {
                var bpCount = IdKeyData.GetLongList(item, "blueprintIds").Count;
                var itemCount = IdKeyData.GetLongList(item, "Ingredientsids").Count;
                var costCount = IdKeyData.GetIntList(item, "IngredientsNum").Count;

                if (bpCount != itemCount || bpCount != costCount)
                {
                    throw new Exception($"科技表中的数组个数不一致, id:{id}, bpCount:{bpCount}, itemCount:{itemCount}, costCount:{costCount} ");
                }
            }

            return true;
        }
    }
}
