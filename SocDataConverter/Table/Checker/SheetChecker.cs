using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table.Intermedia;
using SocDataConverter.Table.Converter;

namespace SocDataConverter.Table.Checker
{
    internal interface ISheetChecker
    {
        IEnumerable<string> GetInteresetTableNames();
        bool Check(SheetChecker info);
    }

    internal class Sheet<PERSON>hecker
    {
        public readonly List<string> InputNames;
        public readonly ISheetChecker Checker;
        public JArray[] InputJsonList;
        internal static List<SheetChecker> AllSheetCheckers = new();

        public SheetChecker(List<string> inputNames, ISheetChecker checker)
        {
            InputNames = inputNames;
            Checker = checker;
        }

        public bool ParseAndCheck()
        {
            ReadJsonData();
            if (!Checker.Check(this))
            {
                throw new Exception($"Check failed inputNames {InputNames} checkerType {Checker.GetType()}");
            }
            return true;
        }

        public void ReadJsonData()
        {
            InputJsonList = new JArray[InputNames.Count];
            for (var i = 0; i < InputNames.Count; i++)
            {
                InputJsonList[i] = ConvertSheetInfo.getJsonFromCache(Path.Combine(ConvertSheetInfo.lubanJsonPath, $"{InputNames[i]}.json"), InputNames[i]);
            }
        }

        public InputInfo GetInputInfo(int seq)
        {
            return new InputInfo()
            {
                Name = InputNames[seq],
                InputJson = InputJsonList[seq]
            };
        }

        public static void AddSheetChecker(ISheetChecker checker)
        {
            var sheetChecker = new SheetChecker(checker.GetInteresetTableNames().ToList(), checker);
            AllSheetCheckers.Add(sheetChecker);
        }
    }
}
