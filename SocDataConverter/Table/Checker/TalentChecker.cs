using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table.Intermedia;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SocDataConverter.Table.Checker
{
    internal class TalentChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "play_tbtalentlevel";
            yield return "charactor_tbbuff";
            yield return "play_tbreputrwd";
            yield return "dataitem_tbitemconfig";
            yield return "blueprintdata_tbblueprint";
        }

        public bool Check(SheetChecker info)
        {
            var inputInfo = info.GetInputInfo(0);
            var talentLevelArray = inputInfo.InputJson;
            var buffDict = IdKeyData.GetData(info.GetInputInfo(1));
            var reputationRewardDict = IdKeyData.GetData(info.GetInputInfo(2));
            var itemConfig = IdKeyData.GetDataLong(info.GetInputInfo(3));
            var blueprintConfig = IdKeyData.GetData(info.GetInputInfo(4));
            foreach (var item in talentLevelArray)
            {
                var obj = item as JObject;
                var talentId = IdKeyData.GetIntVal(obj, "talentID");
                var effectType = IdKeyData.GetIntVal(obj, "effectType");
                var effectParams = IdKeyData.GetLongList(obj, "effectParams");
                var effectMapParams = IdKeyData.GetDict<long, int>(obj, "effectMapParams");
                switch (effectType)
                {
                    // ActivateBuff
                    case 1:
                        {
                            if (effectParams.Count == 0)
                            {
                                throw new Exception($"天赋等级表中 id为{talentId}的天赋的效果为激活buff 但是没有配置buffID");
                            }

                            foreach (var effectParam in effectParams)
                            {
                                if (!buffDict.ContainsKey((int)effectParam))
                                {
                                    throw new Exception($"天赋等级表中 id为{talentId}的天赋的效果为激活buff 但是配置的buffID {effectParam} 在buff表中不存在");
                                }
                            }
                            break;
                        }
                    // AddReputationSelectReward
                    case 2:
                        {
                            if (effectParams.Count == 0)
                            {
                                throw new Exception($"天赋等级表中 id为{talentId}的天赋的效果为添加声望奖励 但是没有配置奖励ID");
                            }

                            foreach (var effectParam in effectParams)
                            {
                                if (!reputationRewardDict.TryGetValue((int)effectParam, out var repObj))
                                {
                                    throw new Exception($"天赋等级表中 id为{talentId}的天赋的效果为添加声望奖励 但是配置的奖励ID {effectParam} 在声望奖励表中不存在");
                                }

                                var isTalentLocked = IdKeyData.GetBoolVal(repObj, "isTalentLocked");
                                if (!isTalentLocked)
                                {
                                    throw new Exception($"天赋等级表中 id为{talentId}的天赋的效果为添加声望奖励 但是配置的奖励ID {effectParam} 在声望奖励表中没有设置isTalentLocked为true");
                                }
                            }
                            break;
                        }
                    // FirstBornAddProperty
                    case 3:
                        {

                            break;
                        }
                    // FirstBornAddItem
                    case 4:
                        {
                            if (effectMapParams.Count == 0)
                            {
                                throw new Exception($"天赋等级表中 id为{talentId}的天赋的效果为添加物品 但是没有配置物品ID");
                            }
                            foreach (var (itemId, count) in effectMapParams)
                            {
                                if (!itemConfig.TryGetValue((int)itemId, out var itemObj))
                                {
                                    throw new Exception($"天赋等级表中 id为{talentId}的天赋的效果为添加物品 但是配置的物品ID {itemId} 在物品配置表中不存在");
                                }
                            }
                            break;
                        }
                    // UnlockConstructionBlueprint
                    case 5:
                        {
                            if (effectParams.Count == 0)
                            {
                                throw new Exception($"天赋等级表中 id为{talentId}的天赋的效果为解锁建筑蓝图 但是没有配置蓝图ID");
                            }

                            foreach (var effectParam in effectParams)
                            {
                                if (!blueprintConfig.TryGetValue((int)effectParam, out var blueprintObj))
                                {
                                    throw new Exception($"天赋等级表中 id为{talentId}的天赋的效果为解锁建筑蓝图 但是配置的蓝图ID {effectParam} 在蓝图表中不存在");
                                }
                            }
                            break;
                        }
                    default:
                        {
                            break;
                        }
                }
            }
            return true;
        }
    }
}
