using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class BlueprintChecker : ISheetChecker
    {
        private const int RustComponentFlag = 111;

        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "blueprintdata_tbblueprint";
            yield return "dataitem_tbitemconfig";
            yield return "gun_tbgunbase";
        }

        public bool Check(SheetChecker info)
        {
            var blueprintDict = IdKeyData.GetData(info.GetInputInfo(0));
            var itemDict = IdKeyData.GetData(info.GetInputInfo(1));
            var gunDict = IdKeyData.GetData("gunid", info.GetInputInfo(2));
            Dictionary<long, long> itemId2BpId = new();

            foreach (var (bpId, data) in blueprintDict)
            {
                var ItemId = IdKeyData.GetLongVal(data, "ItemId");
                if (ItemId == 0)
                {
                    throw new Exception($"blueprint:{bpId} config not have itemId ");
                }

                if (itemId2BpId.ContainsKey(ItemId))
                {
                    throw new Exception($"blueprint:{bpId} config itemId:{ItemId} is duplicate");
                }
                itemId2BpId.Add(ItemId, bpId);

                var bulletId = IdKeyData.GetIntVal(data, "BulletId");
                if (bulletId != 0)
                {
                    if (!gunDict.TryGetValue((int)ItemId, out var gunData))
                    {
                        throw new Exception($"blueprint:{bpId} config ItemId:{ItemId} not exist in gun_tbgunbase");
                    }

                    var bulletIds = IdKeyData.GetIntList(gunData, "availablebullet");
                    if (bulletIds == null || !bulletIds.Contains(bulletId))
                    {
                        throw new Exception($"blueprint:{bpId} config ItemId:{ItemId} not have bulletId:{bulletId} in gun_tbgunbase");
                    }

                    var bulletNum = IdKeyData.GetIntVal(data, "BulletNumber");
                    var maxBulletNum = IdKeyData.GetIntVal(gunData, "cartridge");
                    if (bulletNum < 0 || bulletNum > maxBulletNum)
                    {
                        throw new Exception($"blueprint:{bpId} config ItemId:{ItemId} BulletNumber:{bulletNum} is illegal");
                    }
                }
            }

            foreach (var (id, data) in blueprintDict)
            {
                var ingrediants = IdKeyData.GetIntList(data, "Ingredients");
                var ingrediantsNum = IdKeyData.GetIntList(data, "IngredientsNum");

                for (int i = 0; i < ingrediants.Count; i++)
                {
                    var ingrediantId = ingrediants[i];
                    // 若该材料的道具无 111(Rust组件)标签，直接检查材料和数量
                    if (!itemDict.ContainsKey(ingrediantId))
                    {
                        throw new Exception($"蓝图：{id}，配置非法的维修材料道具Id：{ingrediantId}");
                    }

                    var ingrediantData = itemDict[ingrediantId];
                    var ingrediantFlags = IdKeyData.GetIntList(ingrediantData, "Itemflags");
                    if (ingrediantFlags.Contains(RustComponentFlag))
                    {
                        var ingrediantBpId = (int)itemId2BpId[ingrediantId];
                        // 若该材料的道具有 111(Rust组件)标签，则检查该组件的蓝图的第一个材料
                        var ingrediantBlueprint = blueprintDict[ingrediantBpId];
                        var ingrediantOfIngrediant = IdKeyData.GetIntList(ingrediantBlueprint, "Ingredients");
                        var ingrediantNumOfIngrediant = IdKeyData.GetIntList(ingrediantBlueprint, "IngredientsNum");
                       
                        if (!itemDict.ContainsKey(ingrediantOfIngrediant[0]))
                        {
                            throw new Exception($"蓝图：{ingrediantBpId}，配置非法的维修材料道具Id：{ingrediantOfIngrediant[0]}");
                        }

                        if (ingrediantNumOfIngrediant[0] <= 0)
                        {
                            throw new Exception($"蓝图：{ingrediantBpId}，维修材料道具Id：{ingrediantId}，配置非法的维修材料道具数量：{ingrediantNumOfIngrediant[0]}");
                        }
                    }
                    else
                    {
                        if (ingrediantsNum[i] <= 0)
                        {
                            throw new Exception($"蓝图：{id}，维修材料道具Id：{ingrediantId}，配置非法的维修材料道具数量：{ingrediantsNum[i]}");
                        }
                    }
                }
            }

            return true;
        }
    }
}
