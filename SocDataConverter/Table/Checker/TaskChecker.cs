using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table.Intermedia;
using WizardGames.SocConst.Soc.Const;

namespace SocDataConverter.Table.Checker
{
    public enum MainTaskType
    {
        /// <summary>
        /// 引导任务
        /// </summary>
        GuideTask = 1,
        /// <summary>
        /// 探索任务
        /// </summary>
        ExploreTask = 2,
        /// <summary>
        /// 剧本任务
        /// </summary>
        StoryTask = 4,
        /// <summary>
        /// Poi寻宝任务
        /// </summary>
        PoiTask = 5,
        /// <summary>
        /// 徽章任务
        /// </summary>
        BadgeTask = 6,
        /// <summary>
        /// 蜂鸣行动任务
        /// </summary>
        BeeBuzzTask = 7,
        /// <summary>
        /// 日常任务
        /// </summary>
        DailyTask = 8,
        /// <summary>
        /// 大厅任务局内部分
        /// </summary>
        LobbyMainTask = 9,
    }

    public enum ETaskTriggerEventType
    {
        /// <summary>
        /// 跳转任务
        /// </summary>
        SkipTask = 1,
        /// <summary>
        /// 刷新资源
        /// </summary>
        Spawn = 2,
        /// <summary>
        /// 删除空气墙
        /// </summary>
        CloseAirWall = 3,
        /// <summary>
        /// 重置玩家状态
        /// </summary>
        ResetPlayerStatus = 4,
        /// <summary>
        /// 指定坐标刷新交互物
        /// </summary>
        SpawnAtSpecificPos = 5,
        /// <summary>
        /// 指定坐标刷新建筑
        /// </summary>
        SpawnConstruction = 6,
        /// <summary>
        /// 删除领地柜
        /// </summary>
        DelSpawnTerrioryCabinet = 7,
        /// <summary>
        /// Poi任务事件
        /// </summary>
        PoiEvent = 8,
        /// <summary>
        /// 关闭声望柜
        /// </summary>
        CloseReputation = 11,
        /// <summary>
        /// 开启空气墙
        /// </summary>
        OpenAirWall = 12,
        /// <summary>
        /// 遗迹内刷新
        /// </summary>
        SpawnInMonumentEvent = 13,
        /// <summary>
        /// 延迟刷新
        /// </summary>
        DelaySpawn = 14,
        /// <summary>
        /// 添加Buff
        /// </summary>
        AddBuff = 15,
        /// <summary>
        /// 删除Buff
        /// </summary>
        DelBuff = 16,
        /// <summary>
        /// 修改玩家状态
        /// </summary>
        ChangePlayerCalories = 17,
    }
    
    internal class TaskChecker : ISheetChecker
    {
        private IDictionary<int, JObject> taskTable;
        private IDictionary<int, JObject> dropTable;
        private IDictionary<int, JObject> spawnTable;
        private IDictionary<int, JObject> statusTable;
        private IDictionary<int, JObject> idGroupTable;
        private IDictionary<int, JObject> taskConditionTable;
        private IDictionary<int, JObject> spawnAtMonumentTable;
        private IDictionary<int, JObject> spawnConstrutionTable;
        private IDictionary<int, JObject> buffTable;

        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "play_tbquestphase";
            yield return "itemreward_tbitemdrop";
            yield return "play_tbquestspawn";
            yield return "play_tbcharacterstatus";
            yield return "play_tbtaskidgroup";
            yield return "play_tbquestcondition";
            yield return "play_tbspawnmonumenttask";
            yield return "play_tbspawnconstruction";
            yield return "charactor_tbbuff";
        }

        public bool Check(SheetChecker info)
        {
            taskTable = IdKeyData.GetData(info.GetInputInfo(0));
            dropTable = IdKeyData.GetData(info.GetInputInfo(1));
            spawnTable = IdKeyData.GetData(info.GetInputInfo(2));
            statusTable = IdKeyData.GetData(info.GetInputInfo(3));
            idGroupTable = IdKeyData.GetData(info.GetInputInfo(4));
            taskConditionTable = IdKeyData.GetData(info.GetInputInfo(5));
            spawnAtMonumentTable = IdKeyData.GetData(info.GetInputInfo(6));
            spawnConstrutionTable = IdKeyData.GetData(info.GetInputInfo(7));
            buffTable = IdKeyData.GetData(info.GetInputInfo(8));

            foreach (var (taskId, jsonData) in taskTable)
            {
                CheckTask(taskId, jsonData);
            }

            return true;
        }

        public void CheckTask(int taskId, JObject taskJsonData)
        {
            var taskType = IdKeyData.GetIntVal(taskJsonData, "type");
            switch (taskType)
            {
                case (int)MainTaskType.GuideTask:
                    CheckGuideTask(taskId, taskJsonData);
                    break;
                case (int)MainTaskType.PoiTask:
                    CheckPoiTask(taskId, taskJsonData);
                    break;
                default:
                    CheckCommonTask(taskId, taskJsonData);
                    break;
            }
        }

        private void CheckCommonTask(int taskId, JObject taskJsonData)
        {
            var taskGroupId = IdKeyData.GetIntVal(taskJsonData, "taskId");
            if (taskGroupId > 0)
            {
                throw new Exception($"任务：{taskId}的taskId字段不能配置，只能为0");
            }

            var startCondition = IdKeyData.GetIntVal(taskJsonData, "taskPhaseBeginCondition");
            if (startCondition > 0)
            {
                throw new Exception($"任务：{taskId}不支持配置开始条件");
            }

            var startConditionParam = IdKeyData.GetLongList(taskJsonData, "beginConditionParameter");
            if (startConditionParam.Count > 0)
            {
                throw new Exception($"任务：{taskId}不支持配置开始条件参数");
            }

            var beginEvent = IdKeyData.GetIntList(taskJsonData, "beginEvent");
            if (beginEvent.Count > 0)
            {
                throw new Exception($"任务：{taskId}不支持配置开始事件");
            }

            var beginEvent1 = IdKeyData.GetIntList(taskJsonData, "beginEvent1");
            if (beginEvent1.Count > 0)
            {
                throw new Exception($"任务：{taskId}不支持配置开始事件1");
            }

            var beginEvent2 = IdKeyData.GetIntList(taskJsonData, "beginEvent2");
            if (beginEvent2.Count > 0)
            {
                throw new Exception($"任务：{taskId}不支持配置开始事件2");
            }

            var taskPhaseResetCondition = IdKeyData.GetIntVal(taskJsonData, "ResetCondition");
            if (taskPhaseResetCondition > 0)
            {
                if (!taskConditionTable.ContainsKey(taskPhaseResetCondition))
                {
                    throw new Exception($"任务：{taskId}配置非法的重置条件Id");
                }

                var taskPhaseResetConditionParam = IdKeyData.GetLongList(taskJsonData, "ResetConditionParameter");
                if (taskPhaseResetConditionParam.Count <= 0)
                {
                    throw new Exception($"任务：{taskId}没有配置重置条件参数");
                }

                CheckResetCondition(taskId, taskJsonData, taskPhaseResetCondition, taskPhaseResetConditionParam);
            }

            var taskPhaseEndCondition = IdKeyData.GetIntVal(taskJsonData, "taskPhaseEndCondition");
            if (taskPhaseEndCondition <= 0)
            {
                throw new Exception($"任务：{taskId}没有配置结束条件");
            }

            if (!taskConditionTable.ContainsKey(taskPhaseEndCondition))
            {
                throw new Exception($"任务：{taskId}配置非法的结束条件Id");
            }

            var taskPhaseEndConditionParam = IdKeyData.GetLongList(taskJsonData, "endConditionParameter");
            if (taskPhaseEndConditionParam.Count <= 0)
            {
                throw new Exception($"任务：{taskId}没有配置结束条件参数");
            }

            CheckEndCondition(taskId, taskJsonData, taskPhaseEndCondition, taskPhaseEndConditionParam);

            var subTaskIds = IdKeyData.GetIntList(taskJsonData, "subTasks");
            foreach (var subTaskId in subTaskIds)
            {
                CheckSubTask(taskId, IdKeyData.GetIntVal(taskJsonData, "type"), subTaskId);
            }

            var endEvent = IdKeyData.GetIntList(taskJsonData, "endEvent");
            if (endEvent.Count > 0)
            {
                throw new Exception($"任务：{taskId}不支持配置结束事件");
            }

            var endEvent1 = IdKeyData.GetIntList(taskJsonData, "endEvent1");
            if (endEvent1.Count > 0)
            {
                throw new Exception($"任务：{taskId}不支持配置结束事件1");
            }

            var endEvent2 = IdKeyData.GetIntList(taskJsonData, "endEvent2");
            if (endEvent2.Count > 0)
            {
                throw new Exception($"任务：{taskId}不支持配置结束事件2");
            }

            var taskRewardDropIds = IdKeyData.GetIntList(taskJsonData, "taskRewardId");
            foreach (var dropId in taskRewardDropIds)
            {
                if (!dropTable.ContainsKey(dropId))
                {
                    throw new Exception($"任务：{taskId}的taskRewardId字段配置了非法的掉落ID：{dropId}");
                }
            }
        }

        private void CheckGuideTask(int taskId, JObject taskJsonData)
        {
            var taskGroupId = IdKeyData.GetIntVal(taskJsonData, "taskId");
            if (taskGroupId <= 0 && IdKeyData.GetIntVal(taskJsonData, "isSubTask") == 0)
            {
                throw new Exception($"引导任务：{taskId}的taskId字段配置不能为0");
            }

            var taskPhaseBeginCondition = IdKeyData.GetIntVal(taskJsonData, "taskPhaseBeginCondition");
            if (taskPhaseBeginCondition > 0)
            {
                if (!taskConditionTable.ContainsKey(taskPhaseBeginCondition))
                {
                    throw new Exception($"引导任务：{taskId}配置非法的开始条件Id");
                }

                var taskPhaseBeginConditionParam = IdKeyData.GetLongList(taskJsonData, "beginConditionParameter");
                if (taskPhaseBeginConditionParam.Count <= 0)
                {
                    throw new Exception($"引导任务：{taskId}没有配置开始条件参数");
                }

                CheckBeginCondition(taskId, taskJsonData, taskPhaseBeginCondition, taskPhaseBeginConditionParam);
            }

            var taskPhaseResetCondition = IdKeyData.GetIntVal(taskJsonData, "ResetCondition");
            if (taskPhaseResetCondition > 0)
            {
                if (!taskConditionTable.ContainsKey(taskPhaseResetCondition))
                {
                    throw new Exception($"任务：{taskId}配置非法的重置条件Id");
                }

                var taskPhaseResetConditionParam = IdKeyData.GetLongList(taskJsonData, "ResetConditionParameter");
                if (taskPhaseResetConditionParam.Count <= 0)
                {
                    throw new Exception($"任务：{taskId}没有配置重置条件参数");
                }

                CheckResetCondition(taskId, taskJsonData, taskPhaseResetCondition, taskPhaseResetConditionParam);
            }

            var taskPhaseEndCondition = IdKeyData.GetIntVal(taskJsonData, "taskPhaseEndCondition");
            if (taskPhaseEndCondition <= 0)
            {
                throw new Exception($"引导任务：{taskId}没有配置结束条件");
            }

            if (!taskConditionTable.ContainsKey(taskPhaseEndCondition))
            {
                throw new Exception($"引导任务：{taskId}配置非法的结束条件Id");
            }

            var taskPhaseEndConditionParam = IdKeyData.GetLongList(taskJsonData, "endConditionParameter");
            if (taskPhaseEndConditionParam.Count <= 0)
            {
                throw new Exception($"引导任务：{taskId}没有配置结束条件参数");
            }

            CheckEndCondition(taskId, taskJsonData, taskPhaseEndCondition, taskPhaseEndConditionParam);

            var subTaskIds = IdKeyData.GetIntList(taskJsonData, "subTasks");
            foreach (var subTaskId in subTaskIds)
            {
                CheckSubTask(taskId, IdKeyData.GetIntVal(taskJsonData, "type"), subTaskId);
            }

            var taskRewardDropIds = IdKeyData.GetIntList(taskJsonData, "taskRewardId");
            foreach (var dropId in taskRewardDropIds)
            {
                if (!dropTable.ContainsKey(dropId))
                {
                    throw new Exception($"引导任务：{taskId}的taskRewardId字段配置了非法的掉落ID：{dropId}");
                }
            }

            var beginEvent = IdKeyData.GetIntList(taskJsonData, "beginEvent");
            if (beginEvent.Count > 0)
            {
                CheckTriggerEventParam(taskId, taskJsonData, beginEvent, true, 0);
            }

            var beginEvent1 = IdKeyData.GetIntList(taskJsonData, "beginEvent1");
            if (beginEvent1.Count > 0)
            {
                CheckTriggerEventParam(taskId, taskJsonData, beginEvent1, true, 1);
            }

            var beginEvent2 = IdKeyData.GetIntList(taskJsonData, "beginEvent2");
            if (beginEvent2.Count > 0)
            {
                CheckTriggerEventParam(taskId, taskJsonData, beginEvent2, true, 2);
            }

            var endEvent = IdKeyData.GetIntList(taskJsonData, "endEvent");
            if (endEvent.Count > 0)
            {
                CheckTriggerEventParam(taskId, taskJsonData, endEvent, false, 0);
            }

            var endEvent1 = IdKeyData.GetIntList(taskJsonData, "endEvent1");
            if (endEvent1.Count > 0)
            {
                CheckTriggerEventParam(taskId, taskJsonData, endEvent1, false, 1);
            }

            var endEvent2 = IdKeyData.GetIntList(taskJsonData, "endEvent2");
            if (endEvent2.Count > 0)
            {
                CheckTriggerEventParam(taskId, taskJsonData, endEvent2, false, 2);
            }
        }

        private void CheckPoiTask(int taskId, JObject taskJsonData)
        {
            var taskGroupId = IdKeyData.GetIntVal(taskJsonData, "taskId");
            if (taskGroupId > 0)
            {
                throw new Exception($"Poi任务：{taskId}的taskId字段不能配置，只能为0");
            }

            var startCondition = IdKeyData.GetIntVal(taskJsonData, "taskPhaseBeginCondition");
            if (startCondition > 0)
            {
                throw new Exception($"Poi任务：{taskId}不支持配置开始条件");
            }

            var startConditionParam = IdKeyData.GetLongList(taskJsonData, "beginConditionParameter");
            if (startConditionParam.Count > 0)
            {
                throw new Exception($"Poi任务：{taskId}不支持配置开始条件参数");
            }

            var beginEvent = IdKeyData.GetIntList(taskJsonData, "beginEvent");
            if (beginEvent.Count > 0)
            {
                if (beginEvent[0] != (int)ETaskTriggerEventType.PoiEvent)
                {
                    throw new Exception($"Poi任务：{taskId}开始事件类型错误， 只允许配置为：{(int)ETaskTriggerEventType.PoiEvent}");
                }
            }

            var taskPhaseResetCondition = IdKeyData.GetIntVal(taskJsonData, "ResetCondition");
            if (taskPhaseResetCondition > 0)
            {
                if (!taskConditionTable.ContainsKey(taskPhaseResetCondition))
                {
                    throw new Exception($"任务：{taskId}配置非法的重置条件Id");
                }

                var taskPhaseResetConditionParam = IdKeyData.GetLongList(taskJsonData, "ResetConditionParameter");
                if (taskPhaseResetConditionParam.Count <= 0)
                {
                    throw new Exception($"任务：{taskId}没有配置重置条件参数");
                }

                CheckResetCondition(taskId, taskJsonData, taskPhaseResetCondition, taskPhaseResetConditionParam);
            }

            var beginEvent1 = IdKeyData.GetIntList(taskJsonData, "beginEvent1");
            if (beginEvent1.Count > 0)
            {
                throw new Exception($"Poi任务：{taskId}不支持配置开始事件1");
            }

            var beginEvent2 = IdKeyData.GetIntList(taskJsonData, "beginEvent2");
            if (beginEvent2.Count > 0)
            {
                throw new Exception($"Poi任务：{taskId}不支持配置开始事件2");
            }

            var taskPhaseEndCondition = IdKeyData.GetIntVal(taskJsonData, "taskPhaseEndCondition");
            if (taskPhaseEndCondition <= 0)
            {
                throw new Exception($"Poi任务：{taskId}没有配置结束条件");
            }

            var taskPhaseEndConditionParam = IdKeyData.GetLongList(taskJsonData, "endConditionParameter");
            if (taskPhaseEndConditionParam.Count <= 0)
            {
                throw new Exception($"Poi任务：{taskId}没有配置结束条件参数");
            }

            if (!taskConditionTable.ContainsKey(taskPhaseEndCondition))
            {
                throw new Exception($"Poi任务：{taskId}配置非法的结束条件Id");
            }

            CheckEndCondition(taskId, taskJsonData, taskPhaseEndCondition, taskPhaseEndConditionParam);

            var endEvent = IdKeyData.GetIntList(taskJsonData, "endEvent");
            if (endEvent.Count > 0)
            {
                throw new Exception($"Poi任务：{taskId}不支持配置结束事件");
            }

            var endEvent1 = IdKeyData.GetIntList(taskJsonData, "endEvent1");
            if (endEvent1.Count > 0)
            {
                throw new Exception($"Poi任务：{taskId}不支持配置结束事件1");
            }

            var endEvent2 = IdKeyData.GetIntList(taskJsonData, "endEvent2");
            if (endEvent2.Count > 0)
            {
                throw new Exception($"Poi任务：{taskId}不支持配置结束事件2");
            }

            var subTaskIds = IdKeyData.GetIntList(taskJsonData, "subTasks");
            foreach (var subTaskId in subTaskIds)
            {
                CheckSubTask(taskId, IdKeyData.GetIntVal(taskJsonData, "type"), subTaskId);
            }

            var autoGrant = IdKeyData.GetBoolVal(taskJsonData, "autoGrant");
            if (autoGrant)
            {
                throw new Exception($"Poi任务：{taskId}不支持配置自动领奖");
            }

            var taskRewardDropIds = IdKeyData.GetIntList(taskJsonData, "taskRewardId");
            foreach (var dropId in taskRewardDropIds)
            {
                if (!dropTable.ContainsKey(dropId))
                {
                    throw new Exception($"Poi任务：{taskId}的taskRewardId字段配置了非法的掉落ID：{dropId}");
                }
            }
        }

        private void CheckSubTask(int taskId, int taskType, int subTaskId)
        {
            if (!taskTable.TryGetValue(subTaskId, out var taskJsonData))
            {
                throw new Exception($"任务：{taskId}配置非法的子任务ID：{subTaskId}");
            }

            var isSubTask = IdKeyData.GetIntVal(taskJsonData, "isSubTask");
            if (isSubTask != 1)
            {
                throw new Exception($"任务：{taskId}配置的子任务ID：{subTaskId}不是子任务");
            }

            var subTaskType = IdKeyData.GetIntVal(taskJsonData, "type");
            if (subTaskType != taskType)
            {
                throw new Exception($"任务：{taskId}的任务类型：{taskType}和配置的子任务ID：{subTaskId}的任务类型不一致{subTaskType}");
            }

            var startCondition = IdKeyData.GetIntVal(taskJsonData, "taskPhaseBeginCondition");
            if (startCondition > 0)
            {
                throw new Exception($"子任务：{subTaskId}不支持配置开始条件");
            }

            var startConditionParam = IdKeyData.GetLongList(taskJsonData, "beginConditionParameter");
            if (startConditionParam.Count > 0)
            {
                throw new Exception($"子任务：{subTaskId}不支持配置开始条件参数");
            }

            var beginEvent = IdKeyData.GetIntList(taskJsonData, "beginEvent");
            if (beginEvent.Count > 0)
            {
                throw new Exception($"子任务：{subTaskId}不支持配置开始事件");
            }

            var beginEvent1 = IdKeyData.GetIntList(taskJsonData, "beginEvent1");
            if (beginEvent1.Count > 0)
            {
                throw new Exception($"子任务：{subTaskId}不支持配置开始事件1");
            }

            var beginEvent2 = IdKeyData.GetIntList(taskJsonData, "beginEvent2");
            if (beginEvent2.Count > 0)
            {
                throw new Exception($"子任务：{subTaskId}不支持配置开始事件2");
            }

            var taskPhaseEndCondition = IdKeyData.GetIntVal(taskJsonData, "taskPhaseEndCondition");
            if (taskPhaseEndCondition <= 0)
            {
                throw new Exception($"子任务：{subTaskId}没有配置结束条件");
            }

            if (!taskConditionTable.ContainsKey(taskPhaseEndCondition))
            {
                throw new Exception($"子任务：{subTaskId}配置非法的结束条件Id");
            }

            var taskPhaseEndConditionParam = IdKeyData.GetLongList(taskJsonData, "endConditionParameter");
            if (taskPhaseEndConditionParam.Count <= 0)
            {
                throw new Exception($"子任务：{subTaskId}没有配置结束条件参数");
            }

            CheckEndCondition(taskId, taskJsonData, taskPhaseEndCondition, taskPhaseEndConditionParam);

            var subTaskIds = IdKeyData.GetIntList(taskJsonData, "subTasks");
            if (subTaskIds.Count > 0)
            {
                throw new Exception($"子任务：{subTaskId}不支持再配置子任务");
            }

            var endEvent = IdKeyData.GetIntList(taskJsonData, "endEvent");
            if (endEvent.Count > 0)
            {
                throw new Exception($"子任务：{subTaskId}不支持配置结束事件");
            }

            var endEvent1 = IdKeyData.GetIntList(taskJsonData, "endEvent1");
            if (endEvent1.Count > 0)
            {
                throw new Exception($"子任务：{subTaskId}不支持配置结束事件1");
            }

            var endEvent2 = IdKeyData.GetIntList(taskJsonData, "endEvent2");
            if (endEvent2.Count > 0)
            {
                throw new Exception($"子任务：{subTaskId}不支持配置结束事件2");
            }

            var completeTime = IdKeyData.GetIntVal(taskJsonData, "completeTime");
            if (completeTime > 0)
            {
                throw new Exception($"子任务：{subTaskId}不能是定时任务");
            }

            var taskRewardDropIds = IdKeyData.GetIntList(taskJsonData, "taskRewardId");
            if (taskRewardDropIds.Count > 0)
            {
                throw new Exception($"子任务：{subTaskId}不支持配置奖励信息");
            }
        }

        private void CheckBeginCondition(int taskId, JObject taskJsonData, int conditionId, List<long> conditionParams)
        {
            if (!taskConditionTable.TryGetValue(conditionId, out var conditionJsonData))
            {
                throw new Exception($"任务：{taskId}配置非法的条件Id");
            }

            var counterChange = IdKeyData.GetIntVal(conditionJsonData, "counterChange");
            if (counterChange != 1)
            {
                throw new Exception($"任务：{taskId}配置开始条件：{conditionId}计数器类型错误，必须为GetValue");
            }

            CheckTaskCondition(taskId, taskJsonData, conditionId, conditionParams);
        }

        private void CheckResetCondition(int taskId, JObject taskJsonData, int conditionId, List<long> conditionParams)
        {
            if (!taskConditionTable.TryGetValue(conditionId, out var conditionJsonData))
            {
                throw new Exception($"任务：{taskId}配置非法的条件Id");
            }

            var counterChange = IdKeyData.GetIntVal(conditionJsonData, "counterChange");
            if (counterChange != 2)
            {
                throw new Exception($"任务：{taskId}配置重置条件：{conditionId}计数器类型错误，必须为Incremental");
            }

            CheckTaskCondition(taskId, taskJsonData, conditionId, conditionParams);
        }

        private void CheckEndCondition(int taskId, JObject taskJsonData, int conditionId, List<long> conditionParams) => CheckTaskCondition(taskId, taskJsonData, conditionId, conditionParams);

        private void CheckTaskCondition(int taskId, JObject taskJsonData, int conditionId, List<long> conditionParams)
        {
            if (!taskConditionTable.TryGetValue(conditionId, out var conditionJsonData))
            {
                throw new Exception($"任务：{taskId}配置非法的条件Id");
            }

            var targetDataType = (TargetData)IdKeyData.GetIntVal(conditionJsonData, "targetData");
            CheckConditionParamLength(taskId, conditionId, targetDataType, conditionParams);
        }

        private void CheckConditionParamLength(int taskId, int conditionId, TargetData type, List<long> param)
        {
            switch (type)
            {
                case TargetData.ContainerPropNum:
                case TargetData.ConsumeItem:
                case TargetData.ConsumeItemForType:
                case TargetData.GetItem:
                case TargetData.GetItemForType:
                case TargetData.BuildLevelNum:
                case TargetData.ElectricConnection:
                case TargetData.EquipWeapon:
                case TargetData.WinGame:
                case TargetData.Unload:
                case TargetData.PlacePropsIntoContainer:
                    if (param.Count != 3)
                    {
                        throw new Exception($"任务表中配置条件ID：{conditionId}，参数数量错误，请检查配置！id {taskId}");
                    }
                    break;
                case TargetData.HydrationNum:
                case TargetData.CaloriesNum:
                    if (param.Count != 1)
                    {
                        throw new Exception($"任务表中配置条件ID：{conditionId}，参数数量错误，请检查配置！id {taskId}");
                    }
                    break;
                default:
                    if (param.Count != 2)
                    {
                        throw new Exception($"任务表中配置条件ID：{conditionId}，参数数量错误，请检查配置！id {taskId}");
                    }
                    break;
            }

            var id = param[param.Count - 1];
            if (IsIdGroupId(id))
            {
                if (!idGroupTable.ContainsKey((int)id))
                {
                    throw new Exception($"任务表中配置条件ID：{conditionId}，参数组ID：{id}错误，请检查配置！id {taskId}");
                }
            }
        }

        private bool IsIdGroupId(long id) => id <= NodeConst.IdGroupEnd && id >= NodeConst.IdGroupStart;

        private void CheckTriggerEventParam(int taskId, JObject taskJsonData, List<int> eventParams, bool isBeginEvent, int eventIndex)
        {
            var triggerType = (ETaskTriggerEventType)eventParams[0];
            switch (triggerType) {
            case ETaskTriggerEventType.SkipTask:
                CheckSkipTask();
                break;
            case ETaskTriggerEventType.Spawn:
                CheckSpawn();
                break;
            case ETaskTriggerEventType.CloseAirWall:
                CheckCloseAirWall();
                break;
            case ETaskTriggerEventType.ResetPlayerStatus:
                CheckResetPlayerStatus();
                break;
            case ETaskTriggerEventType.SpawnAtSpecificPos:
                CheckSpawnAtSpecificPos();
                break;
            case ETaskTriggerEventType.SpawnConstruction:
                CheckSpawnConstruction();
                break;
            case ETaskTriggerEventType.DelSpawnTerrioryCabinet:
                CheckDelSpawnTerrioryCabinet();
                break;
            case ETaskTriggerEventType.CloseReputation:
                CheckCloseReputation();
                break;
            case ETaskTriggerEventType.OpenAirWall:
                CheckOpenAirWall();
                break;
            case ETaskTriggerEventType.SpawnInMonumentEvent:
                CheckSpawnInMonumentEvent();
                break;
            case ETaskTriggerEventType.DelaySpawn:
                CheckDelaySpawn();
                break;
            case ETaskTriggerEventType.AddBuff:
                CheckAddBuff();
                break;
            case ETaskTriggerEventType.DelBuff:
                CheckDelBuff();
                break;
            case ETaskTriggerEventType.ChangePlayerCalories:
                CheckChangePlayerCalories();
                break;
            default:
                break;
            }

            void CheckSkipTask()
            {
                throw new Exception($"任务：{taskId}，{Desc()}，跳转事件已作废，不支持配置");
            }

            void CheckSpawn()
            {
                if (eventParams.Count <= 1)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}参数长度错误");
                }

                for (int i = 1; i < eventParams.Count; i++)
                {
                    if (!spawnTable.ContainsKey(eventParams[i]))
                    {
                        throw new Exception($"任务：{taskId}，{Desc()}配置非法的刷新id:{eventParams[i]}");
                    }
                }
            }

            void CheckCloseAirWall()
            {
                if (eventParams.Count <= 1)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}参数长度错误");
                }
            }

            void CheckResetPlayerStatus()
            {
                if (eventParams.Count != 2)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}参数长度错误");
                }

                if (!statusTable.ContainsKey(eventParams[1]))
                {
                    throw new Exception($"任务：{taskId}，{Desc()}配置非法的角色状态id:{eventParams[1]}");
                }
            }

            void CheckSpawnAtSpecificPos()
            {
                if (eventParams.Count <= 1)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}参数长度错误");
                }

                for (int i = 1; i < eventParams.Count; i++)
                {
                    if (!spawnTable.ContainsKey(eventParams[i]))
                    {
                        throw new Exception($"任务：{taskId}，{Desc()}配置非法的刷新id:{eventParams[i]}");
                    }
                }
            }

            void CheckSpawnConstruction()
            {
                if (eventParams.Count != 2)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}参数长度错误");
                }

                for (int i = 1; i < eventParams.Count; i++)
                {
                    if (!spawnConstrutionTable.ContainsKey(eventParams[i]))
                    {
                        throw new Exception($"任务：{taskId}，{Desc()}配置非法的建筑刷新id:{eventParams[i]}");
                    }
                }
            }

            void CheckDelSpawnTerrioryCabinet()
            {
                if (eventParams.Count <= 1)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}参数长度错误");
                }

                for (int i = 1; i < eventParams.Count; i++)
                {
                    if (!taskTable.ContainsKey(eventParams[i]))
                    {
                        throw new Exception($"任务：{taskId}，{Desc()}配置非法的任务id:{eventParams[i]}");
                    }
                }
            }

            void CheckCloseReputation()
            {
                if (eventParams.Count != 2)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}参数长度错误");
                }

                if (eventParams[1] != 0 && eventParams[1] != 1)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}配置非法的角色状态id:{eventParams[1]}");
                }
            }

            void CheckOpenAirWall()
            {
                if (eventParams.Count <= 1)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}参数长度错误");
                }
            }

            void CheckSpawnInMonumentEvent()
            {
                if (eventParams.Count <= 1)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}参数长度错误");
                }

                for (int i = 1; i < eventParams.Count; i++)
                {
                    if (!spawnAtMonumentTable.ContainsKey(eventParams[i]))
                    {
                        throw new Exception($"任务：{taskId}，{Desc()}配置非法的遗迹刷新id:{eventParams[i]}");
                    }
                }
            }

            void CheckDelaySpawn()
            {
                if (eventParams.Count != 3)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}参数长度错误");
                }

                var timeout = eventParams[1];
                if (timeout <= 0)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}延迟刷新时间不能小于等于0");
                }

                if (!spawnTable.ContainsKey(eventParams[2]))
                {
                    throw new Exception($"任务：{taskId}，{Desc()}配置非法的刷新id:{eventParams[2]}");
                }
            }
            
            void CheckAddBuff()
            {
                if (eventParams.Count != 2)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}参数长度错误");
                }

                var buffId = eventParams[1];
                if (!buffTable.ContainsKey(buffId))
                {
                    throw new Exception($"任务：{taskId}，{Desc()}非法的buff id");
                }
            }

            void CheckDelBuff()
            {
                if (eventParams.Count != 2)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}参数长度错误");
                }

                var buffId = eventParams[1];
                if (!buffTable.ContainsKey(buffId))
                {
                    throw new Exception($"任务：{taskId}，{Desc()}非法的buff id");
                }
            }

            void CheckChangePlayerCalories()
            {
                if (eventParams.Count != 3)
                {
                    throw new Exception($"任务：{taskId}，{Desc()}参数长度错误");
                }
            }

            string Desc()
            {
                var text = isBeginEvent ? "开始事件" : "结束事件";
                return $"{text}{eventIndex}";
            }
        }
    }
}
