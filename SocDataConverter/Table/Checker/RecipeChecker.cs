using Soc.Common.CodeParser.Table;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class RecipeChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "dataitem_tbrecipe";
            yield return "dataitem_tbitemconfig";
            yield return "blueprintdata_tbblueprint";
        }

        public bool Check(SheetChecker info)
        {
            var recipeDic = IdKeyData.GetData(info.GetInputInfo(0));
            var itemDic = IdKeyData.GetData(info.GetInputInfo(1));
            var bulePrintDic = IdKeyData.GetData(info.GetInputInfo(2));
            foreach (var (recipeId, recipeData) in recipeDic)
            {
                var ingredients = IdKeyData.GetStringList(recipeData, "ingredients");
                foreach (var ingredient in ingredients)
                {
                    var tempStr = ingredient.Split(',');
                    if (tempStr.Length != 2)
                    {
                        throw new Exception($"配方Id：{recipeId}, 配方材料 格式错误");
                    }
                    var itemId = int.Parse(tempStr[0]);
                    var count = int.Parse(tempStr[1]);
                    if (!itemDic.ContainsKey(itemId))
                    {
                        throw new Exception($"配方Id：{recipeId}, 配方材料：{itemId} 在道具表中不存在");
                    }
                }

                var outcomes = IdKeyData.GetStringList(recipeData, "outcome");
                foreach (var outcome in outcomes)
                {
                    var tempStr = outcome.Split(',');
                    if (tempStr.Length != 2)
                    {
                        throw new Exception($"配方Id：{recipeId}, 转化结果 格式错误");
                    }
                    var itemId = int.Parse(tempStr[0]);
                    var count = int.Parse(tempStr[1]);
                    if (!itemDic.ContainsKey(itemId))
                    {
                        throw new Exception($"配方Id：{recipeId}, 转化结果：{itemId} 在道具表中不存在");
                    }
                }

                var time = IdKeyData.GetIntVal(recipeData, "time");
                if (time <= 0)
                {
                    throw new Exception($"配方Id：{recipeId}, 配方时间：{time} 小于等于0");
                }

                var bluePrintId = IdKeyData.GetIntVal(recipeData, "BluePrintId");
                if (bluePrintId != 0 && !bulePrintDic.ContainsKey(bluePrintId))
                {
                    throw new Exception($"配方Id：{recipeId}, 配方依赖的蓝图{bluePrintId} 在蓝图表不存在");
                }
            }
            return true;
        }
    }
}
