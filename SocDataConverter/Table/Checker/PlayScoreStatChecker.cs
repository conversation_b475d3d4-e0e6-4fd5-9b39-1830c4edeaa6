using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class PlayScoreStatChecker : ISheetChecker
    {
        Dictionary<int, Dictionary<KeyValuePair<int, int>, JObject>> allScoreStat = new();

        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "play_tbscorestat";
            yield return "dataitem_tbitemconfig";
        }

        public bool Check(SheetChecker info)
        {
            var scoreStatDic = IdKeyData.GetData(info.GetInputInfo(0));
            var itemDic = IdKeyData.GetData(info.GetInputInfo(1));

            foreach (var (keyId, data) in scoreStatDic)
            {
                var ruleId = IdKeyData.GetIntVal(data, "scoreRuleID");

                if (!allScoreStat.TryGetValue(ruleId, out var scoreDict))
                {
                    scoreDict = new();
                    allScoreStat[ruleId] = scoreDict;
                }
                
                var statId = IdKeyData.GetIntVal(data, "id");  
                var conditionType = IdKeyData.GetIntVal(data, "conditionType");
                var conditionParam = IdKeyData.GetIntVal(data, "conditionParam");
                var keyPair = new KeyValuePair<int, int>(conditionType, conditionParam);
                if (scoreDict.ContainsKey(keyPair))
                {
                    throw new Exception($"玩法积分表中, 唯一Id：{statId}， 条件类型 {conditionType}," +
                        $"条件参数：{conditionParam}");
                }

                if (!itemDic.ContainsKey(conditionParam))
                {
                    throw new Exception($"玩法积分表中, 唯一Id：{statId}, 条件参数：{conditionParam} 未在道具总表中配置");
                }

                scoreDict.Add(new KeyValuePair<int, int>(IdKeyData.GetIntVal(data, "conditionType"), IdKeyData.GetIntVal(data, "conditionParam")), data);
            }

            return true;
        }
    }
}
