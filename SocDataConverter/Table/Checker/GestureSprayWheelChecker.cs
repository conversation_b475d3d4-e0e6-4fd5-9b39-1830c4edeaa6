using Soc.Common.CodeParser.Table.Intermedia;
using Newtonsoft.Json.Linq;

namespace SocDataConverter.Table.Checker
{
    class GestureSprayWheelChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "dataitem_tbgestureconfig";
            yield return "dataitem_tbsprayconfig";
            yield return "dataitem_tbskinconfig";
            yield return "dataitem_tbcostumeconfig";
            yield return "constraction_tbbuildingcoreskin";
        }

        public bool Check(SheetChecker info)
        {
            var gestureConfigDic = IdKeyData.GetData(info.GetInputInfo(0));
            var sprayConfigDic = IdKeyData.GetData(info.GetInputInfo(1));
            var skinConfigDic = IdKeyData.GetData(info.GetInputInfo(2));
            var costumeConfigDic = IdKeyData.GetData(info.GetInputInfo(3));
            var buildCoreConfigDic = IdKeyData.GetData("Id", info.GetInputInfo(4));
            HashSet<int> ids = new HashSet<int>();
            CheckIdDuplicates("手势", gestureConfigDic);
            CheckIdDuplicates("喷漆", sprayConfigDic);
            CheckIdDuplicates("皮肤", skinConfigDic);
            CheckIdDuplicates("时装", costumeConfigDic);
            CheckIdDuplicates("核心建筑皮肤", buildCoreConfigDic);

            void CheckIdDuplicates(string configName, IDictionary<int, JObject> configDic)
            {
                var duplicateId = configDic.Keys.FirstOrDefault(id => ids.Contains(id));
                if (duplicateId != 0)
                {
                    throw new Exception($"{configName}配置表中id {duplicateId} 不能和其他皮肤配置表中id重复");
                }
                ids.UnionWith(configDic.Keys);
            }

            return true;
        }
    }
}