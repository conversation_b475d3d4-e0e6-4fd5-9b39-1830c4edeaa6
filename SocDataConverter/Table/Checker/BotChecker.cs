using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class BotChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "dataitem_tbitemconfig";
            yield return "bot_tbbotweapon";
        }

        public bool Check(SheetChecker info)
        {
            var itemDict = IdKeyData.GetData(info.GetInputInfo(0));
            var botConfig = IdKeyData.GetData(info.GetInputInfo(1));

            foreach (var (id, item) in botConfig)
            {
                var weaponId = IdKeyData.GetLongVal(item, "weaponId");
                if (!itemDict.ContainsKey((int)weaponId))
                {
                    throw new Exception($"武器bot对照表@123_玩法_BOT总表.xlsx 的道具ID：{weaponId}, 在道具表不存在");
                }
            }

            return true;
        }
    }
}
