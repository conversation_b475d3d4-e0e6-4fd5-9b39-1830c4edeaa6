using Bright.Collections;
using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class SpawnChecker : ISheetChecker
    {
        public const int offset = 2;
        //2 - TreeEntity
        //3 - OreEntity 
        //4 - CollectableEntity 
        //5 - CorpseEntity
        //6 - ItemEntity
        //7 - MonsterEntity
        //8 - BoxEntity
        //9 - IOEntity
        //10 - ShopEntity
        //11 - PartEntity
        //12 - ElevatorEntity
        //13 - InteractionEntity
        //14 - KatyushaEntity
        //15 - CarShredderEntity
        //16 - TrapEntity
        //17 - TrainCarEntity 
        //18 - HorseEntity
        //19 - DigEntity
        //20 - NPCEntity
        //21 - ModularCarEntity
        //22 - TempCofferEntity
        //23 - BonusRocketEntity
        //24 - CaveLiftEntity
        //25 - TrainCarEntity -- 火车组

        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "spawn_tbspawnruleconfig";
            yield return "spawn_tbspawnstationconfig";
            yield return "monument_tbmonumentarea";
            yield return "units_tbgatherresourcestree";
            yield return "units_tbgatherresourcesore";
            yield return "units_tbgatherresourcescollectable";
            yield return "units_tbcorpse";
            yield return "dataitem_tbitemconfig";
            yield return "units_tbmonster";
            yield return "units_tbtreasurebox";
            yield return "units_tbiointeractive";
            yield return "item_tbshop";
            yield return "constraction_tbbuildingcore";
            yield return "units_tbelevatorinteractive";
            yield return "units_tbelevatorinteractivecomponent";
            yield return "units_tbkatyusha";
            yield return "units_tbcarshredder";
            yield return "units_tbtrap";
            yield return "play_tbtraincarproperty";
            yield return "vehicle_tbhorsebreedinfo";
            yield return "play_tbdigproperty";
            yield return "units_tbnpc";
            yield return "vehicle_tbmodularcar";
            yield return "play_tbtempcoffer";
            yield return "surpriseplay_tbbonusrocketconfig";
            yield return "units_tbinteractobj";
            yield return "units_tbcaveliftconfig";
            yield return "play_tbtraincarcomponents";
        }

        private bool CheckElementInner(JToken infos, long templateId, string entityType)
        {
            try
            {
                bool find = false;
                foreach (var data in infos)
                {
                    JObject singleData = (JObject)data;
                    var id = 0L;
                    if (entityType == "PartEntity")
                    {
                        id = (long)singleData["partId"];
                    }
                    else if (entityType == "KatyushaEntity")
                    {
                        id = (long)singleData["vehicleTemplateId"];
                    }
                    else if (entityType == "TrapEntity")
                    {
                        id = (long)singleData["trapID"];
                    }
                    else if (entityType == "CarshredderEntity")
                    {
                        id = (long)singleData["carShredderID"];
                    }
                    else if (entityType == "HorseEntity")
                    {
                        id = (long)singleData["breedId"];
                    }
                    else if (entityType == "ElevatorEntity")
                    {
                        if (singleData.ContainsKey("elevatorTemplateId"))
                        {
                            id = (long)singleData["elevatorTemplateId"];
                        }
                        else if (singleData.ContainsKey("componentTemplateId"))
                        {
                            id = (long)singleData["componentTemplateId"];
                        }
                    }
                    else
                    {
                        id = (long)singleData["id"];
                    }

                    if (id == templateId || templateId == 0)
                    {
                        find = true;
                        break;
                    }
                }

                return find;
            }
            catch (Exception ex)
            {

                throw new Exception($"这里刷新配置检查, 发生错误请联系关卡组策划同学检查配置, 错误信息如下：, ex:{ex}");
            }
        }

        private bool CheckElementInner(SheetChecker info, long templateId, string entityType, bool isSpawnRule = false)
        {
            try
            {
                var ret = false;
                switch (entityType)
                {
                    case "TreeEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 1).InputJson, templateId, entityType);
                        break;
                    case "OreEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 2).InputJson, templateId, entityType);
                        break;
                    case "CollectableEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 3).InputJson, templateId, entityType);
                        break;
                    case "CorpseEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 4).InputJson, templateId, entityType);
                        break;
                    case "ItemEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 5).InputJson, templateId, entityType);
                        break;
                    case "MonsterEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 6).InputJson, templateId, entityType);
                        break;
                    case "BoxEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 7).InputJson, templateId, entityType);
                        break;
                    case "IOEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 8).InputJson, templateId, entityType);
                        break;
                    case "ShopEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 9).InputJson, templateId, entityType);
                        break;
                    case "PartEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 10).InputJson, templateId, entityType);
                        break;
                    case "ElevatorEntity":
                        var tmpInfo = new JArray();
                        tmpInfo.AddRange(info.GetInputInfo(offset + 11).InputJson);
                        tmpInfo.AddRange(info.GetInputInfo(offset + 12).InputJson);
                        ret = CheckElementInner(tmpInfo, templateId, entityType);
                        break;
                    case "InteractionEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 23).InputJson, templateId, entityType);
                        break;
                    case "KatyushaEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 13).InputJson, templateId, entityType);
                        break;
                    case "CarshredderEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 14).InputJson, templateId, entityType);
                        break;
                    case "TrapEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 15).InputJson, templateId, entityType);
                        break;
                    case "TrainCarEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + (isSpawnRule ? 25 : 16)).InputJson, templateId, entityType);
                        break;
                    case "TrainBarricadeEntity":
                        ret = true;
                        break;
                    case "VehicleEntity":
                        ret = true;
                        break;
                    case "HorseEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 17).InputJson, templateId, entityType);
                        break;
                    case "DigEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 18).InputJson, templateId, entityType);
                        break;
                    case "NPCEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 19).InputJson, templateId, entityType);
                        break;
                    case "MonumentEntity":
                        ret = true;
                        break;
                    case "ModularCarEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 20).InputJson, templateId, entityType);
                        break;
                    case "TempCofferEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 21).InputJson, templateId, entityType);
                        break;
                    case "BonusRocketEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 22).InputJson, templateId, entityType);
                        break;
                    case "CaveLiftEntity":
                        ret = CheckElementInner(info.GetInputInfo(offset + 24).InputJson, templateId, entityType);
                        break;
                    default:
                        return false;
                }
                return ret;
            }
            catch (Exception ex)
            {
                throw new Exception($"这里刷新配置检查, 发生错误请联系关卡组策划同学检查配置, 错误信息如下：, ex:{ex}");
            }
        }


        private bool CheckElement(SheetChecker info, JArray elements, string entityType, long groupId)
        {
            var ret = true;
            foreach (var element in elements)
            {
                JObject elementData = (JObject)element;

                var templateId = (long)elementData["templateId"];
                ret = CheckElementInner(info, templateId, entityType);
                if (!ret)
                {
                    throw new Exception($"这里刷新配置检查, 发生错误请联系关卡组策划同学检查配置, 错误信息如下：, groupId:{groupId}, templateId:{templateId}, entityType:{entityType}");
                }
            }

            return true;
        }

        public bool Check(SheetChecker info)
        {
            var spawnRules = IdKeyData.GetData(info.GetInputInfo(0));

            foreach (var (keyId, rule) in spawnRules)
            {
                var elements = IdKeyData.GetIntList(rule, "elementIds");
                var elementBounds = rule["elementbounds"] as JArray;
                if (elements.Count != elementBounds.Count)
                {
                    throw new Exception($"单位刷新规则表, ruleId：{keyId}, elements:{elements.Count}, elementbounds {elementBounds.Count}");
                }
                var elementWeights = rule["elementWeight"] as JArray;
                if (elementWeights.Count > 0 && elements.Count != elementWeights.Count)
                {
                    throw new Exception($"单位刷新规则表, ruleId：{keyId}, elements:{elements.Count}, elementWeight {elementWeights.Count}");
                }
                var scaleRange = IdKeyData.GetFloatList(rule, "scaleRange");
                if (scaleRange[0] > scaleRange[1])
                {
                    throw new Exception($"单位刷新规则表, ruleId：{keyId}, min:{scaleRange[0]} is large than max{scaleRange[1]}");
                }

                var devRange = IdKeyData.GetFloatList(rule, "yAxisDevRange");
                if (devRange[0] > devRange[1])
                {
                    throw new Exception($"单位刷新规则表, ruleId：{keyId}, min:{devRange[0]} is large than max{devRange[1]}");
                }

                var heightRange = IdKeyData.GetFloatList(rule, "heightLimit");
                if (heightRange[0] > heightRange[1])
                {
                    throw new Exception($"单位刷新规则表, ruleId：{keyId}, min:{heightRange[0]} is large than max{heightRange[1]}");
                }

                foreach (var element in elements)
                {
                    var ret = CheckElementInner(info, element, (string)rule["type"], true);
                    if (!ret)
                    {
                        throw new Exception($"这里刷新配置检查, 发生错误请联系关卡组策划同学检查配置, 错误信息如下：, groupId:{keyId}, templateId:{element}, entityType:{rule["type"]}");
                    }
                }
            }

            var infos = info.GetInputInfo(1).InputJson;
            HashSet<long> records = new();
            foreach (var data in infos)
            {
                JObject singleData = (JObject)data;
                var singleGroups = singleData["groups"] as JArray;
                foreach (var group in singleGroups)
                {
                    JObject tmp = (JObject)group;
                    var id = long.Parse(tmp["id"].ToString());
                    if (records.Contains(id))
                    {
                        throw new Exception($"关卡编辑器, monument:{singleData["name"]}, name:{tmp["name"]} groupId:{id} 重复");
                    }


                    var points = tmp["points"] as JArray;
                    if (points.Count == 0)
                    {
                        throw new Exception($"关卡编辑器, monument:{singleData["name"]}, name:{tmp["name"]} groupId:{id} 点位组为空");
                    }


                    var tierGroup = tmp["tierGroups"] as JArray;
                    foreach (var tgroup in tierGroup)
                    {
                        JObject tmptGroup = (JObject)tgroup;
                        var tickMin = (int)tmptGroup["tickMin"];
                        var tickMax = (int)tmptGroup["tickMax"];
                        if ((tickMin < 0 || tickMax < 0 || tickMax < tickMin))
                        {
                            throw new Exception($"关卡编辑器, monument:{singleData["name"]}, name:{tmp["name"]} groupId:{id}, tick:[{tickMin},{tickMax}] tickMax 应该大于等于 tickMin 配置项错误");
                        }
                        if (tickMin > points.Count)
                        {
                            throw new Exception($"关卡编辑器, monument:{singleData["name"]}, name:{tmp["name"]} groupId:{id},tickMin:{tickMin} <= points.count:{points.Count} 点位不足");
                        }

                        var delayMin = (int)tmptGroup["delayMin"];
                        var delayMax = (int)tmptGroup["delayMax"];

                        if ((delayMin == 0 || delayMax == 0 || delayMax < delayMin))
                        {
                            throw new Exception($"关卡编辑器, monument:{singleData["name"]}, name:{tmp["name"]} groupId:{id}, delay:[{delayMin},{delayMax}] 配置项错误");
                        }

                        var maxCount = (int)tmptGroup["maxCount"];
                        if (maxCount == 0 && (int)tmptGroup["tickMin"] > 0)
                        {
                            throw new Exception($"关卡编辑器, monument:{singleData["name"]}, name:{tmp["name"]} groupId:{id}, maxCount:{maxCount} 配置项错误");
                        }

                        var elements = tmptGroup["elements"] as JArray;
                        var entityType = (string)tmptGroup["entityType"];
                        CheckElement(info, elements, entityType, (long)tmptGroup["id"]);
                    }

                    records.Add(id);
                }
            }

            var areaInfos = info.GetInputInfo(2).InputJson;
            foreach (var data in areaInfos)
            {
                JObject singleData = (JObject)data;
                var monumentAreaVolumes = singleData["monumentAreaVolumes"];
                var monumentAreaVolumesList = monumentAreaVolumes as JArray;
                if (monumentAreaVolumesList.Count != 0)
                {
                    JObject areaData = (JObject)monumentAreaVolumesList[0];
                    if ((long)areaData["resetMin"] == 0 && (long)areaData["resetMax"] == 0) continue;
                    if (((long)areaData["resetMin"] < 0 || (long)areaData["resetMax"] < 0) ||
                        ((long)areaData["resetMax"] <= (long)areaData["resetMin"]))
                    {
                        throw new Exception($"单位刷新区域表, monument:{singleData["name"]}, name:{areaData["name"]} resetMin:{areaData["resetMin"]} resetMax:{areaData["resetMax"]}");
                    }
                }
            }
            return true;
        }
    }
}
