using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    public enum ESkinType
    {
        Weapon = 1,
        Armor = 2,
        Vehicle = 3,
        Furniture = 4,
        Building = 5,
        Door = 6,
        Costome = 7,
        Equipment = 8,
        Gesture = 9,
        Spray = 10,
    }

    internal class LobbySkinChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "resource_tbskin";
            yield return "dataitem_tbskinconfig";
            yield return "constraction_tbbuildingcoreskin";
            yield return "dataitem_tbcostumeconfig";
            yield return "dataitem_tbgestureconfig";
            yield return "dataitem_tbsprayconfig";
            yield return "dataitem_tbitemconfig";
        }

        public bool Check(SheetChecker info)
        {
            var lobbySkinConfigDict = IdKeyData.GetData("ID", info.GetInputInfo(0));
            var itemSkinConfigDict = IdKeyData.GetData(info.GetInputInfo(1));
            var buildCoreConfigDict = IdKeyData.GetData("Id", info.GetInputInfo(2));
            var costumeConfigDict = IdKeyData.GetData(info.GetInputInfo(3));
            var gestureConfigDict = IdKeyData.GetData(info.GetInputInfo(4));
            var sprayConfigDict = IdKeyData.GetData(info.GetInputInfo(5));
            var itemConfigDict = IdKeyData.GetData(info.GetInputInfo(6));
            foreach (var (resId, resConfig) in lobbySkinConfigDict)
            {
                var defaultSkin = IdKeyData.GetIntVal(resConfig, "DefaultSkin");
                if (defaultSkin == 1) continue;
                var skinType = IdKeyData.GetIntVal(resConfig, "SkinType");
                switch (skinType)
                {
                    case (int)ESkinType.Weapon:
                    case (int)ESkinType.Armor:
                    case (int)ESkinType.Furniture:
                    case (int)ESkinType.Door:
                    case (int)ESkinType.Equipment:
                        {
                            if (!itemSkinConfigDict.TryGetValue(resId, out var _) && !itemConfigDict.TryGetValue(resId, out var _))
                            {
                                throw new Exception($"大厅资源分表—皮肤中的：{resId}，皮肤类型：{skinType}，皮肤ID：{resId} 在局内皮肤表中不存在");
                            }
                            break;
                        }
                    case (int)ESkinType.Building:
                        {
                            if (!buildCoreConfigDict.TryGetValue(resId, out var _) && !itemConfigDict.TryGetValue(resId, out var _))
                            {
                                throw new Exception($"大厅资源分表—皮肤中的：{resId}，皮肤类型：{skinType}，皮肤ID：{resId} 在建筑核心皮肤表中不存在");
                            }
                            break;
                        }
                    case (int)ESkinType.Costome:
                        {
                            if (!costumeConfigDict.TryGetValue(resId, out var _))
                            {
                                Console.WriteLine($"大厅资源分表—皮肤中的：{resId}，皮肤类型：{skinType}，皮肤ID：{resId} 在时装表中不存在");
                                //throw new Exception($"大厅资源分表—皮肤中的：{resId}，皮肤类型：{skinType}，皮肤ID：{resId} 在时装表中不存在");
                            }
                            break;
                        }
                    case (int)ESkinType.Gesture:
                        {
                            if (!gestureConfigDict.TryGetValue(resId, out var _))
                            {
                                throw new Exception($"大厅资源分表—皮肤中的：{resId}，皮肤类型：{skinType}，皮肤ID：{resId} 在手势表中不存在");
                            }
                            break;
                        }
                    case (int)ESkinType.Spray:
                        {
                            if (!sprayConfigDict.TryGetValue(resId, out var _))
                            {
                                throw new Exception($"大厅资源分表—皮肤中的：{resId}，皮肤类型：{skinType}，皮肤ID：{resId} 在喷漆表中不存在");
                            }
                            break;
                        }
                }
            }

            foreach (var (skinId, _) in itemSkinConfigDict)
            {
                if (!lobbySkinConfigDict.TryGetValue(skinId, out var _))
                {
                    Console.WriteLine($"局内皮肤表中的：{skinId}，在大厅资源分表—皮肤中不存在");
                }
            }

            foreach (var (buildSkinId, _) in buildCoreConfigDict)
            {
                if (!lobbySkinConfigDict.TryGetValue(buildSkinId, out var _))
                {
                    throw new Exception($"建筑核心皮肤表中的：{buildSkinId}，在大厅资源分表—皮肤中不存在");
                }
            }

            foreach (var (costumeId, _) in costumeConfigDict)
            {
                if (!lobbySkinConfigDict.TryGetValue(costumeId, out var _))
                {
                    throw new Exception($"时装表中的：{costumeId}，在大厅资源分表—皮肤中不存在");
                }
            }

            foreach (var (gestureId, _) in gestureConfigDict)
            {
                if (!lobbySkinConfigDict.TryGetValue(gestureId, out var _))
                {
                    throw new Exception($"手势表中的：{gestureId}，在大厅资源分表—皮肤中不存在");
                }
            }

            foreach (var (sprayId, _) in sprayConfigDict)
            {
                if (!lobbySkinConfigDict.TryGetValue(sprayId, out var _))
                {
                    throw new Exception($"喷漆表中的：{sprayId}，在大厅资源分表—皮肤中不存在");
                }
            }

            return true;
        }
    }
}
