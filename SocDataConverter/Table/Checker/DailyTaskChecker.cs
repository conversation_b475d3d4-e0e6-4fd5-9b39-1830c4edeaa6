using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class DailyTaskChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "play_tbdailytask";
            yield return "play_tbquestphase";
        }

        public bool Check(SheetChecker info)
        {
            var dailyTaskConfigDic = IdKeyData.GetData(info.GetInputInfo(0));
            var taskConfigDic = IdKeyData.GetData(info.GetInputInfo(1));

            foreach (var (id, dailyTaskConfig) in dailyTaskConfigDic)
            {
                if (!taskConfigDic.ContainsKey(id))
                {
                    throw new Exception($"任务配置表中不存在日常任务id {id}");
                }
            }

            return true;
        }
    }
}
