using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table.Intermedia;
using System.Linq;

namespace SocDataConverter.Table.Checker
{
    internal class ItemDropChecker : ISheetChecker
    {
        private readonly HashSet<long> groupIds = new();

        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "itemreward_tbitemdrop";
            yield return "itemreward_tbitemdropparameters";
            yield return "gun_tbgunbase";
        }

        public bool Check(SheetChecker info)
        {
            var dropDic = IdKeyData.GetData(info.GetInputInfo(0));
            var dropParamDic = IdKeyData.GetData(info.GetInputInfo(1));
            var gunBaseDic = IdKeyData.GetData("gunid", info.GetInputInfo(2));
            foreach (var (dropId, dropData) in dropDic)
            {
                // 所有掉落id
                if (dropData.TryGetValue("rewards", out var rewards))
                {
                    // 一个掉落 多个掉落组
                    var rewardsList = rewards as JArray;
                    groupIds.Clear();
                    foreach (var reward in rewardsList)
                    {
                        // 这里是一个掉落组
                        var rewardDic = reward as JObject;
                        // 这个检查一个掉落里面的掉落组id不能重复
                        var id = (long)rewardDic["id"];
                        if (groupIds.Contains(id))
                        {
                            throw new Exception($"[掉落表里掉落组id重复了] dropId {dropId} has duplicate groupId {id}");
                        }
                        groupIds.Add(id);

                        var dropTimes = (long)rewardDic["dropTimes"];
                        //var dropTimes = rewardDic.TryGetValue("dropTimes", out var dts) ? (long)dts : 1;// [ALB] Tags测试beans
                        if (dropTimes <= 0)
                        {
                            throw new Exception($"[掉落表里掉落组掉落次数小于等于0] dropId {dropId} groupId {id} dropTimes {dropTimes}");
                        }

                        var dropType = (long)rewardDic["dropType"];

                        var dropRewards = rewardDic["dropRewards"] as JArray;
                        // 对于不可放回的掉落，掉落次数不能大于掉落组里的掉落奖励数量
                        if (dropType == 2 && dropTimes > dropRewards.Count)
                        {
                            throw new Exception($"[掉落表里不可放回的掉落，掉落次数大于掉落组里的掉落奖励数量] dropId {dropId} groupId {id} dropTimes {dropTimes} dropRewardsCount {dropRewards.Count}");
                        }
                        foreach (var item in dropRewards)
                        {
                            var itemDic = item as JObject;
                            var itemId = IdKeyData.GetIntVal(itemDic, "ItemID");
                            var dropNumLower = (long)itemDic["DropNumLower"];
                            var dropNumUpper = (long)itemDic["DropNumUpper"];
                            if (dropNumLower < 0 || dropNumUpper < 0)
                            {
                                throw new Exception($"[掉落表里掉落组里的掉落奖励数量小于0] dropId {dropId} groupId {id} dropNumLower {dropNumLower} dropNumUpper {dropNumUpper}");
                            }
                            if (dropNumLower > dropNumUpper)
                            {
                                throw new Exception($"[掉落表里掉落组里的掉落奖励数量范围错误] dropId {dropId} groupId {id} dropNumLower {dropNumLower} dropNumUpper {dropNumUpper}");
                            }
                            var allowGlobalItem = (int)itemDic["AllowGlobalItem"];
                            if (allowGlobalItem != 0 && allowGlobalItem != 1)
                            {
                                throw new Exception($"[掉落表里掉落组里的掉落奖励是否允许全局物品错误] dropId {dropId} groupId {id} allowGlobalItem {allowGlobalItem}");
                            }
                            var itemParam = (int)itemDic["DropParameters"];
                            if (itemParam != 0 && !dropParamDic.TryGetValue(itemParam, out var _))
                            {
                                throw new Exception($"[掉落表里掉落组里的掉落参数错误] dropId {dropId} groupId {id} itemParam {itemParam} not found in drop parameters");
                            }

                            if (dropParamDic.TryGetValue(itemParam, out var paramConfig))
                            {
                                var dropBulletNumLower = IdKeyData.GetIntVal(paramConfig, "DropBulletNumLower");
                                var dropBulletNumUpper = IdKeyData.GetIntVal(paramConfig, "DropBulletNumUpper");
                                if (dropBulletNumLower < 0 || dropBulletNumUpper < 0)
                                {
                                    throw new Exception($"[掉落表里掉落组里的掉落参数的子弹数量小于0] dropId {dropId} groupId {id} dropBulletNumLower {dropBulletNumLower} dropBulletNumUpper {dropBulletNumUpper}");
                                }
                                if (dropBulletNumLower > dropBulletNumUpper)
                                {
                                    throw new Exception($"[掉落表里掉落组里的掉落参数的子弹数量范围错误] dropId {dropId} groupId {id} dropBulletNumLower {dropBulletNumLower} dropBulletNumUpper {dropBulletNumUpper}");
                                }

                                var dropBullet = IdKeyData.GetIntVal(paramConfig, "DropBullet");
                                if (gunBaseDic.TryGetValue(itemId, out var gunConfig))
                                {
                                    var availablebullet = IdKeyData.GetIntList(gunConfig, "availablebullet");
                                    if (dropBullet > 0 && !availablebullet.Contains(dropBullet))
                                    {
                                        throw new Exception($"[掉落表里掉落组里的掉落参数的子弹类型错误] dropId {dropId} groupId {id} itemId {itemId} dropBullet {dropBullet} not in availablebullet {string.Join(",", availablebullet)}");
                                    }

                                    var cartridge = IdKeyData.GetIntVal(gunConfig, "cartridge");
                                    if (dropBulletNumUpper > cartridge)
                                    {
                                        throw new Exception($"[掉落表里掉落组里的掉落参数的子弹数量大于枪械弹匣容量] dropId {dropId} groupId {id} itemId {itemId} dropBulletNumUpper {dropBulletNumUpper} cartridge {cartridge}");
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    throw new Exception($"[掉落表里没有rewards] dropId {dropId}");
                }
            }

            return true;
        }
    }

    internal class ItemDropLimitChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "itemreward_tbitemdroplimit";
        }

        public bool Check(SheetChecker info)
        {
            var dropDic = IdKeyData.GetData(info.GetInputInfo(0));
            foreach (var (dropId, dropData) in dropDic)
            {
                if (dropData.TryGetValue("limitNum", out var limitNumObj))
                {
                    var limitNum = (long)limitNumObj;
                    if (limitNum <= 0)
                    {
                        throw new Exception($"[掉落表里limitNum小于等于0] dropId {dropId} limitNum {limitNum}");
                    }
                }
                else
                {
                    throw new Exception($"[掉落表里没有limitNum] dropId {dropId}");
                }
            }
            return true;
        }
    }
}
