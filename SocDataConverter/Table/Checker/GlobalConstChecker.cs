using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class GlobalConstChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "global_tbglobalconfig";
        }

        public bool Check(SheetChecker info)
        {
            var inputJson = info.GetInputInfo(0).InputJson;

            foreach (var data in inputJson)
            {
                var jObject = data as JObject;
               
                var spawnHorseAroundPlayerRadius = IdKeyData.GetIntList(jObject, "SpawnHorseAroundPlayerRadius");
                if(!(spawnHorseAroundPlayerRadius.Count== 2 && spawnHorseAroundPlayerRadius[1] > spawnHorseAroundPlayerRadius[0]))
                {
                    throw new Exception($"02_全局配置表：大世界补充玩家周围马的半径  外环 {spawnHorseAroundPlayerRadius[1]} 必须大于 内环 {spawnHorseAroundPlayerRadius[0]}");
                }

                var worldSpawnRange = IdKeyData.GetIntVal(jObject, "WorldSpawnRange");
                if (spawnHorseAroundPlayerRadius[0] < worldSpawnRange)
                {
                    throw new Exception($"02_全局配置表：大世界补充玩家周围马的半径内环 {spawnHorseAroundPlayerRadius[0]} 必须大于 大世界生成检查范围 {worldSpawnRange}");
                }

                var SpawnTrainCountInSplie_3x3 = IdKeyData.GetIntList(jObject, "SpawnTrainCountInSplie_3x3");
                if (!(SpawnTrainCountInSplie_3x3.Count == 2 && SpawnTrainCountInSplie_3x3[1] >= SpawnTrainCountInSplie_3x3[0]))
                {
                    throw new Exception($"02_全局配置表：3x3大世界铁路岔口火车刷新数量区间  上限 {SpawnTrainCountInSplie_3x3[1]} 必须大于等于 下限 {SpawnTrainCountInSplie_3x3[0]}");
                }


                var SpawnTrainCountInSplie_4x4 = IdKeyData.GetIntList(jObject, "SpawnTrainCountInSplie_4x4");
                if (!(SpawnTrainCountInSplie_4x4.Count == 2 && SpawnTrainCountInSplie_4x4[1] >= SpawnTrainCountInSplie_4x4[0]))
                {
                    throw new Exception($"02_全局配置表：4x4大世界铁路岔口火车刷新数量区间  上限 {SpawnTrainCountInSplie_4x4[1]} 必须大于等于 下限 {SpawnTrainCountInSplie_4x4[0]}");
                }
            }
            return true;
        }
    }
}
