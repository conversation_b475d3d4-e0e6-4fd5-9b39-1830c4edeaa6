using Newtonsoft.Json.Linq;
using Soc.Common.CodeParser.Table.Intermedia;

namespace SocDataConverter.Table.Checker
{
    internal class PackagesChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "dataitem_tbitempackages";
            yield return "itemreward_tbitemdrop";
            yield return "dataitem_tbitemconfig";
        }

        public bool Check(SheetChecker info)
        {
            var packagesConfigDic = IdKeyData.GetData(info.GetInputInfo(0));
            var dropConfigDic = IdKeyData.GetData(info.GetInputInfo(1));
            var itemConfigConfigDic = IdKeyData.GetData(info.GetInputInfo(2));

            foreach (var (_, packagesConfig) in packagesConfigDic)
            {
                var id = IdKeyData.GetLongVal(packagesConfig, "id");
                if (!itemConfigConfigDic.ContainsKey((int)id))
                {
                    throw new Exception($"道具配置表中不存在礼包id {id}");
                }

                var emptyCell = IdKeyData.GetIntVal(packagesConfig, "emptyCell");
                if (emptyCell <= 0)
                {
                    throw new Exception($"礼包id：{id} 所需空格子数量：{emptyCell} 必须大于0");
                }

                var dropIdList = IdKeyData.GetIntList(packagesConfig, "dropIDList");
                if (null == dropIdList || dropIdList.Count <= 0)
                {
                    throw new Exception($"礼包id：{id} 掉落id组配置错误");
                }

                var optional = IdKeyData.GetIntVal(packagesConfig, "optional");
                long finalDropTimes = 0;
                foreach (var dropId in dropIdList)
                {
                    if (!dropConfigDic.TryGetValue(dropId, out var dropData))
                    {
                        throw new Exception($"礼包id：{id}, 掉落配置表中不存在掉落id {dropId}");
                    }

                    // 所有掉落id
                    if (!dropData.TryGetValue("rewards", out var rewards))
                    {
                        throw new Exception($"礼包id：{id}, 掉落id:{dropId}, 奖励配置错误");
                    }

                    var rewardsList = rewards as JArray;
                    foreach (var reward in rewardsList)
                    {
                        // 这里是一个掉落组
                        var rewardDic = reward as JObject;
                        var dropTimes = (long)rewardDic["dropTimes"];
                        //var dropTimes = rewardDic.TryGetValue("dropTimes", out var dts) ? (long)dts : emptyCell; // [ALB] Tags测试beans
                        if (optional == 1)
                        {
                            if (dropTimes != emptyCell)
                            {
                                throw new Exception($"礼包id：{id}, 可选掉落id:{dropId} dropTimes {dropTimes}和空格数量：{emptyCell}不一致");
                            }
                        }
                        else
                        {
                            finalDropTimes += dropTimes;
                        }
                    }
                }

                if (optional == 0 && finalDropTimes != emptyCell)
                {
                    throw new Exception($"礼包id：{id}, dropTimes {finalDropTimes}和空格数量：{emptyCell}不一致");
                }
            }
            return true;
        }
    }
}
