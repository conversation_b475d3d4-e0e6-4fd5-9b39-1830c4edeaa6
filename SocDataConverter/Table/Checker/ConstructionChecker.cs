namespace SocDataConverter.Table.Checker
{
    internal class ConstructionConstantChecker : ISheetChecker
    {
        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "constraction_tbconstructionconstantconfig";
        }

        public bool Check(SheetChecker info)
        {
            var inputJson = info.GetInputInfo(0).InputJson;
            if (inputJson.Count != 1)
            {
                throw new Exception("ConstructionConstantChecker inputJson.Count != 1");
            }
            var data = inputJson[0];

            var TerrRepairPeriod = data["TerrRepairPeriod"].ToObject<int>();
            if (TerrRepairPeriod <= 0 || TerrRepairPeriod > 36000)
            {
                throw new Exception($"TerrRepairPeriod {TerrRepairPeriod} not in [1, 36000]");
            }

            var UpgradeNumPerSecond = data["UpgradeNumPerSecond"].ToObject<int>();
            if (UpgradeNumPerSecond <= 0 || UpgradeNumPerSecond > 300)
            {
                throw new Exception($"UpgradeNumPerSecond {UpgradeNumPerSecond} not in [1,200]");
            }
            
            var wardrobeSlotNum = data["WardrobeSlotNum"].ToObject<int>();
            if (wardrobeSlotNum <= 0 || wardrobeSlotNum > 10)
            {
                throw new Exception($"WardrobeSlotNum {wardrobeSlotNum} not in [1,10]");
            }
            return true;
        }
    }

    internal class ConstructionCountLimitChecker : ISheetChecker
    {
        /*
            1=建筑核心
            2=建造类摆件
            3=普通摆件
            4=锁类摆件
         */
        public const int PART_CORE = 1;

        //partType, (ownCountLimit, countLimit, maxBuildWild, EntityGroupId)
        private Dictionary<long, (int, int, int, int)> partCountLimitDetail = new();

        private Dictionary<int, HashSet<long>> partGroupInfo = new();

        //EntityGroupId, (List<partType>, ownCountLimit, MaxGroupEntityTerr, MaxGroupEntityWild)
        private Dictionary<int, (int, int, int)> partGroupLimitInfo = new();

        private Dictionary<long, int> partTypeInfo = new();

        public IEnumerable<string> GetInteresetTableNames()
        {
            yield return "constraction_tbbuildingcore";
            yield return "global_tbpartgroup";
        }

        public bool Check(SheetChecker info)
        {
            // 建筑组
            var groupJson = info.GetInputInfo(1).InputJson;
            foreach (var data in groupJson)
            {
                var id = data["Id"].ToObject<int>();
                var MaxGroupEntityTerr = data["MaxGroupEntityTerr"].ToObject<int>();
                var MaxGroupEntityWild = data["MaxGroupEntityWild"].ToObject<int>();
                var ownCountLimit = data["ownCountLimit"].ToObject<int>();

                // 个人限制==领地或野外限制
                if (!CheckOnlyOneLimit(ownCountLimit, MaxGroupEntityTerr, MaxGroupEntityWild))
                {
                    throw new Exception($"EntityGroupId:{id} ownCountLimit > 0 && (MaxGroupEntityTerr > 0 || MaxGroupEntityWild > 0)");
                }
                partGroupLimitInfo.Add(id, (ownCountLimit, MaxGroupEntityTerr, MaxGroupEntityWild));
            }

            // 单个建筑
            var inputJson = info.GetInputInfo(0).InputJson;
            foreach (var data in inputJson)
            {
                var type = data["type"].ToObject<int>();
                var partId = data["partId"].ToObject<long>();
                var ownCountLimit = data["ownCountLimit"].ToObject<int>();
                var countLimit = data["countLimit"].ToObject<int>();
                var maxBuildWild = data["maxBuildWild"].ToObject<int>();
                var EntityGroupId = data["EntityGroupId"].ToObject<int>();
                partCountLimitDetail.Add(partId, (ownCountLimit, countLimit, maxBuildWild, EntityGroupId));
                partGroupInfo.TryGetValue(EntityGroupId, out var partIds);
                partIds ??= new();
                partIds.Add(partId);

                partTypeInfo.Add(partId, type);
                // 个人限制==领地或野外限制
                if (!CheckOnlyOneLimit(ownCountLimit, countLimit, maxBuildWild))
                {
                    throw new Exception($"partId:{partId} ownCountLimit > 0 && (countLimit > 0 || maxBuildWild > 0)");
                }
                if (EntityGroupId > 0)
                {
                    //if (ownCountLimit > 0 || countLimit > 0 || maxBuildWild > 0)
                    //{
                    //    throw new Exception($"partId:{partId} EntityGroupId > 0 && (ownCountLimit > 0 || countLimit > 0 || maxBuildWild > 0)");
                    //}

                    if (partGroupLimitInfo.TryGetValue(EntityGroupId, out var groupInfo))
                    {
                        // 核心建筑不能有个人限制
                        if (type == PART_CORE)
                        {
                            if (groupInfo.Item1 != 0)
                            {
                                throw new Exception($"partId:{partId} type == PART_CORE EntityGroupId > 0 && groupInfo.ownCountLimit != 0");
                            }
                        }
                    }
                    else
                    {
                        throw new Exception($"partId:{partId} EntityGroupId > 0 && groupInfo == null");
                    }
                }
                else
                {
                    // 核心建筑不能有个人限制
                    if (type == PART_CORE)
                    {
                        if (ownCountLimit != 0)
                        {
                            throw new Exception($"partId:{partId} type == PART_CORE && ownCountLimit != 0");
                        }
                    }
                }

            }

            return true;
        }

        private bool CheckOnlyOneLimit(int ownLimit, int terrLimit, int wildLimit)
        {
            if (ownLimit > 0)
            {
                if (terrLimit > 0 || wildLimit > 0)
                {
                    return false;
                }
            }
            return true;
        }
    }
}
