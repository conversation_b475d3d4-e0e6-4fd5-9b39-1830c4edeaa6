
namespace WizardGames.Soc.Common.Entity
{
    public static class LifeFlags
    {
        public const int Init = 0;
        public const int Alive = 1 << 0; // 1
        public const int Died = 1 << 1; // 2
        public const int Wounded = 1 << 2; // 4
        public const int Incapacitated = 1 << 3; // 8
        public const int Reborning = 1 << 4; // 16
        public const int WaitDied = 1 << 5; // 32
        public const int DyingEnd = 1 << 6; // 64
        public const int ForbidSelfAid = 1 << 7; // 128
        public const int BeLooting = 1 << 8; // 256 被掠夺中
        public const int InCampingTent = 1 << 9;
    }

    /// <summary>
    /// 任务关联类型，与PlayerEntity.InterestedEvents按位与
    /// </summary>
    public static class InterestedEventFlags
    {
        public const int CauseDamage = 1 << 0;
        public const int Kill = 1 << 1;
        public const int Heal = 1 << 2;
    }

    // 重装套和防水服用player上的int值计数

    /// <summary>
    /// 装备开关枚举，用于判断各个位置装备的特殊功能是否开启
    /// </summary>
    public static class SwitchableEquip
    {
        public const int Head = 0;
        public const int Eye = 1;
        public const int Jaw = 2;
        public const int UpperArmor = 3;
        public const int Shirt = 4;
        public const int Glove = 5;
        public const int Pants = 6;
        public const int LowerArmor = 7;
        public const int Shoe = 8;
        public const int Bag = 9;

        public static bool IsLightableHat(long id)
        {
            if (id is EquipTableIds.MinerHat or EquipTableIds.CandleHat or EquipTableIds.NightVision)
            {
                return true;
            }
            return false;
        }
    }

    /// <summary>
    /// 特殊的装备id
    /// </summary>
    public static class EquipTableIds
    {
        public const long MinerHat = 13010026; //矿工帽
        public const long CandleHat = 13010049;//蜡烛帽
        public const long Sunglasses = 13010002;//太阳镜
        public const long Divingmask = 13010030;//潜水镜
        public const long NightVision = 13010052;//夜视仪
        public const long HeavyDutyHelmets = 13010046;//重型盔甲
    }

    public static class EntityConst
    {
        public const long UnDefineId = -1;

        // public const long EnvironmentId = 0;

        public static bool IsEnvironment(long envId)
        {
            return envId <= EnvironmentStart && envId >= EnvironmentEnd;
        }
        public const long EnvironmentStart = -100;
        public const long EnvironmentBleeding = -100;
        public const long EnvironmentCalories = -101;
        public const long EnvironmentHydration = -102;
        public const long EnvironmentOxygen = -103;
        public const long EnvironmentPoison = -104;
        public const long EnvironmentRadiation = -105;
        public const long EnvironmentTemperature = -106;
        public const long EnvironmentElevatorExtrusion = -107;
        public const long EnvironmentFallInjury = -108;
        public const long EnvironmentSuicide = -109;
        public const long EnvironmentDecay = -110;
        public const long EnvironmentElectricity = -111;
        public const long EnvironmentVehicleCollide = -112;
        public const long ENVIRONMENT_OVER_LIMIT_DESTROY = -113;
        public const long EnvironmentEnd = -200;

        public const int PlayerCorpseTemplateId = 60008;
    }
}

