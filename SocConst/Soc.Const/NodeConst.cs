using System.Collections.Generic;
using WizardGames.Soc.Common.Algorithm;

namespace WizardGames.SocConst.Soc.Const
{
    public static class NodeSystemType
    {
        // 占位用，默认的0号节点是root，系统在此基础上自增
        public const int Invaild = -1;
        public const int Root = 0;
        // 以下为各个系统

        // 玩家容器系统
        public const int PlayerInventory = 1;
        // 异端容器系统
        public const int Storage = 2;
        // 蓝图
        public const int Blueprint = 3;
        // 熔炉
        public const int Oven = 4;
        // 科技
        public const int Technology = 5;
        // 格子内物品（掉落物，可采集物，etc）
        public const int PickableItem = 6;
        // 食物
        public const int Food = 7;
        // 通用制造
        public const int CommonCompose = 8;
        // 自动售货机
        public const int VendingMachine = 9;
        // 专门用来处理捡起地上的东西
        public const int PickUpSystem = 10;
        // 挖掘机的燃油模块
        public const int DigFuelSystem = 11;
        // 他人背包
        public const int OtherPlayerInventory = 12;
        // 自动炮台
        public const int AutoTurretSystem = 13;
        // 领地柜
        public const int TerritoryCabinet = 15;
        // 分解机
        public const int DecomposeMachineSystem = 18;
        // 任务
        public const int TaskSystem = 19;
        // 数据统计
        public const int DataStatisticSystem = 20;
        // 玩法收益积分
        public const int PlayIncomeScoreSystem = 21;
        // 玩法成本积分
        public const int PlayCostScoreSystem = 22;
        // 植物
        public const int PlantSystem = 23;
        // 尸体背包
        public const int CorpseInventory = 24;
        // 皮肤
        public const int Skin = 25;
        // 保险盒子
        public const int SafetyBox = 28;
        // 马的尸体
        public const int HorseCorpse = 29;
        // 研究台
        public const int ResearchBench = 30;
        // 声望符文
        public const int ReputationBadge = 31;
        // 声望奖励
        public const int ReputationSystem = 32;
        // 防空炮台
        public const int FlakTurretSystem = 33;
        // 种子背包
        public const int SeedBackpackSystem = 34;
        // buff
        public const int Buff = 35;
        // 邮件
        public const int Mail = 36;
        // 领地战报
        public const int TerritoryPlunderSystem = 37;
        // 衣柜
        public const int WardrobeSystem = 38;
        // 天梯勋章
        public const int MedalSystem = 39;
    }

    public static class NodePathConst
    {
        // 这俩是同一个意思
        // 代码表达的时候选择一个合适的用就行
        public const long Anywhere = -1;
        public const long AnyIndex = Anywhere;

        public static readonly IList<long> AnywhereInStorage = new ConstList<long>(NodeSystemType.Storage, Anywhere);
        public static readonly IList<long> AnywhereInPlayerInventory = new ConstList<long>(NodeSystemType.PlayerInventory, Anywhere);
        public static readonly IList<long> AnywhereInOtherPlayerInventory = new ConstList<long>(NodeSystemType.OtherPlayerInventory, Anywhere);
        public static readonly IList<long> AnywhereInPlayerSeedBackpack = new ConstList<long>(NodeSystemType.SeedBackpackSystem, Anywhere);

        public static readonly IList<long> PlayerInventoryWear = new ConstList<long>(NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Wear);
        public static readonly IList<long> PlayerInventoryBelt = new ConstList<long>(NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Belt);
        public static readonly IList<long> PlayerInventoryMain = new ConstList<long>(NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Main);
        public static readonly IList<long> ReputationReward = new ConstList<long>(NodeSystemType.ReputationSystem, PlayerInventoryNodeIndex.Reputation);
        public static readonly IList<long> AnywhereInPlayerInventoryMain = new ConstList<long>(NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Main, Anywhere);
        public static readonly IList<long> AnywhereInPlayerInventoryWear = new ConstList<long>(NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Wear, Anywhere);
        public static readonly IList<long> AnywhereInReputationReward = new ConstList<long>(NodeSystemType.ReputationSystem, PlayerInventoryNodeIndex.Reputation, Anywhere);
        public static readonly IList<long> AnywhereInOvenOutput = new ConstList<long>(NodeSystemType.Oven, StorageContainerConst.OvenOutput, Anywhere);
        public static readonly IList<long> AnywhereInResearchOutput = new ConstList<long>(NodeSystemType.ResearchBench, StorageContainerConst.ResearchBenchOutput, Anywhere);
        public static readonly IList<long> AnywhereInResearchConsume = new ConstList<long>(NodeSystemType.ResearchBench, StorageContainerConst.ResearchBenchConsume, Anywhere);
        public static readonly IList<long> AnywhereInDecomposeOutput = new ConstList<long>(NodeSystemType.DecomposeMachineSystem, StorageContainerConst.DecomposeOutput, Anywhere);
        public static readonly IList<long> DataStatisticManufacture = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Manufacture);
        public static readonly IList<long> DataStatisticConstruction = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Construction);
        public static readonly IList<long> DataStatisticItemAdd = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.ItemAdd);
        public static readonly IList<long> DataStatisticItemDestroy = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.ItemAdd);
        public static readonly IList<long> DataStatisticArea = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Area);
        public static readonly IList<long> DataStatisticReputation = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Reputation);
        public static readonly IList<long> DataStatisticKill = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Kill);
        public static readonly IList<long> DataStatisticDestroy = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Destroy);
        public static readonly IList<long> DataStatisticConstructionAdd = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.ConstructionAdd);
        public static readonly IList<long> DataStatisticFromTeammate = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.FromTeammate);
        public static readonly IList<long> AnywhereInDataStatisticManufacture = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Manufacture, Anywhere);
        public static readonly IList<long> AnywhereInDataStatisticConstruction = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Construction, Anywhere);
        public static readonly IList<long> AnywhereInDataStatisticArea = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Area, Anywhere);
        public static readonly IList<long> AnywhereInDataStatisticReputation = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Reputation, Anywhere);
        public static readonly IList<long> AnywhereInDataStatisticKill = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Kill, Anywhere);
        public static readonly IList<long> AnywhereInDataStatisticDestroy = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Destroy, Anywhere);
        public static readonly IList<long> AnywhereInDataStatisticItemAdd = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.ItemAdd, Anywhere);
        public static readonly IList<long> AnywhereInDataStatisticConstructionAdd = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.ConstructionAdd, Anywhere);
        public static readonly IList<long> AnywhereInDataStatisticFromTeammate = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.FromTeammate, Anywhere);
        // 队友统计的具体路径
        public static readonly IList<long> DataStatisticFromTeammateKill = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.FromTeammate, DataStatisticContainerNodeIndex.Kill);
        public static readonly IList<long> DataStatisticFromTeammateDestroy = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.FromTeammate, DataStatisticContainerNodeIndex.Destroy);
        public static readonly IList<long> DataStatisticFromTeammateItemAdd = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.FromTeammate, DataStatisticContainerNodeIndex.ItemAdd);
        public static readonly IList<long> AnywhereInDataStatisticFromTeammateKill = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.FromTeammate, DataStatisticContainerNodeIndex.Kill, Anywhere);
        public static readonly IList<long> AnywhereInDataStatisticFromTeammateDestroy = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.FromTeammate, DataStatisticContainerNodeIndex.Destroy, Anywhere);
        public static readonly IList<long> AnywhereInDataStatisticFromTeammateItemAdd = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.FromTeammate, DataStatisticContainerNodeIndex.ItemAdd, Anywhere);
        public static readonly IList<long> DataStatisticPropertyIncrement = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.PropertyIncrement);
        public static readonly IList<long> DataStatisticOccupyTerritory = new ConstList<long>(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.OccupyTerritory);
        public static readonly IList<long> VehicleOil = new ConstList<long>(NodeSystemType.Storage, VehicleContainerIndex.VehicleOilContainerIndex);
        public static readonly IList<long> VehicleModular = new ConstList<long>(NodeSystemType.Storage, VehicleContainerIndex.VehicleModularContainerIndex);
        public static readonly IList<long> VehicleContainerIndexes = new ConstList<long>(VehicleContainerIndex.VehicleContainer0, VehicleContainerIndex.VehicleContainer1, VehicleContainerIndex.VehicleContainer2, VehicleContainerIndex.VehicleContainer3);
        public static readonly IList<long> HorseEquip = new ConstList<long>(NodeSystemType.Storage, HorseContainerIndex.Equip);
        public static readonly IList<long> HorseCorpseEquip = new ConstList<long>(NodeSystemType.HorseCorpse, HorseCorpseContainerIndex.HorseEquip);
        public static readonly IList<long> HorseCorpseOther = new ConstList<long>(NodeSystemType.HorseCorpse, HorseCorpseContainerIndex.HorseOther);
        public static readonly IList<long> CorpseMainContainerPath = new long[] { NodeSystemType.CorpseInventory, StorageContainerConst.CorpseMain };
        public static readonly IList<long> CorpseBeltContainerPath = new long[] { NodeSystemType.CorpseInventory, StorageContainerConst.CorpseBelt };
        public static readonly IList<long> CorpseWearContainerPath = new long[] { NodeSystemType.CorpseInventory, StorageContainerConst.CorpseWear };
        public static readonly IList<long> CorpseSeedBackpackContainerPath = new long[] { NodeSystemType.CorpseInventory, StorageContainerConst.CORPSE_SEED_BACKPACK };
        public static readonly IList<long> GridPickablePath = new ConstList<long>(NodeSystemType.PickableItem, GridNodeIndex.Pickable);
        public static readonly IList<long> AllSkinPath = new ConstList<long>(NodeSystemType.Skin, SkinContainerIndex.AllSkin);
        public static readonly IList<long> DefaultSkinPath = new ConstList<long>(NodeSystemType.Skin, SkinContainerIndex.Default);
        public static readonly IList<long> DefaultBuildSkinPath = new ConstList<long>(NodeSystemType.Skin, SkinContainerIndex.Build);

        /// <summary>
        /// 可以在这里根据bizId拿到玩家拥有的所有的相同bizId的物品数量
        /// </summary>
        public static readonly IList<long> VPlayerInventory = new ConstList<long>(NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.VirtualAll);
        /// <summary>
        /// 可以在这里根据bizId拿到玩家主背包中所有的相同bizId的物品数量
        /// </summary>
        public static readonly IList<long> VPlayerInventoryMain = new ConstList<long>(NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.VirtualMain);
        /// <summary>
        /// 可以在这里根据bizId拿到玩家装备的所有的相同bizId的物品数量
        /// </summary>
        public static readonly IList<long> VPlayerInventoryBelt = new ConstList<long>(NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.VirtualBelt);
        /// <summary>
        /// 可以在这里根据bizId拿到玩家穿戴的所有的相同bizId的物品数量
        /// </summary>
        public static readonly IList<long> VPlayerInventoryWear = new ConstList<long>(NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.VirtualWear);
        /// <summary>
        /// 可以获取领地柜里材料的数量
        /// </summary>
        public static readonly IList<long> VTerritoryCainet = new ConstList<long>(NodeSystemType.TerritoryCabinet, PlayerInventoryNodeIndex.VirtualCabinetMaterials);

        /// <summary>
        /// 可以获取自动售货机里道具得数量
        /// </summary>
        public static readonly IList<long> VVendingMachine = new ConstList<long>(NodeSystemType.VendingMachine, PlayerInventoryNodeIndex.VirtualAll);

        /// <summary>
        /// 可以在这里根据bizId拿到玩家拥有的所有的相同Manufacturing的物品数量
        /// </summary>
        public static readonly IList<long> VPlayerInventoryManufacturing = new ConstList<long>(NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.VirtualAllItemManufacturing);

        /// <summary>
        /// 可以在这里根据bizId拿到玩家拥有的所有的相同Manufacturing的物品数量
        /// </summary>
        public static readonly IList<long> VStorage = new ConstList<long>(NodeSystemType.Storage, PlayerInventoryNodeIndex.VirtualAll);
        /// <summary>
        /// 可以在这里根据bizId拿到玩家拥有的所有的相同bizId的物品数量
        /// </summary>
        public static readonly IList<long> VSeedBackpack = new ConstList<long>(NodeSystemType.SeedBackpackSystem, PlayerInventoryNodeIndex.VirtualAll);
        /// <summary>
        /// 可以在这里根据bizId拿到玩家拥有的所有的相同Manufacturing的物品数量
        /// </summary>
        public static readonly IList<long> VSeedBackpackManufacturing = new ConstList<long>(NodeSystemType.SeedBackpackSystem, PlayerInventoryNodeIndex.VirtualAllItemManufacturing);
    }

    public static class NodeConst
    {
        public const long InvalidNodeIndex = -1;
        public const long InvalidNodeId = -1;
        public const int InvalidBizId = -1;
        public const long InvalidParentId = -1;
        public const long InvaildLevel = -1;

        public const int IdGroupStart = 990000; // Id组的开始值
        public const int IdGroupEnd = 999999; // Id组的结束值


        public const long RootNodeId = 0;
        public const long SystemNodeIdBegin = NodeSystemType.PlayerInventory;
        // ----------------------------------
        public const long ItemSystemNodeId = NodeSystemType.PlayerInventory;
        public const long StorageSystemNodeId = NodeSystemType.Storage;
        public const long BlueprintSystemNodeId = NodeSystemType.Blueprint;
        public const long OvenSystemNodeId = NodeSystemType.Oven;
        // ----------------------------------
        public const long SystemNodeIdEnd = 999;
        public const long FixedNodeIdBegin = 1000;
        // ----------------------------------

        public const long MainItemContainerNodeId = 1000;
        public const long BeltItemContainerNodeId = 1001;
        public const long WearItemContainerNodeId = 1002;
        public const long RepairContainerNodeId = 1003;
        public const long OvenComposeQueueNodeId = 1004;
        public const long OvenStorageNodeId = 1005;
        public const long BoxContainerNodeId = 1007;
        public const long PickableSystemNodeId = 1008;
        public const long MixingContainerNodeId = 1009;
        public const long DecomposeInputContainerNodeId = 1010;
        public const long DecomposeOutputContainerNodeId = 1011;
        public const long EatTimeNodeId = 1012;
        public const long ResearchBenchInputContainerNodeId = 1013;
        public const long ResearchBenchConsumeContainerNodeId = 1014;
        public const long ResearchBenchOutputContainerNodeId = 1015;
        public const long TerritoryCabinetContainerNodeId = 1016;
        public const long FuseContainerNodeId = 1017;
        public const long CorpseNodeId = 1018;
        public const long AutoTurretGunNodeId = 1019;
        public const long AutoTurretBulletNodeId = 1020;
        public const long CorpseMainNodeId = 1021;
        public const long CorpseBeltNodeId = 1022;
        public const long CorpseWearNodeId = 1023;
        public const long CORPSE_SEED_BACKPACK_NODE_ID = 1024;
        public const long WaterFacilityPureWaterNodeId = 1027;
        public const long WaterCatcherBottleNodeId = 1028;
        public const long KatyushaSightContainerNodeId = 1029;
        public const long KatyushaMissileContainerNodeId = 1030;
        public const long ReputationConverterInputContainerNodeId = 1031;
        public const long ReputationConverterOutputContainerNodeId = 1032;
        public const long ReputationConverterLockContainerNodeId = 1033;
        public const long SmallGeneratorNodeId = 1034;
        public const long CompletedTaskNodeId = 1035;
        public const long InProgressTaskNodeId = 1036;
        public const long PlantBoxContainerNodeId = 1040;
        public const long VendingMachineGoodsNodeId = 1041;
        public const long WaterFacilitySaltWaterNodeId = 1042;
        public const long WaterFacilityAnyWaterNodeId = 1043;
        public const long CompletedAndNotGetRewardTaskNodeId = 1044;
        public const long DigFuelContainerId = 1045;
        public const long DigOutputContainerId = 1046;
        public const long ComposterInputId = 1047;
        public const long ComposterOutputId = 1048;
        public const long OilContainerId = 1049;
        public const long VehicleContainerId0 = 1050;
        public const long VehicleContainerId1 = 1051;
        public const long VehicleContainerId2 = 1052;
        public const long VehicleContainerId3 = 1053;
        public const long TrapCollectionNodeId = 1054;
        public const long HitchingPostNodeId = 1055;
        public const long HorseDefaultContainerId = 1056;
        public const long HorseEquipContainerId = 1057;
        public const long HorseCorpseEquipContainerId = 1058;
        public const long HorseCorpseOtherContainerId = 1059;
        public const long ReputationContainerNodeId = 1060;
        public const long ModularContainerNodeId = 1062;
        public const long UnclaimedTaskNodeId = 1063;
        public const long FlakTurretAmmoNodeId = 1065;
        public const long SeedBackpackContainerNodeId = 1066;
        public const long TrainContainerNodeId = 1068;
        public const long SkinDefaultContainerNodeId = 1069;
        public const long SkinDefaultBuildContainerNodeId = 1070;
        public const long AllSkinContainerNodeId = 1071;
        public const long GestureContainerNodeId = 1072;
        public const long SprayContainerNodeId = 1073;
        public const long StorageDebrisContainerNodeId = 1077;
        public const long LANTERN = 1078;
        
        #region 衣柜保留段
        public const long WARDROBE_CONTAINER_ID_MIN = 1079;
        public const long WARDROBE_CONTAINER_ID_MAX = 1108;
        #endregion

        public const long SafetyBoxCpunterNode = 9980;

        public const long TerritoryCabinetCounterNode = 9990;
        public const long TerritoryCabinetCounterNodeRuputationInput = 9991;
        public const long TerritoryCabinetCounterNodeRuputationOutput = 9992;

        public const long TerritoryCabinetWood = 9993;
        public const long TerritoryCabinetStone = 9994;
        public const long TerritoryCabinetMetals = 9995;
        public const long TerritoryCabinetAdvanceMetals = 9996;

        public const long TerritoryCabinetSafetyBoxTemporarStorage = 9997;

        // ----------------------------------
        public const long FixedNodeIdEnd = 9999;

        // ----------------------------------
        public const int CommonComposeQueueNodeIndex = 1;
        public const int CommonComposeQueueBizId = 1;

    }

    /// <summary>
    /// 玩家背包根Node的Index
    /// </summary>
    public static class PlayerInventoryNodeIndex
    {
        public const int Main = 0;       // 背包
        public const int Belt = 1;       // 腰带、快捷栏
        public const int Wear = 2;       // 装备
        public const int Reputation = 3; // 声望奖励背包
        public const int Blueprint = 9;      // 特殊物品：建筑制造的蓝图
        public const int BuildHammer = 10;   // 特殊物品：建筑制造的小木槌
        public const int WireTool = 11;      // 特殊物品：接线工具
        public const int HoseTool = 12;      // 特殊物品：软管工具
        public const int Guguji = 13;      // 特殊物品：咕咕鸡
        public const int Max = 14;          //特殊物品最大值
        public const int VirtualMain = -1;   // 虚拟背包 统计默认不包含武器中内嵌的子弹，需要的话自己强转并用CountWithBullet属性
        public const int VirtualBelt = -2;   // 虚拟快捷栏
        public const int VirtualWear = -3;   // 虚拟装备
        public const int VirtualAll = -4;    // 虚拟全部
        public const int VirtualAllItemManufacturing = -5;    // 虚拟道具大类，统计会包含武器中内嵌的子弹
        public const int VirtualCabinetMaterials = -6; // 虚拟领地柜材料
    }

    public static class PlayerTaskContainerIndex
    {
        public const int Guide      = 1; // 引导任务
        public const int Explore    = 2; // 探索任务
        public const int Bounty     = 3; // 悬赏任务
        public const int Story      = 4; // 剧本任务
        public const int Poi        = 5; // 夺宝任务
        public const int Bage       = 6; // 徽章任务
        public const int BeeBuzz    = 7; // 蜂鸣任务
        public const int Daily      = 8; // 日常任务
        public const int Lobby      = 9; // 大厅主线任务局内任务
        public const int Medal      = 10; // 勋章任务
    }

    public static class SkinContainerIndex
    {
        public const int Default = 0; // 默认皮肤
        public const int Build = 1;   // 默认建筑皮肤
        public const int AllSkin = 2; // 所有皮肤

        public const int Gesture = 3; // 手势

        public const int Spray = 4; // 喷漆
    }

    /// <summary>
    /// 格子根Node的Index
    /// </summary>
    public static class GridNodeIndex
    {
        public const long Pickable = 0;   // 可拾取物
    }

    public static class TechnologyUnlockStatus
    {
        public const int CanUnlock = 0;
        public const int NotUnlock = 1;
        public const int AlreadyUnlock = 2;
        public const int TeamUnlock = 3;
    }

    /// <summary>
    /// 自动炮台根Node的Index
    /// </summary>
    public static class AutoTurretNodeIndex
    {
        public const long Gun = 0;   // 枪
        public const long Bullet = 1;   // 子弹
    }

    /// <summary>
    /// 任务系统Node下的Index
    /// </summary>
    public static class TaskNodeIndex
    {
        public const long InProgress = 1;   // 进行中
        public const long CompletedAndNotGetReward = 2; // 已完成但未领取奖励
        public const long Unclaimed = 3; // 未领取 Poi寻宝任务
    }

    /// <summary>
    /// 载具的默认容器Index 目前最多四个
    /// </summary>
    public static class VehicleContainerIndex
    {
        public const long VehicleContainer0 = 100;
        public const long VehicleContainer1 = 101;
        public const long VehicleContainer2 = 102;
        public const long VehicleContainer3 = 103;
        public const long VehicleOilContainerIndex = 104;
        public const long VehicleModularContainerIndex = 105;
    }

    /// <summary>
    /// 领地柜的默认容器Index
    /// </summary>
    public static class TerritoryCabinetContainerIndex
    {
        public const long Wood = 1;
        public const long Stone = 2;
        public const long Metals = 3;
        public const long AdvanceMetals = 4;
        public const long ReputationConverterInput = 5;    // 输入区
        public const long ReputationConverterOutput = 6;   // 输出区
        public const long ReputationConverterLock = 7;     // 锁定区
        public const long SafetyBoxTemporarStorage = 8;    // 保险柜暂存容器
    }

    public static class ModularContainerIndex
    {
        public const long ModularContainer0 = 0;
        public const long ModularContainer1 = 1;
        public const long ModularContainer2 = 2;
        public const long ModularContainer3 = 3;
    }

    public static class HorseCorpseContainerIndex
    {
        public const long HorseEquip = 0;
        public const long HorseOther = 1;
    }

    public static class DataStatisticContainerNodeIndex
    {
        // 制造
        public const long Manufacture = 0;
        // 建造
        public const long Construction = 1;
        // 到达关卡区域次数
        public const long Area = 2;
        // 呼叫过的情报奖励
        public const long Reputation = 3;
        // 击杀
        public const long Kill = 4;
        // 摧毁
        public const long Destroy = 5;
        // 获取道具
        public const long ItemAdd = 6;
        // 建筑(制造/升级)
        public const long ConstructionAdd = 7;
        // 队友统计信息
        public const long FromTeammate = 8;
        // 简单计数属性数值变更统计
        public const long PropertyIncrement = 9;
        // 被其它玩家击杀
        public const long BeKilled = 10;
        // 非其它玩家击杀
        public const long NoKillDeath = 11;
        // 死在遗迹
        public const long DeathInMonument = 12;
        // 进入遗迹
        public const long EnterMonument = 13;
        // 占领领地
        public const long OccupyTerritory = 14;
        // 放置领地柜
        public const long PlaceTerritoryCabinet = 15;
        // 打开宝箱
        public const long OpenBox = 16;
        // 击杀npc
        public const long KillNpc = 17;
    }

    public static class TerritoryPlunderContainerNodeIndex
    {
        //击杀
        public const long Kill = 0;
        //死亡
        public const long Death = 1;
        //伤害
        public const long Damage = 2;
        //被伤害
        public const long BeDamage = 3;
        //摧毁建筑
        public const long DestroyConstruction = 4;
        //摧毁摆件
        public const long DestroyFurnishing = 5;
        //使用爆炸物
        public const long UseExplosive = 6;
        //掠夺领地容器
        public const long PlunderContainer = 7;
    }

    public static class HorseContainerIndex
    {
        public const long Default = 100;
        public const long Equip = 101;
    }

    public enum PickableNodeType
    {
        SceneItemNode = 0,
        CollectableNode = 1,
        ThrownNode = 2
    }

    public class StorageContainerConst
    {
        public const int DEFAULT = 0;

        public const int OvenFuel = 0;
        public const int OvenInput = 1;
        public const int OvenOutput = 2;
        public const int OvenStorage = 3;

        public const int DigFuel = 0;
        public const int DigOutput = 1;

        public const int ResearchBenchInput = 0;
        public const int ResearchBenchConsume = 1;
        public const int ResearchBenchOutput = 2;

        public const int DecomposeInput = 0;
        public const int DecomposeOutput = 1;

        public const int TerritoryCabinet = 0;

        public const int CorpseMain = 0;
        public const int CorpseBelt = 1;
        public const int CorpseWear = 2;
        public const int CORPSE_SEED_BACKPACK = 3;

        public const int KatyushaAim = 0;
        public const int KatyushaMissile = 1;

        public const int PlantBoxSlot = 0;

        public const int ComposterInput = 0;
        public const int ComposterOutput = 1;

        public const int ShotgunInput = 0;

        public const int FlameTurret = 0;

        public const int TrapMaterials = 0;

        public const int GeneratorInput = 0;

        public const int SafetyBoxWeapon = 0;
        public const int SafetyBoxBelt = 1;
        public const int SafetyBoxUniverse = 2;

        public const int NoneContainer = -99999;
    }

    /// <summary>
    /// 导弹车Node的Index
    /// </summary>
    public static class KatyushaNodeIndex
    {
        public const long Sight = 0;   // 瞄准器
        public const long Missile = 1;   // 导弹
    }

    public static class OvenType
    {
        public const int NormalOven = 1; // 普通熔炉
        public const int ElectricOven = 2;
    }

    public static class FreeModeTaskType
    {
        public const int IntergalTask = 1; // 积分任务
        public const int BoxTask = 2;
    }

    public static class FreeModeTaskLimit
    {
        public const int IntergerTaskLimit = 3; //随机生成任务数量限制
    }

    public static class BoxTaskType
    {
        public const int ItemMode = 1; // 道具任务
        public const int RandomMode = 2;
    }

    public static class TaskStatu
    {
        public const int Fail = -1;
        public const int Unclaimed = 1; // 任务未领取
        public const int Claimed = 2;   //任务领取未完成  
        public const int FinishUnclaimed = 3;   //任务完成未领取奖励
        public const int FinishClaimed = 4; //任务完成奖励已领取
    }

    public static class Manufacturing
    {
        public const int Consumables = 23;
    }

    public static class SecondaryClassification
    {
        public const int TreasureMap = 8;
        public const int BeeBuzzSignalCard = 10;
    }

    public static class MountTypeConst
    {
        public const int Boat = 1;
        public const int Car = 2;
        public const int Helicopter = 3;
        public const int Horse = 4;
        public const int Train = 5;
    }

    public static class ReputationTaskStatus
    {
        public const int NotFind = -2;
        public const int Reject = -1; // 未领取
        public const int Unlaimed = 0; // 未领取
        public const int Droping = 1; // 空头中
        public const int Dropped = 2; // 已空投

        public const string LobbyReward4LevelUp = "PLayer reputation level up";
    }

    public static class ReputationLevelStatus
    {
        public const string LobbyReward4LevelUp = "PLayer reputation level up";
    }

    public static class MissionoperationType
    {
        public const int Complete = 1;
        public const int Reset = 2;
        public const int Accept = 3;
    }

    public static class SafetyBoxState
    {
        public const int Close = 0;
        public const int Activate = 1;
        public const int Temporary = 2;
    }

    public static class ReputationConst
    {
        public const int BadgeDirectoryIndex = 1;
        public const int BadgeSlotDirectoryIndex = 2;
    }

    public static class ItemStackConst
    {
        public const float ItemConditionInvaild = -1.0f;
        public const float ItemMaxConditionInvaild = -1.0f;
    }

    /// <summary>
    /// 火车车厢
    /// </summary>
    public static class TrainContainerIndex
    {
        public const long Carriage = 0;
    }

    /// <summary>
    /// 建筑残骸
    /// </summary>
    public static class DebrisContainerIndex
    {
        public const long Main = 0;
    }
}