using WizardGames.Soc.Share.Framework;

namespace WizardGames.Soc.Common.Entity
{
    public static class EntityConstId
    {
        public const long PROCESSS_ENTITY_ID = EntityComponentFrameworkConst.PROCESSS_ENTITY_ID;
        public const long RPC_ENTITY_ID = 2;
        public const long RESET_ENTITY_ID = 3;
        public const long TRANSACTION_HOST_ENTITY_ID = 4;
        public const long USER_MANAGER_ENTITY_ID = 5;

        // 业务
        public const long SERVER_BOOT_ENTITY_ID = 8;
        public const long MARKER_MANAGER_ENTITY_ID = 9;
        public const long SURPRISE_PLAY_ENTITY_ID = 10;
        public const long SERVER_INSTANCE_ENTITY_ID = 12;
        public const long LOGIN_QUEUE_ENTITY_ID = 13;
        public const long ELECTRIC_MANAGER_ENTITY_ID = 14;
        public const long SPAWN_CONTROL_ENTITY_ID = 15;
        public const long DEAD_SHEEP_ENTITY_ID = 16;
        public const long CONSTRUCTION_MANAGER_ENTITY_ID = 17;
        public const long TERRITORY_MANAGER_ENTITY_ID = 18;
        public const long PLAYER_BORN_ENTITY_ID = 19;
        public const long TASK_SPAWN_ENTITY_ID = 20;
        public const long BLUEPRINT_MANAGER_ENTITY_ID = 21;
        public const long SHOP_CONTROL_ENTITY_ID = 22;
        public const long TREE_CONTROL_ENTITY_ID = 24;
        public const long SERVER_CONTROL_ENTITY_ID = 25;
        public const long FIXPOINT_ENTITY_ID = 26;
        public const long AIRDROP_CONTROL_ENTITY_ID = 27;
        public const long GRAPH_DEBUG_ENTITY_ID = 28;
        public const long PLAYER_CONSTRUCTION_BLUEPRINT_MANAGER_ENTITY_ID = 29;
        public const long TEAM_MANAGER_ENTITY_ID = 30;

        // 由于读档顺序按照ID，需要将GlobalSyncEntity读档的顺序放置在靠后，2025年1月13日18点01分 by:@wjqiang
        public const long GLOBAL_INFO_SYNC_ENTITY_ID = 123;

        public const long GRID_ENTITY_ID_START = 1000;
        public const long GRID_ENTITY_ID_END = 10000;

        public const long TEST_ID_BEGIN = 100000;
        public const long TEST_TIMER_ID_BEGIN = 900000;
        public const long TEST_ID_RESERVE = 950000;
        public const long TEST_ID_END = 999999;
        // 100000-999999 预留给测试使用
    }
}

namespace WizardGames.Soc.Share.Framework
{
    public static class EntityComponentFrameworkConst
    {
        public const long PROCESSS_ENTITY_ID = 1;
        public const int COMPONENT_ID_START = 0;
        public const int INVALID_COMPONENT_ID = -1;
    }

    public static class FrameworkEntityConstId
    {
        public const long PROCESSS_ENTITY_ID = Common.Entity.EntityConstId.PROCESSS_ENTITY_ID;
        public const long USER_MANAGER_ENTITY_ID = Common.Entity.EntityConstId.USER_MANAGER_ENTITY_ID;
    }
}
