using System.Collections.Generic;
namespace WizardGames.Soc.Share
{
    public static class CustomTypeConst
    {
        public const int NodeChildDictHash = 3;
        public const int EntityDeltaMsgHash = 4;
        public const int EntityFullMsgHash = 5;
        public const int ClientDeltaSyncMsgHash = 6;
        public const int ENTITY_BATCH_DELTA_PROPERTY_HASH = 7;
        public const int POSITION_SYNC_INFO = 8;

        private const int CustomTypeHashReserved = 20;

        public enum EFixedCustomTypeHash
        {
            Reserved = CustomTypeHashReserved,
            // ----建筑相关，改了需要同步修改死羊房数据----
            PartEntityHash = 21,
            PartDamageableComponentHash = 22,
            FoundationLinkComponentHash = 23, 
            StabilityComponentHash = 24,
            ConstructionCoreComponentHash = 25,
            ConstructionBaseComponentHash = 26,
            ConstructionSocketBaseComponentHash = 27,
            ConstructionSkinComponentHash = 28,
            DamageableComponentHash = 29,
            RootNodeComponentHash = 30,
            TransformComponentHash = 31,
            ConstructionFlagComponentHash = 32, 
            ElectricBatteryComponent = 33,
            // -------------------------
            
            PlayerDataStatisticComponentHash = 34,
            WearItemCustomHash = 35,
            VehicleModuleCustomHash = 36,
            PlayerDamageableComponentHash = 37,
            ServerInstanceEntity = 38,

            BuildPartBriefHash = 50,
            BuildPartBriefCoreHash = 51,
            BuildPartBriefWithExtraDataHash = 52,


            Max = 1000,
        }
        
        public const int REF_PROPERTY_INDEX_OFFSET = 10000;

        public static readonly Dictionary<string, int> FixedCustomTypeHash = new()
        {
            { "PartEntity", (int)EFixedCustomTypeHash.PartEntityHash },
            { "PartDamageableComponent",  (int)EFixedCustomTypeHash.PartDamageableComponentHash},
            { "FoundationLinkComponent",  (int)EFixedCustomTypeHash.FoundationLinkComponentHash},
            { "StabilityComponent", (int)EFixedCustomTypeHash.StabilityComponentHash},
            { "ConstructionCoreComponent",  (int)EFixedCustomTypeHash.ConstructionCoreComponentHash},
            { "ConstructionBaseComponent",  (int)EFixedCustomTypeHash.ConstructionBaseComponentHash},
            { "ConstructionSocketBaseComponent",  (int)EFixedCustomTypeHash.ConstructionSocketBaseComponentHash},
            { "ConstructionSkinComponent",  (int)EFixedCustomTypeHash.ConstructionSkinComponentHash},
            { "TransformComponent",  (int)EFixedCustomTypeHash.TransformComponentHash},
            { "ConstructionFlagComponent",  (int)EFixedCustomTypeHash.ConstructionFlagComponentHash},
            { "DamageableComponent",  (int)EFixedCustomTypeHash.DamageableComponentHash},
            { "RootNodeComponent",  (int)EFixedCustomTypeHash.RootNodeComponentHash},
            { "ElectricBatteryComponent",  (int)EFixedCustomTypeHash.ElectricBatteryComponent},
            { "BuildPartBrief",  (int)EFixedCustomTypeHash.BuildPartBriefHash},
            { "BuildPartBriefCore",  (int)EFixedCustomTypeHash.BuildPartBriefCoreHash },
            { "BuildPartBriefWithExtraData",  (int)EFixedCustomTypeHash.BuildPartBriefWithExtraDataHash},

            { "ServerInstanceEntity",  (int)EFixedCustomTypeHash.ServerInstanceEntity},
            { "PlayerDataStatisticComponent",  (int)EFixedCustomTypeHash.PlayerDataStatisticComponentHash},
            { "WearItemCustom",  (int)EFixedCustomTypeHash.WearItemCustomHash},
            { "VehicleModuleCustom",  (int)EFixedCustomTypeHash.VehicleModuleCustomHash},
            { "PlayerDamageableComponent",  (int)EFixedCustomTypeHash.PlayerDamageableComponentHash},
        };
    }
}
