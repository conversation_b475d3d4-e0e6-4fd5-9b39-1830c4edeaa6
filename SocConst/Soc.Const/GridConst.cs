using System.Collections.Generic;
using System;

namespace WizardGames.Soc.Common.Entity
{
    public class GridConst
    {
        public const int RADIUS_MAX = 10;
        // 格子从半径 0 到 10 的半斤所有点
        public static readonly (int x, int y)[][] Points =
        {
            // 半径 0
            new (int, int)[] { (0, 0) },
        
            // 半径 1
            new (int, int)[]
            {
                (1, -1), (1, 0), (1, 1),
                (0, 1), (0, -1),
                (-1, 1), (-1, 0), (-1, -1)
            },

            // 半径 2
            new (int, int)[]
            {
                (2, -2), (2, -1), (2, 0), (2, 1), (2, 2),
                (0, 2), (0, -2),
                (-2, 2), (-2, 1), (-2, 0), (-2, -1), (-2, -2),
                (1, 2), (1, -2), (-1, 2), (-1, -2)
            },

            // 半径 3
            new (int, int)[]
            {
                (3, -3), (3, -2), (3, -1), (3, 0), (3, 1), (3, 2), (3, 3),
                (0, 3), (0, -3),
                (-3, 3), (-3, 2), (-3, 1), (-3, 0), (-3, -1), (-3, -2), (-3, -3),
                (1, 3), (1, -3), (2, 3), (2, -3),
                (-1, 3), (-1, -3), (-2, 3), (-2, -3)
            },

            // 半径 4
            new (int, int)[]
            {
                (4, -4), (4, -3), (4, -2), (4, -1), (4, 0), (4, 1), (4, 2), (4, 3), (4, 4),
                (0, 4), (0, -4),
                (-4, 4), (-4, 3), (-4, 2), (-4, 1), (-4, 0), (-4, -1), (-4, -2), (-4, -3), (-4, -4),
                (1, 4), (1, -4), (2, 4), (2, -4), (3, 4), (3, -4),
                (-1, 4), (-1, -4), (-2, 4), (-2, -4), (-3, 4), (-3, -4)
            },

            // 半径 5
            new (int, int)[]
            {
                (5, -5), (5, -4), (5, -3), (5, -2), (5, -1), (5, 0), (5, 1), (5, 2), (5, 3), (5, 4), (5, 5),
                (0, 5), (0, -5),
                (-5, 5), (-5, 4), (-5, 3), (-5, 2), (-5, 1), (-5, 0), (-5, -1), (-5, -2), (-5, -3), (-5, -4), (-5, -5),
                (1, 5), (1, -5), (2, 5), (2, -5), (3, 5), (3, -5), (4, 5), (4, -5),
                (-1, 5), (-1, -5), (-2, 5), (-2, -5), (-3, 5), (-3, -5), (-4, 5), (-4, -5)
            },

            // 半径 6
            new (int, int)[]
            {
                (6, -6), (6, -5), (6, -4), (6, -3), (6, -2), (6, -1), (6, 0), (6, 1), (6, 2), (6, 3), (6, 4), (6, 5), (6, 6),
                (0, 6), (0, -6),
                (-6, 6), (-6, 5), (-6, 4), (-6, 3), (-6, 2), (-6, 1), (-6, 0), (-6, -1), (-6, -2), (-6, -3), (-6, -4), (-6, -5), (-6, -6),
                (1, 6), (1, -6), (2, 6), (2, -6), (3, 6), (3, -6), (4, 6), (4, -6), (5, 6), (5, -6),
                (-1, 6), (-1, -6), (-2, 6), (-2, -6), (-3, 6), (-3, -6), (-4, 6), (-4, -6), (-5, 6), (-5, -6)
            },

            // 半径 7
            new (int, int)[]
            {
                (7, -7), (7, -6), (7, -5), (7, -4), (7, -3), (7, -2), (7, -1), (7, 0), (7, 1), (7, 2), (7, 3), (7, 4), (7, 5), (7, 6), (7, 7),
                (0, 7), (0, -7),
                (-7, 7), (-7, 6), (-7, 5), (-7, 4), (-7, 3), (-7, 2), (-7, 1), (-7, 0), (-7, -1), (-7, -2), (-7, -3), (-7, -4), (-7, -5), (-7, -6), (-7, -7),
                (1, 7), (1, -7), (2, 7), (2, -7), (3, 7), (3, -7), (4, 7), (4, -7), (5, 7), (5, -7), (6, 7), (6, -7),
                (-1, 7), (-1, -7), (-2, 7), (-2, -7), (-3, 7), (-3, -7), (-4, 7), (-4, -7), (-5, 7), (-5, -7), (-6, 7), (-6, -7)

            },

            // 半径 8
            new (int, int)[]
            {
                (8, -8), (8, -7), (8, -6), (8, -5), (8, -4), (8, -3), (8, -2), (8, -1), (8, 0), (8, 1), (8, 2), (8, 3), (8, 4), (8, 5), (8, 6), (8, 7), (8, 8),
                (0, 8), (0, -8),
                (-8, 8), (-8, 7), (-8, 6), (-8, 5), (-8, 4), (-8, 3), (-8, 2), (-8, 1), (-8, 0), (-8, -1), (-8, -2), (-8, -3), (-8, -4), (-8, -5), (-8, -6), (-8, -7), (-8, -8),
                (1, 8), (1, -8), (2, 8), (2, -8), (3, 8), (3, -8), (4, 8), (4, -8), (5, 8), (5, -8), (6, 8), (6, -8), (7, 8), (7, -8),
                (-1, 8), (-1, -8), (-2, 8), (-2, -8),
            },

            // 半径 9
            new (int, int)[]
            {
                (9, -9), (9, -8), (9, -7), (9, -6), (9, -5), (9, -4), (9, -3), (9, -2), (9, -1), (9, 0), (9, 1), (9, 2), (9, 3), (9, 4), (9, 5), (9, 6), (9, 7), (9, 8), (9, 9),
                (0, 9), (0, -9),
                (-9, 9), (-9, 8), (-9, 7), (-9, 6), (-9, 5), (-9, 4), (-9, 3), (-9, 2), (-9, 1), (-9, 0), (-9, -1), (-9, -2), (-9, -3), (-9, -4), (-9, -5), (-9, -6), (-9, -7), (-9, -8), (-9, -9),
                (1, 9), (1, -9), (2, 9), (2, -9), (3, 9), (3, -9), (4, 9), (4, -9), (5, 9), (5, -9), (6, 9), (6, -9), (7, 9), (7, -9), (8, 9), (8, -9),
                (-1, 9), (-1, -9), (-2, 9), (-2, -9), (-3, 9), (-3, -9), (-4, 9), (-4, -9), (-5, 9), (-5, -9), (-6, 9), (-6, -9), (-7, 9), (-7, -9), (-8, 9), (-8, -9)
            },

            // 半径 10
            new (int, int)[]
            {
                (10, -10), (10, -9), (10, -8), (10, -7), (10, -6), (10, -5), (10, -4), (10, -3), (10, -2), (10, -1), (10, 0), (10, 1), (10, 2), (10, 3), (10, 4), (10, 5), (10, 6), (10, 7), (10, 8), (10, 9), (10, 10),
                (0, 10), (0, -10),
                (-10, 10), (-10, 9), (-10, 8), (-10, 7), (-10, 6), (-10, 5), (-10, 4), (-10, 3), (-10, 2), (-10, 1), (-10, 0), (-10, -1), (-10, -2), (-10, -3), (-10, -4), (-10, -5), (-10, -6), (-10, -7), (-10, -8), (-10, -9), (-10, -10),
                (1, 10), (1, -10), (2, 10), (2, -10), (3, 10), (3, -10), (4, 10), (4, -10), (5, 10), (5, -10), (6, 10), (6, -10), (7, 10), (7, -10), (8, 10), (8, -10), (9, 10), (9, -10),
                (-1, 10), (-1, -10), (-2, 10), (-2, -10), (-3, 10), (-3, -10), (-4, 10), (-4, -10), (-5, 10), (-5, -10), (-6, 10), (-6, -10), (-7, 10), (-7, -10), (-8, 10), (-8, -10), (-9, 10), (-9, -10)
            },
        };

        public static IEnumerable<(int x, int y)> GetNeighboringPoints(int radius)
        {
            if (radius < 0 || radius > RADIUS_MAX)
                throw new ArgumentOutOfRangeException(nameof(radius), "Radius must be between 0 and 10.");

            return Points[radius];
        }
    }

}


