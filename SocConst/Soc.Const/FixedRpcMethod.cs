using System.Collections.Generic;

namespace WizardGames.SocConst.Soc.Const
{
    public static class FixedRpcMethod
    {
        public const int PLAYER_ENTITY_FRAME_END = 1;
        public const int OBSERVER_ENTITY_FRAME_END = 2;
        public const int UPDATE_CLIENT_ENTITIES = 3;
        public const int PLAYER_ENTITY_RECEIVE_USER_CMD = 4;
        public const int PROCESS_ENTITY_GATE_KICK_CLIENT = 5;
        public const int PROCESS_ENTITY_START_LOADING = 6;
        public const int PROCESS_ENTITY_BEGIN_SYNC_TIME = 7;
        public const int PROCESS_ENTITY_SYNC_TIME_ACK = 8;
        public const int PROCESS_ENTITY_REGISTER_SERVICE_ACK = 9;
        public const int PROCESS_ENTITY_SYNC_UNITY_DS_POSITION = 10;
        public const int PROCESS_ENTITY_UPDATE_UNITY_DS_ENTITIES = 11;
        public const int PROCESS_ENTITY_ON_SERVER_KICK = 12;
        public const int PROCESS_ENTITY_SYNC_ENTITY_DONE = 13;
        public const int PROCESS_ENTITY_BATCH_UPDATE_DELTA_PROPERTY = 14;
        public const int PROCESS_ENTITY_GATE_NET_INFO_REPORT = 15;
        public const int PROCESS_ENTITY_CLIENT_DISCONNECT = 16;
        public const int PROCESS_ENTITY_LOGIN_NAK = 17;
        public const int PROCESS_ENTITY_LOGIN_VERSION_MISMATCH = 18;
        public const int PROCESS_ENTITY_DEV_VERSION_MISMATCH_DETAIL = 19;
        public const int PROCESS_ENTITY_LOGIN_ACK = 20;
        public const int PROCESS_ENTITY_CLIENT_SYNC_TIME_ACK = 21;
        public const int PROCESS_ENTITY_ON_ENTER_QUEUE = 22;
        public const int PROCESS_ENTITY_UPDATE_QUEUE_POSITION = 23;
        public const int PROCESS_ENTITY_UPDATE_CLIENT_TABLE = 24;
        public const int PROCESS_ENTITY_KICK_OUT = 25;
        public const int RPC_ENTITY_ON_GRPC_COMMAND = 26;
        public const int RPC_ENTITY_CREATE_TEAM = 27;
        public const int RPC_ENTITY_START_DYNAMIC_BATTLE = 28;
        public const int RPC_ENTITY_FORCE_SHUTDOWN_DYNAMIC_BATTLE = 29;
        public const int RPC_ENTITY_REPLY_TEAM_INVITATION_FROM_LOBBY = 30;
        public const int RPC_ENTITY_PLAYER_DELETE_BATTLE = 31;
        public const int RPC_ENTITY_NOTIFY_ROLE_RESOURCE_CHENGE = 32;
        public const int RPC_ENTITY_APPLY_JOIN_BATTLE_RECRUITMENT = 33;
        public const int PROCESS_ENTITY_UPDATE_GRID_ENTITY = 34;
        public const int PROCESS_ENTITY_ADJUST_TIME = 35;
        public const int RPC_ENTITY_NOTIFY_SERVER_CHANGE_TIME = 36;
        public const int RPC_ENTITY_GET_ONE_TERRITORY_OUTSIDE = 37;
        public const int RPC_ENTITY_GET_ONE_TERRITORY_OUTSIDE_ACK = 38;
        public const int RPC_ENTITY_NO_TERRITORY_OUTSIDE = 39;
        public const int RPC_ENTITY_TERRITORY_OUTSIDE_DONE = 40;
        public const int PROCESS_ENTITY_RECORD_POSSIBLE_ERROR_ENTITY_ID = 41;
        public const int RPC_ENTITY_JOIN_DYNAMIC_BATTLE = 42;
        public const int PROCESS_ENTITY_LOBBY_ALLOCATE_SERVER_INFO = 43;
        public const int PROCESS_ENTITY_SERVER_LOADING_DONE = 44;

        public const int RPC_ENTITY_PUSH_CHANGE_TALENTS = 45;
        public const int RPC_ENTITY_PUSH_NEW_TASK_GROUPS = 46;
        public const int RPC_ENTITY_GM_TASK_FINISH = 47;

        public const int FIXED_METHOD_ID_MAX = 1000;
        // 1000以下的号段是被占用的，可以手工指定
        public static readonly Dictionary<string, int> FixedRpcMethodId = new()
        {
            { "PlayerEntity.FrameEnd", PLAYER_ENTITY_FRAME_END },
            { "ObserverEntity.FrameEnd", OBSERVER_ENTITY_FRAME_END },
            { "ProcessEntity.UpdateClientEntities", UPDATE_CLIENT_ENTITIES },
            { "PlayerEntity.ReceiveUserCmd", PLAYER_ENTITY_RECEIVE_USER_CMD },
            { "ProcessEntity.GateKickClient", PROCESS_ENTITY_GATE_KICK_CLIENT},
            { "ProcessEntity.StartLoading", PROCESS_ENTITY_START_LOADING},
            { "ProcessEntity.BeginSyncTime" , PROCESS_ENTITY_BEGIN_SYNC_TIME},
            { "ProcessEntity.SyncTimeAck", PROCESS_ENTITY_SYNC_TIME_ACK },
            { "ProcessEntity.RegisterServiceAck", PROCESS_ENTITY_REGISTER_SERVICE_ACK },
            { "ProcessEntity.SyncUnityDsPosition", PROCESS_ENTITY_SYNC_UNITY_DS_POSITION },
            { "ProcessEntity.UpdateUnityDsEntities", PROCESS_ENTITY_UPDATE_UNITY_DS_ENTITIES },
            { "ProcessEntity.OnServerKick", PROCESS_ENTITY_ON_SERVER_KICK },
            { "ProcessEntity.SyncEntityDone", PROCESS_ENTITY_SYNC_ENTITY_DONE },
            { "ProcessEntity.BatchUpdateDeltaProperty", PROCESS_ENTITY_BATCH_UPDATE_DELTA_PROPERTY },
            { "ProcessEntity.GateNetInfoReport", PROCESS_ENTITY_GATE_NET_INFO_REPORT },
            { "ProcessEntity.ClientDisconnect", PROCESS_ENTITY_CLIENT_DISCONNECT },
            { "ProcessEntity.LoginNak", PROCESS_ENTITY_LOGIN_NAK },
            { "ProcessEntity.LoginVersionMismatch", PROCESS_ENTITY_LOGIN_VERSION_MISMATCH },
            { "ProcessEntity.DevVersionMismatchDetail", PROCESS_ENTITY_DEV_VERSION_MISMATCH_DETAIL },
            { "ProcessEntity.LoginAck", PROCESS_ENTITY_LOGIN_ACK },
            { "ProcessEntity.ClientSyncTimeAck", PROCESS_ENTITY_CLIENT_SYNC_TIME_ACK },
            { "ProcessEntity.OnEnterQueue", PROCESS_ENTITY_ON_ENTER_QUEUE },
            { "ProcessEntity.UpdateQueuePosition", PROCESS_ENTITY_UPDATE_QUEUE_POSITION },
            { "ProcessEntity.UpdateClientTable", PROCESS_ENTITY_UPDATE_CLIENT_TABLE },
            { "ProcessEntity.KickOut", PROCESS_ENTITY_KICK_OUT },
            { "RpcEntity.OnGRpcCommand", RPC_ENTITY_ON_GRPC_COMMAND },
            { "RpcEntity.CreateTeam", RPC_ENTITY_CREATE_TEAM },
            { "RpcEntity.StartDynamicBattle", RPC_ENTITY_START_DYNAMIC_BATTLE },
            { "RpcEntity.ForceShutdownDynamicBattle", RPC_ENTITY_FORCE_SHUTDOWN_DYNAMIC_BATTLE },
            { "RpcEntity.ReplyTeamInvitationFromLobby", RPC_ENTITY_REPLY_TEAM_INVITATION_FROM_LOBBY },
            { "RpcEntity.PlayerDeleteBattle", RPC_ENTITY_PLAYER_DELETE_BATTLE },
            { "RpcEntity.NotifyRoleResourceChange", RPC_ENTITY_NOTIFY_ROLE_RESOURCE_CHENGE },
            { "RpcEntity.ApplyJoinBattleRecruitment", RPC_ENTITY_APPLY_JOIN_BATTLE_RECRUITMENT },
            { "ProcessEntity.UpdateGridEntity", PROCESS_ENTITY_UPDATE_GRID_ENTITY },
            { "ProcessEntity.AdjustTime", PROCESS_ENTITY_ADJUST_TIME },
            { "RpcEntity.NotifyServerChangeTime", RPC_ENTITY_NOTIFY_SERVER_CHANGE_TIME },
            { "RpcEntity.GetOneTerritoryOutside", RPC_ENTITY_GET_ONE_TERRITORY_OUTSIDE },
            { "RpcEntity.GetOneTerritoryOutsideAck", RPC_ENTITY_GET_ONE_TERRITORY_OUTSIDE_ACK },
            { "RpcEntity.NoTerritoryOutside", RPC_ENTITY_NO_TERRITORY_OUTSIDE },
            { "RpcEntity.TerritoryOutsideDone", RPC_ENTITY_TERRITORY_OUTSIDE_DONE },
            { "ProcessEntity.RecordPossibleErrorEntityId", PROCESS_ENTITY_RECORD_POSSIBLE_ERROR_ENTITY_ID },
            { "RpcEntity.JoinDynamicBattle", RPC_ENTITY_JOIN_DYNAMIC_BATTLE },
            { "ProcessEntity.LobbyAllocateServerInfo", PROCESS_ENTITY_LOBBY_ALLOCATE_SERVER_INFO },
            { "ProcessEntity.ServerLoadingDone", PROCESS_ENTITY_SERVER_LOADING_DONE },
            { "RpcEntity.PushChangeTalents", RPC_ENTITY_PUSH_CHANGE_TALENTS },
            { "RpcEntity.PushNewTaskGroups", RPC_ENTITY_PUSH_NEW_TASK_GROUPS },
            { "RpcEntity.GMTaskFinish", RPC_ENTITY_GM_TASK_FINISH }
        };
    }
}
