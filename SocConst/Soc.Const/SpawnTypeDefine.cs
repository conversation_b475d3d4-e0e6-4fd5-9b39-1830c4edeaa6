namespace WizardGames.Soc.Common.Entity
{
    public static class SpawnTypeDefine
    {
        /// <summary>
        /// 默认
        /// </summary>
        public const int Default = 0;

        /// <summary>
        /// 大世界刷新
        /// </summary>
        public const int SpawnRule = 1;

        /// <summary>
        /// 关卡组刷新
        /// </summary>
        public const int SpawnGroup = 2;

        /// <summary>
        /// 垃圾堆
        /// </summary>
        public const int SpawnJunkpile = 3;

        /// <summary>
        /// 任务刷新
        /// </summary>
        public const int SpawnTask = 4;

        /// <summary>
        /// 死羊刷新
        /// </summary>
        public const int SpawnDeadSheep = 5;

        /// <summary>
        /// bot刷新
        /// </summary>
        public const int SpawnBot = 6;
        /// <summary>
        /// 商店刷新
        /// </summary>
        public const int SpawnShop = 7;
        /// <summary>
        /// 服务器内部刷新
        /// </summary>
        public const int GraphNode = 8;
        /// <summary>
        /// POI任务刷新
        /// </summary>
        public const int SpawnPoiTask = 9;
        /// <summary>
        /// 巡逻队刷新
        /// </summary>
        public const int SpawnPatrolTeam = 10;
        /// <summary>
        /// 巡逻商店刷新
        /// </summary>
        public const int SpawnPatrolShop = 11;
        /// <summary>
        /// 官方小屋
        /// </summary>
        public const int ConstructionBlueprint = 12;
        /// <summary>
        /// 男团刷新
        /// </summary>
        public const int SpawnBoyband = 13;
        /// <summary>
        /// 蜂鸣小队刷新
        /// </summary>
        public const int SPAWN_BEE_BUZZ_TEAM = 14;
        /// <summary>
        /// 武直刷新
        /// </summary>
        public const int SpawnGunship = 15;
        /// <summary>
        ///夺宝召唤敌人
        /// </summary>
        public const int TreasureCallEnemies = 16;
        /// <summary>
        /// 任务遗迹内刷新
        /// </summary>
        public const int SpawnMonumentTask = 17;

        public const int PlayerBlueprintSpawn = 20;
        /// <summary>
        /// <summary>
        /// 服务端内部创建默认值
        /// </summary>
        public const int SpawnInServer = 99;
    }

    public enum ESpawnRule
    {
        CommonSpawn,
        None
    }
}
