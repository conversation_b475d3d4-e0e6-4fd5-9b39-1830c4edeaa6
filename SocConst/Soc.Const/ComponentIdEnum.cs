using WizardGames.Soc.Share.Framework;

namespace WizardGames.Soc.Common.Component
{
    public enum EComponentIdEnum
    {
        InvalidId = EntityComponentFrameworkConst.INVALID_COMPONENT_ID,
        ArrComponentIdStart = EntityComponentFrameworkConst.COMPONENT_ID_START,
        ReputationComponentId,
        PlayerMiscComponentId,
        PlayerMailComponentId,
        PlayerMarkComponentId,
        PlayerTeamComponentId,
        BlueprintComponentId,
        PlayerChatComponentId,
        PlayerSurpriseComponentId,
        PlayerInventoryComponentId,
        PlayerLootingComponentId,
        CommonComposeComponent,
        FoodComponent,
        PlayerMoneyComponentId,
        PlayerConstruction,
        PlayerPickUp,
        PlayerSkin,
        PlayerSeedBackpack,
        PlayerBoyband,
        PlayerSafeOffline,
        PlayerUnlimitedItem,
        BuffComponentId,
        PlayerPlunder,
        PlayerPandora,

        Debug,
        // 服务端
        PlayerDeathComponentId,
        PlayerBotComponentId,
        PlayerDropAndGatherComponentId,
        Storage,        //储物组件
        Switch,         //开关组件
        Manufacture,    //制造组件
        Fuel,           //燃料组件
        FuelConsumption,      //发电组件
        SearchLight,    //探照灯
        FireEffectByFuel,   //燃料组件引起的火焰效果
        LightEffectByFlag,//等光组件（仅PC）
        NodeEnableByFuel,   //燃料组件引起的节点开启效果
        PermissionLock,     //权限锁组件
        PlantBox,           //种植箱组件
        SleepingBag,        //睡袋组件
        Oven,               //熔炉组件
        RepairBench,        //修理台组件
        Transform,
        TerritroyBatchUpgrade,
        OutsideDataSet,  // 建筑外观数据
        ElectricBase,
        ElectricPowerUser,
        VendingMachine,    //自动售货机组件
        RootNodeComponent,
        PlayerShopComponent,
        Wardrobe,//衣柜

        PlayerTaskComponent,            // 任务组件
        PlayerMedalComponent,           // 天梯勋章组件

        ResearchBench,     //研究台组件
        TerritoryCabinet,  //领地柜组件
        DecomposeMachine,  //分解机组件
        MixingTable,       //调制台组件
        HitchTrough,       //拴马桩和喂食槽组件
        Composter,         //堆肥机组件
        StorageBox,        //储物箱组件
        DigComponentId,
        IOComponentId,
        MonsterComponentId,
        KatyushaComponentId,
        TerritoryBase,      //领地中枢基础组件
        TerritoryPermssion, //领地权限组件
        TerritoryPlay,      //领地玩法组件
        TerritoryDeadSheep,      //死羊领地
        TerritorySkin,      //领地皮肤管理
        TerritoryDoor,      //领地门管理
        TerritoryBatchRecover, //一键修复
        TerritoryPlunder, //领地掠夺数据统计
        AutoTurret,         //自动炮台
        FlakTurret,         //防空炮台
        HorseComponent,
        PlayerVehicleComponent,
        VehicleComponent,
        ModularCarComponent,
        Box,
        Corpse,
        PickableItem,
        ConsumedItem,
        PlayerDataStatisticComponent,
        ShopComponent,
        PlayerFriendComponent,
        PatrolComponent,
        CampingTentComponent,
        DestroyWithToolcupBoard,

        AntiCheatCheck,
        BeeBuzz,
        SafetyBoxComponent,
        SpawnComponent,
        TrainComponent,
        ShowCabinet, //商品陈列柜
        BoxGame, //保险箱小游戏
        DebrisComponent,//建筑残骸
        DestroyComponent,

        PropTest, //属性测试组件
        ClientGraphNodeComponent,

        DayNightAutoSwitch, //昼夜循环组件。帮助组件自动在昼夜交替时发送
        FunctionSwitchComponent, //功能开关组件

        // 战斗组
        CombatBase = 512,
        PhysicsMove,
        Damageable,//原IHitableEntity功能
        ProjectileExplosion,
        HitBox,
        PlayerDraw,
        PassiveTrap,
        GunTrap,
        SeekerTarget,//可被追踪的目标
        SleepGroup,//休眠entity组合组件，用于监听父对象销毁后激活的子对象
        PlayerArchiveRebornPoint,
        HorseStamina,
        SecAimFlowTlog,
        
        // 建造组
        ConstructionBase = 1024,
        Stability,
        ConstructionSocket,
        ConstructionMove,
        ConstructionCore,
        ConstructionSpawnType,
        ConstructionProduce,
        ConstructionDecayProtection,
        ConstructionContainer,
        ConstructionElectricState,
        ConstructionTrap,
        ConstructionSpecialPlayRule,
        ConstructionRename,
        ConstructionCombination,
        ConstructionElevator,
        ConstructionDoor,
        ConstructionFlag,
        ConstructionSkin, //建筑皮肤
        PermissionHub,
        Decay,
        GroundWatch,
        Trap,
        Trigger,
        TemperatureTrigger,
        ComfortTrigger,
        SelfDestroy,

        DebrisSelfDestroy,
        DebrisObject,
        LiftPlatform,

        Deploy, //客户端专用，摆放组件
        Interactive, //客户端专用，交互组件
        ShatterEffect,//客户端专用，破碎效果组件
        PartGameObject, //客户端专用，Go组件，过度用，之后去GameObject之后会删除
        ConstructionRender, //客户端专用，渲染组件
        PartHighLightEffect, //客户端专用，建筑高亮组件
        PlayModePart, //客户端专用，特定玩法额外功能组件
        PartAnimation, //客户端专用，建筑动画组件
        PartBattle, //Simulator专用，建筑战斗组件

        PartRepair, //Simulator专用，建筑修理组件
        SimulateCheck, //Simulator专用，建筑检测组件

        ConstructionCollider, //建筑碰撞体组件


        FoundationLink,
        ContainerLink,
        WaterFacility,
        WaterCatcher,
        GpuInstComponent,
        ConstructionGapComp,//建筑美缝

        UGCTriggerRegionComp,

        // 死羊
        DeadSheepPosition,

        // 大世界组（PVE）
        BehaviorTreeComponent = 2048,
        SwarmMemberComponent,
        BehaviorRuleTreeComponent,
        BehaviorStrategyTreeComponent,
        GroundSampleComponent,
        //AiSenseComponent,
        MonsterActiveComponent,
        AiBrainComponent,
        BaseSenseComponent,
        HearingSenseComponent,
        VisionSenseComponent,
        SpecialSenseComponent,
        DamageSenseComponent,
        FindTargetComponent,
        AIStateComponent,
        AIMoveComponent,
        SwimmingComponent,
        TiredComponent,
        NavigationObstacleComponent,

        RuleGraphComponent,
        NewTurretComponent,

        BuoyancyComponent,
    }

}