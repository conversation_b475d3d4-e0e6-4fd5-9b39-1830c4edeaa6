using System.Collections.Generic;

namespace WizardGames.SocConst.Soc.Const
{
    // 不要超过7位
    public enum EPropertyGroupEnum
    {
        IPlayerPredictEntity = 1,
        IPositionEntity = 1 << 1, 
        IRotation3Entity = 1 << 2,       
        IRotationEntity = 1 << 3,
    }

    public static class PropertyGroupEnumExtension
    {
        public static readonly Dictionary<string, EPropertyGroupEnum> PropertyGroupName2Enum = new Dictionary<string, EPropertyGroupEnum>
        {
            {"IPositionEntity", EPropertyGroupEnum.IPositionEntity},
            {"IRotation3Entity", EPropertyGroupEnum.IRotation3Entity},
            {"IPlayerPredictEntity", EPropertyGroupEnum.IPlayerPredictEntity},
            {"IRotationEntity", EPropertyGroupEnum.IRotationEntity},
        };

        public static bool InPropertyGroupEnum(string interfaceName)
        {
            return PropertyGroupName2Enum.ContainsKey(interfaceName);
        }
        public static EPropertyGroupEnum GetPropertyGroupEnum(string propertyGroupName)
        {
            if (PropertyGroupName2Enum.TryGetValue(propertyGroupName, out var propertyGroupEnum))
            {
                return propertyGroupEnum;
            }
            return 0;
        }
    }
}
