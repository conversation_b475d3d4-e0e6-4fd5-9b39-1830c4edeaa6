namespace WizardGames.Soc.Common.Framework.Types
{
    public static class TypeDefine
    {
        public const int INVALID = 0;

        public const int BASIC_TYPE_BEGIN = 1;

        public const int INT = 1;
        public const int LONG = 2;
        public const int FLOAT = 3;
        public const int DOUBLE = 4;
        public const int STRING = 5;
        public const int BOOL = 6;
        public const int BYTE = 7;
        public const int SHORT = 8;
        public const int USHORT = 9;
        public const int UINT = 10;
        public const int ULONG = 11;

        public const int BASIC_TYPE_END = 11;

        public const int CUSTOM_TYPE = 12;
        public const int CONTAINER = 13;
        /// <summary>
        /// 注意使用范围，并不是所有为null的地方都用了它
        /// </summary>
        public const int NULL = 14;
        /// <summary>
        /// NOT_BASIC只在客户端使用
        /// BASIC 虽然服务端也用，但不用于传输
        /// </summary>
        public const int NOT_BASIC = 98; // 非基础类型
        public const int BASIC = 99; // 基础类型

        /// <summary>
        /// 子线程写入CustomType时的临时状态
        /// </summary>
        public const int ARRAY_DATA_SET = 100;
        /// <summary>
        /// 由MsgPack反序列化而来的long型数字，不确定位宽
        /// ulong也是强转成long存储的
        /// </summary>
        public const int INTEGER_NUMBER = 101;

        /// <summary>
        /// 当类型明确时，返回是否为基本类型
        /// BASIC、NOT_BASIC、INTEGER_NUMBER不应当使用此函数判断
        /// </summary>
        public static bool IsBasicType(int typeConst)
        {
            return typeConst >= BASIC_TYPE_BEGIN && typeConst <= BASIC_TYPE_END;
        }


        public static string BasicTypeConstToTypeName(int typeConst)
        {
            return typeConst switch
            {
                INT => typeof(int).Name,
                LONG => typeof(long).Name,
                FLOAT => typeof(float).Name,
                DOUBLE => typeof(double).Name,
                STRING => typeof(string).Name,
                BOOL => typeof(bool).Name,
                BYTE => typeof(byte).Name,
                SHORT => typeof(short).Name,
                USHORT => typeof(ushort).Name,
                UINT => typeof(uint).Name,
                ULONG => typeof(ulong).Name,
                _ => "Invalid",
            };
        }
    }
}
