using System.Collections.Generic;

namespace WizardGames.Soc.Common.Framework.Const
{
 #if UNITY_5_6_OR_NEWER
    [UnityEngine.Scripting.Preserve]
#endif
    public class CustomTypeInfo
    {
        public int Hash;
        public short[] PropertyIds;
        public byte[] PropertyTypes;
        public short[] PersistentPropertyIds;
        public short[] OwnClientPropertyIds;
        public short[] OtherClientPropertyIds;
        public short[] ClientsPropertyIds;
        public short[] UnityDsPropertyIds;
        public int[] CustomHashValueArray;
        public byte[] SyncRanges;
        public bool[] SyncDelays;
        public short[] PropId2Index;
        public int[] RefIndex2Id;
        public byte[] LodArray;
        public short ValueTypeCount;
        public short RefTypeCount;
        public int LodBits;
        public int ClassLod;
        public Dictionary<int, byte[]> MethodInfo = new();
        public bool ShouldSerializeValueTypeCount() => ValueTypeCount != 0;
        public bool ShouldSerializeRefTypeCount() => RefTypeCount != 0;
        public bool ShouldSerializeLodBits() => LodBits != 0;
        public bool ShouldSerializeClassLod() => ClassLod != 0;
    }

#if UNITY_5_6_OR_NEWER
    [UnityEngine.Scripting.Preserve]
#endif
    public class CustomTypeFileInfo
    {
        public string Version;
        public List<CustomTypeInfo> CustomTypeInfos = new();
    }
}
