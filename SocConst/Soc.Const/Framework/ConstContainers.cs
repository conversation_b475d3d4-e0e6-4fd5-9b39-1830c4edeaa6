using System;
using System.Collections;
using System.Collections.Generic;

namespace WizardGames.Soc.Common.Algorithm
{

    public class ConstList<T> : IList<T>
    {
        public struct Enumerator : IEnumerator<T>
        {
            private readonly ConstList<T> _list;
            private int _index;
            private T _current;

            internal Enumerator(ConstList<T> list)
            {
                _list = list;
                _index = 0;
                _current = default;
            }

            public void Dispose()
            {
            }

            public bool MoveNext()
            {
                ConstList<T> localList = _list;

                if ((uint)_index < (uint)localList.Count)
                {
                    _current = localList[_index];
                    _index++;
                    return true;
                }
                return false;
            }

            public T Current => _current;

            object IEnumerator.Current => _current;

            void IEnumerator.Reset()
            {
                _index = 0;
                _current = default;
            }
        }

        private readonly T[] valueArray;
        public ConstList(params T[] args)
        {
            valueArray = args;
        }
        public T this[int index] { get => valueArray[index]; set => throw new System.NotSupportedException(); }

        public int Count => valueArray.Length;

        public bool IsReadOnly => true;

        public void Add(T item) => throw new System.NotSupportedException();

        public void Clear() => throw new System.NotSupportedException();

        public bool Contains(T item)
        {
            foreach (var val in valueArray)
            {
                if (EqualityComparer<T>.Default.Equals(item, val)) return true;
            }
            return false;
        }

        public void CopyTo(T[] array, int arrayIndex) => valueArray.CopyTo(array, arrayIndex);

        public IEnumerator<T> GetEnumerator() => new Enumerator(this);

        public int IndexOf(T item)
        {
            for (var i = 0; i < valueArray.Length; i++)
            {
                if (EqualityComparer<T>.Default.Equals(item, valueArray[i])) return i;
            }
            return -1;
        }

        public void Insert(int index, T item) => throw new NotSupportedException();

        public bool Remove(T item) => throw new NotSupportedException();

        public void RemoveAt(int index) => throw new NotSupportedException();

        IEnumerator IEnumerable.GetEnumerator() => valueArray.GetEnumerator();
    }

    public class ConstHashSet<T>: ISet<T>
    {
        private readonly HashSet<T> hashSet;
        public ConstHashSet(params T[] args)
        {
            hashSet = new HashSet<T>(args);
        }
        public int Count => hashSet.Count;

        public bool IsReadOnly => true;

        public bool Add(T item) => throw new NotSupportedException();

        public void Clear() => throw new NotSupportedException();

        public bool Contains(T item) => hashSet.Contains(item);

        public void CopyTo(T[] array, int arrayIndex) => hashSet.CopyTo(array, arrayIndex);

        public void ExceptWith(IEnumerable<T> other) => throw new NotSupportedException();

        public IEnumerator<T> GetEnumerator() => hashSet.GetEnumerator();

        public void IntersectWith(IEnumerable<T> other) => throw new NotSupportedException();

        public bool IsProperSubsetOf(IEnumerable<T> other) => hashSet.IsProperSubsetOf(other);

        public bool IsProperSupersetOf(IEnumerable<T> other) => hashSet.IsProperSupersetOf(other);

        public bool IsSubsetOf(IEnumerable<T> other) => hashSet.IsSubsetOf(other);

        public bool IsSupersetOf(IEnumerable<T> other) => hashSet.IsSupersetOf(other);

        public bool Overlaps(IEnumerable<T> other) => hashSet.Overlaps(other);

        public bool Remove(T item) => throw new NotSupportedException();

        public bool SetEquals(IEnumerable<T> other) => hashSet.SetEquals(other);

        public void SymmetricExceptWith(IEnumerable<T> other) => throw new NotSupportedException();

        public void UnionWith(IEnumerable<T> other) => throw new NotSupportedException();

        void ICollection<T>.Add(T item) => throw new NotSupportedException();

        IEnumerator IEnumerable.GetEnumerator() => hashSet.GetEnumerator();
    }   
}
