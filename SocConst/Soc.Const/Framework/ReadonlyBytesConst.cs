using System.Collections.Generic;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Share;

namespace WizardGames.Soc.Common.Framework.Const
{
    public class ReadonlyBytesConst
    {
        public const string CLASS_NAME = "ReadonlyBytes";
        public const string FIELD_TYPE_NAME = "FixedBytes";
        public const string FIELD_NAME = "_bytes";
        public const int CLASS_HASH = 1288966629;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int BYTES = 0;
        }
#pragma warning restore CS0108
        public static readonly string[] PropNameArray = new string[]
        {
            char.ToUpper(FIELD_NAME.Replace("_", "")[0]) + FIELD_NAME.Replace("_", "").Substring(1)
        };
        public static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.BYTES
        };
        public static readonly int[] PropertyTypeArray = new int[]
        {
            TypeDefine.FIXED_BYTES
        };
        public static readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.BYTES
        };
        public static readonly HashSet<int> PersistentPropertyIds = new()
        {
            PropertyIds.BYTES
        };
        public static readonly int[] OwnClientPropertyInfoArray = new int[]
        {
            PropertyIds.BYTES
        };
        public static readonly int[] OtherClientPropertyInfoArray = new int[]
        {
            PropertyIds.BYTES
        };
        public static readonly int[] ClientsPropertyInfoArray = new int[]
        {
            (TypeDefine.FIXED_BYTES << 16) | PropertyIds.BYTES
        };
        public static readonly int[] UnityDsPropertyInfoArray = new int[]
        {
            PropertyIds.BYTES
        };
        public static readonly int[] CustomHashValueArray = new int[]
        {
            0
        };
        public static readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.All
        };
        public static readonly bool[] SyncDelayArray = new bool[]
        {
            false
        };
        public static readonly int[] LodArray = new int[]
        {
        };

        public static readonly int[] PropId2Index = new int[]
        {
            CustomTypeConst.REF_PROPERTY_INDEX_OFFSET + 0
        };
        public static readonly int[] RefIndexInfoArray = new int[]
        {
            PropertyIds.BYTES
        };
        public const int VALUE_TYPE_COUNT = 0;
        public const int REF_TYPE_COUNT = 1;
    }
}
