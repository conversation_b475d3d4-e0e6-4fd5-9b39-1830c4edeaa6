using System.Runtime.CompilerServices;

namespace WizardGames.Soc.Common.Framework.Const
{
    public static class CustomLod
    {
        /// <summary>
        /// 仅手工全量订阅下发
        /// </summary>
        public const int LOD_FULL = 1 << 0;
        /// <summary>
        /// 10米范围内
        /// </summary>
        public const int LOD_10M = 1 << 1;
        /// <summary>
        /// 50米范围内
        /// </summary>
        public const int LOD_50M = 1 << 2;
        /// <summary>
        /// 128米范围，九宫格
        /// </summary>
        public const int LOD_MEDIUM = 1 << 3;
        /// <summary>
        /// 256米范围内，二十五格
        /// </summary>
        public const int LOD_FAR = 1 << 4;

        /// <summary>
        /// 仅全局同步的Entity的字段使用
        /// </summary>
        public const int LOD_GLOBAL_SYNC = LOD_50M;

        // 后面的这些定义是给框架用的，不要在业务代码中使用
        public const int INNER_LOD_BRIEF = 1 << 5;
        public const int INNER_LOD_DISTANCE_MASK = LOD_10M | LOD_50M | LOD_MEDIUM | LOD_FAR;
        public const int INNER_LOD_ALL = LOD_FULL | LOD_50M | LOD_MEDIUM | LOD_FAR | INNER_LOD_BRIEF;
        public const int INNER_LOD_SUBSCRIBE_MASK = LOD_FULL | INNER_LOD_BRIEF;
    }

    public enum ESyncRange
    {
        LocalOnly = 0,          // 不同步给其它端
        OwnClient = 1 << 0,     // 同步给该Entity的主控客户端（PlayerAoiSubscriber的MyEntityId）
        OtherClients = 1 << 1,  // 同步给除了该Entity主控客户端之外的所有客户端
        UnityDs = 1 << 2,       //Simulator

        All = OwnClient | OtherClients | UnityDs,  // 所有Entity同步端
        Clients = OwnClient | OtherClients,     // 只有客户端
        OwnAndUnityDs = OwnClient | UnityDs,
        OtherAndUnityDs = OtherClients | UnityDs,
    }

    /// <summary>
    /// 属性在自动生成后的默认访问权限
    /// </summary>
    public enum EFieldAccess
    {
        /// <summary>
        /// getter setter均为公开访问
        /// </summary>
        Public,
        /// <summary>
        /// getter为公开访问，setter为私有访问
        /// </summary>
        PrivateSet,
        /// <summary>
        /// getter为公开访问，setter为受保护访问
        /// </summary>
        ProtectedSet,
        /// <summary>
        /// getter为公开访问，setter为包内访问
        /// </summary>
        InternalSet,
        /// <summary>
        /// getter setter均为私有访问
        /// </summary>
        Private,
        /// <summary>
        /// getter setter均为受保护访问
        /// </summary>
        Protected,
        /// <summary>
        /// getter setter均为包内访问
        /// </summary>
        Internal,
    }

    /// <summary>
    /// 属性快速读取方式
    /// </summary>
    public enum EFieldLocal
    {
        None = 0, // 不支持快速读取
        Client = 1 << 0, // 只在客户端
        UnityDs = 1 << 1, // 只在Simulator
        All = Client | UnityDs, // 客户端和Simulator都可以快速读取
    }

    public static class SyncRangeMapper
    {
        // ANY, 0 => 0 == range & outerRange
        // 1, 1 => 1 == range & outerRange
        // 2, 1 => 0 == range & outerRange
        // 3, 1 => 1 == range & outerRange
        // 1, 2 => 0 == range & outerRange
        // 2, 2 => 2 == range & outerRange
        // 3, 2 => 2 == range & outerRange
        // 1, 3 => 1 == range & outerRange
        // 2, 3 => 2 == range & outerRange
        // 3, 3 => 3 == range & outerRange
        //public static SyncRange Shrink(this SyncRange range, SyncRange outerRange) => range & outerRange;

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static ESyncRange Shrink(this ESyncRange range, ESyncRange outerRange)
        {
            return range & outerRange;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool Contains(this ESyncRange range, ESyncRange outerRange)
        {
            return ((range & outerRange) == outerRange);
        }
    }

    /// <summary>
    /// 定义Entity属性可以在哪一端修改
    /// </summary>
    public enum FieldModifier
    {
        /// <summary>
        /// Entity,Component上定义Field时使用
        /// </summary>
        World = 1 << 0,         // 只能在world修改
        UnityDs = 1 << 1,       // 只能在Simulator和Auxiliary修改，目前Simulator和Auxiliary是同一个工程， 代码生成没法区分

        /// <summary>
        /// 只能用在CustomType的Field上
        /// </summary>
        All = World | UnityDs,
    }

    public enum ESerializeMode
    {
        All,
        Db,
        OwnClient,
        OtherClients,
        UnityDs,
        //给arrayDataSet用
        Clients, // OwnClient && OtherClients
    }

    public static class RpcConst
    {
        public const int MAX_PARAM_COUNT = 8;

        #region Client Limit Call
        public const int DEF_RPC_MIN_INTERVAL_MSSEC = 333;
        public const int DEF_RPC_DUR_SEC = 5;
        public const int DEF_RPC_DUR_MSSEC = DEF_RPC_DUR_SEC * 1000;
        public const int DEF_RPC_DUR_COUNT = (1000 / DEF_RPC_MIN_INTERVAL_MSSEC) * DEF_RPC_DUR_SEC;
        #endregion
    }
}
