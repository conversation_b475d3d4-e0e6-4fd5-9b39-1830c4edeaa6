namespace WizardGames.Soc.Common.Const
{
    /// <summary>
    /// 物品类型
    /// </summary>
    public class ItemType
    {
        /// <summary>
        /// 建造
        /// </summary>
        public const int Build = 10;
        /// <summary>
        /// 物品
        /// </summary>
        public const int Item = 11;
        /// <summary>
        /// 资源
        /// </summary>
        public const int Resources = 12;
        /// <summary>
        /// 服饰
        /// </summary>
        public const int Cloth = 13;
        /// <summary>
        /// 工具
        /// </summary>
        public const int Tool = 14;
        /// <summary>
        /// 武器
        /// </summary>
        public const int Weapon = 16;
        /// <summary>
        /// 弹药
        /// </summary>
        public const int Ammo = 17;
        /// <summary>
        /// 电力
        /// </summary>
        public const int Electric = 19;
        /// <summary>
        /// 杂项
        /// </summary>
        public const int Misc = 22;
        /// <summary>
        /// 消耗品
        /// </summary>
        public const int Expendable = 23;
        /// <summary>
        /// 蓝图
        /// </summary>
        public const int Blueprint = 25;
        /// <summary>
        /// 玩法
        /// </summary>
        public const int Gameplay = 26;
        /// <summary>
        /// 礼包
        /// </summary>
        public const int Bundle = 27;
        /// <summary>
        /// 蓝图道具
        /// </summary>
        public const int BlueprintItem = 60;
    }

    //luban 工具检测
    public class ItemSubType
    {
        /// <summary>
        ///功能性道具
        /// </summary>
        public const int Functional = 1;
        /// <summary>
        ///装饰性道具
        /// </summary>
        public const int Decorative = 2;
    }
    public class ResourceSubType
    {
        /// <summary>
        ///动物
        /// </summary>
        public const int Animal = 1;
        /// <summary>
        ///矿石
        /// </summary>
        public const int Ore = 2;
        /// <summary>
        ///植物
        /// </summary>
        public const int Plant = 3;
        /// <summary>
        ///工业木材
        /// </summary>
        public const int IndustryWood = 4;
        /// <summary>
        ///工业制品
        /// </summary>
        public const int IndustryProducts = 5;
    }

    public class BuildingSubType
    {
        public const int CoreBuildings = 1;
    }

    public class BlurprintSubType
    {
        public const int BlueprintTool = 1;
        public const int ElectricBlueprint = 2;
        public const int ConstructionBlueprint = 3;
    }

    public class WeaponSubType
    {
        /// <summary>
        /// 枪械
        /// </summary>
        public const int Firearms = 1;
        /// <summary>
        /// 近战武器
        /// </summary>
        public const int Melee = 2;
        /// <summary>
        /// 配件
        /// </summary>
        public const int Accessories = 3;
        /// <summary>
        /// 投掷物
        /// </summary>
        public const int Throwing = 4;
    }

    public class ExpendableSubType
    {
        /// <summary>
        ///肉类
        /// </summary>
        public const int Meat = 1;
        /// <summary>
        ///果实
        /// </summary>
        public const int Fruit = 2;
        /// <summary>
        ///茶
        /// </summary>
        public const int Tea = 3;
        /// <summary>
        ///零食
        /// </summary>
        public const int Snack = 4;
        /// <summary>
        ///药品
        /// </summary>
        public const int Medicine = 6;
        /// <summary>
        ///种子
        /// </summary>
        public const int Seed = 7;
        /// <summary>
        ///藏宝图
        /// </summary>
        public const int TreasureMap = 8;
    }

    public class ToolSubType
    {
        /// <summary>
        ///工具
        /// </summary>
        public const int Tool = 1;
        /// <summary>
        ///杂货道具
        /// </summary>
        public const int Groceries = 3;
    }

    public class ElectricSubType
    {
        public const int Power = 1;
        public const int Circuit = 2;
        public const int Appliances = 3;
    }

    public class GameplaySubType
    {
        public const int Tool = 1;
        public const int Exhibition = 2;
    }

    public class MiscSubType
    {
        public const int PoiTaskItem = 6;
    }
}

