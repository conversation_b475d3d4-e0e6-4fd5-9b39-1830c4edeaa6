<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
    <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
    <ImplicitUsings>disable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<DefaultItemExcludes>$(DefaultItemExcludes);**\*.meta;**\*.asmdef;**\package.json;**\Lib\*</DefaultItemExcludes>
		<RootNamespace>WizardGames.SocConst</RootNamespace>
    <EnforceCodeStyleInBuild>True</EnforceCodeStyleInBuild>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)'=='Debug'">
		<NoWarn>1701;1702;8601;8603;8618</NoWarn>
		<TreatWarningsAsErrors>True</TreatWarningsAsErrors>
		<Deterministic>True</Deterministic>
    <DebugType>portable</DebugType>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)'=='Release'">
		<NoWarn>1701;1702;8601;8603;8618</NoWarn>
		<TreatWarningsAsErrors>True</TreatWarningsAsErrors>
		<Deterministic>True</Deterministic>
		<Optimize>True</Optimize>
    <DebugType>portable</DebugType>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)'=='PUBLISH'">
		<NoWarn>1701;1702;8601;8603;8618</NoWarn>
		<TreatWarningsAsErrors>True</TreatWarningsAsErrors>
		<Deterministic>True</Deterministic>
		<Optimize>True</Optimize>
    <DebugType>portable</DebugType>
	</PropertyGroup>

</Project>
