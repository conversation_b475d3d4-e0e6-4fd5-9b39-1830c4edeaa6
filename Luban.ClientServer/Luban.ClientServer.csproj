<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <StartupObject>Luban.ClientServer.Program</StartupObject>
    <PackageProjectUrl>https://github.com/focus-creative-games/luban</PackageProjectUrl>
    <EnforceCodeStyleInBuild>True</EnforceCodeStyleInBuild>
    <PackageReadmeFile>README.md</PackageReadmeFile>
    <OutputPath>..\..\..\Tool\Luban.ClientServer\</OutputPath>
	<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
  </PropertyGroup>

  <ItemGroup>
    <None Include="..\..\README.md">
      <Pack>True</Pack>
      <PackagePath>\</PackagePath>
    </None>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Luban.Client\Luban.Client.csproj" />
    <ProjectReference Include="..\Luban.Server\Luban.Server.csproj" />
  </ItemGroup>

</Project>
