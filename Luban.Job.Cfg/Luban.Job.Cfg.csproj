<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <PackageProjectUrl>https://github.com/focus-creative-games/luban</PackageProjectUrl>
    <EnforceCodeStyleInBuild>True</EnforceCodeStyleInBuild>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Source\TypeVisitors\Proto\**" />
    <EmbeddedResource Remove="Source\TypeVisitors\Proto\**" />
    <None Remove="Source\TypeVisitors\Proto\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.95.4" />
    <PackageReference Include="ExcelDataReader" Version="3.6.0" />
    <PackageReference Include="MessagePack" Version="2.3.85" />
    <PackageReference Include="NeoLua" Version="1.3.14" />
    <PackageReference Include="Newtonsoft.Json.Bson" Version="1.0.2" />
    <PackageReference Include="Ude.NetStandard" Version="1.2.0" />
    <PackageReference Include="YamlDotNet" Version="11.2.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Luban.Job.Common\Luban.Job.Common.csproj" />
  </ItemGroup>

</Project>
