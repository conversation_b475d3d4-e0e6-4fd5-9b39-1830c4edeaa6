using CommandLine;
using DocumentFormat.OpenXml.Math;
using DocumentFormat.OpenXml.Wordprocessing;
using Luban.Common.Protos;
using Luban.Common.Utils;
using Luban.Job.Cfg.DataCreators;
using Luban.Job.Cfg.Datas;
using Luban.Job.Cfg.Defs;
using Luban.Job.Cfg.Generate;
using Luban.Job.Cfg.RawDefs;
using Luban.Job.Cfg.Utils;
using Luban.Job.Common.Defs;
using Luban.Job.Common.RawDefs;
using Luban.Job.Common.Types;
using Luban.Job.Common.Utils;
using Luban.Server.Common;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.IO.Pipes;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using FileInfo = Luban.Common.Protos.FileInfo;

namespace Luban.Job.Cfg
{
    [Controller("cfg")]
    public class JobController : IJobController
    {
        private static readonly NLog.Logger s_logger = NLog.LogManager.GetCurrentClassLogger();

        private static bool TryParseArg(List<string> args, out GenArgs options, out string errMsg)
        {
            var helpWriter = new StringWriter();
            var parser = new Parser(ps =>
            {
                ps.HelpWriter = helpWriter;
            }); ;
            var parseResult = parser.ParseArguments<GenArgs>(args);
            if (parseResult.Tag == ParserResultType.NotParsed)
            {
                errMsg = helpWriter.ToString();
                options = null;
                return false;
            }
            else
            {
                options = (parseResult as Parsed<GenArgs>).Value;
                errMsg = null;

                string inputDataDir = options.InputDataDir;
                string outputCodeDir = options.OutputCodeDir;
                string outputDataDir = options.OutputDataDir;

                var genTypes = options.GenType.Split(',').Select(s => s.Trim()).ToList();

                if (genTypes.Any(t => t.StartsWith("code_", StringComparison.Ordinal)) && string.IsNullOrWhiteSpace(outputCodeDir))
                {
                    errMsg = "--outputcodedir missing";
                    return false;
                }
                if (genTypes.Any(t => t.StartsWith("data_", StringComparison.Ordinal)))
                {
                    if (string.IsNullOrWhiteSpace(inputDataDir))
                    {
                        errMsg = "--inputdatadir missing";
                        return false;
                    }
                    if (string.IsNullOrWhiteSpace(outputDataDir))
                    {
                        errMsg = "--outputdatadir missing";
                        return false;
                    }
                    if (genTypes.Contains("data_resources") && string.IsNullOrWhiteSpace(options.OutputDataResourceListFile))
                    {
                        errMsg = "--output_data_resource_list_file missing";
                        return false;
                    }
                    if (genTypes.Contains("output_data_json_monolithic_file") && string.IsNullOrWhiteSpace(options.OutputDataJsonMonolithicFile))
                    {
                        errMsg = "--output_data_json_monolithic_file missing";
                        return false;
                    }

                    if (string.IsNullOrWhiteSpace(options.L10nInputTextTableFiles) ^ string.IsNullOrWhiteSpace(options.L10nOutputNotTranslatedTextFile))
                    {
                        errMsg = "--input_l10n_text_files must be provided with --output_l10n_not_translated_text_file";
                        return false;
                    }
                    if (genTypes.Contains("data_template") ^ !string.IsNullOrWhiteSpace(options.TemplateDataFile))
                    {
                        errMsg = "gen_types data_template should use with --template:data:file";
                        return false;
                    }
                    if (genTypes.Contains("convert_template") ^ !string.IsNullOrWhiteSpace(options.TemplateConvertFile))
                    {
                        errMsg = "gen_types convert_template should use with --template:convert:file";
                        return false;
                    }
                    if (genTypes.Contains("code_template") ^ !string.IsNullOrWhiteSpace(options.TemplateCodeDir))
                    {
                        errMsg = "gen_types code_template should use with --template:code:dir";
                        return false;
                    }
                }

                if (string.IsNullOrWhiteSpace(options.L10nPatchName) ^ string.IsNullOrWhiteSpace(options.L10nPatchInputDataDir))
                {
                    errMsg = "--patch must be provided with --patch_input_data_dir";
                    return false;
                }

                if (options.GenType.Contains("typescript_bin") && !options.ValidateTypescriptRequire(options.GenType, ref errMsg))
                {
                    return false;
                }
                if (options.GenType.Contains("go_") && !options.ValidateGoRequire(options.GenType, ref errMsg))
                {
                    return false;
                }
                if (!options.ValidateConvention(ref errMsg))
                {
                    return false;
                }

                if (options.GenType.Contains("unity"))
                {
                    options.CsUseUnityVectors = true;
                }

                return true;
            }
        }

        public async Task GenAsync(RemoteAgent agent, GenJob rpc)
        {
            var res = new GenJobRes()
            {
                ErrCode = Luban.Common.EErrorCode.OK,
                ErrMsg = "succ",
                FileGroups = new List<FileGroup>(),
            };

            if (!TryParseArg(rpc.Arg.JobArguments, out GenArgs args, out string errMsg))
            {
                res.ErrCode = Luban.Common.EErrorCode.JOB_ARGUMENT_ERROR;
                res.ErrMsg = errMsg;
                agent.Session.ReplyRpc<GenJob, GenJobArg, GenJobRes>(rpc, res);
                return;
            }

            var timer = new ProfileTimer();
            timer.StartPhase("= gen_all =");
            try
            {
                string inputDataDir = args.InputDataDir;
                string outputCodeDir = args.OutputCodeDir;
                string outputDataDir = args.OutputDataDir;

                var genTypes = args.GenType.Split(',').Select(s => s.Trim()).ToList();

                timer.StartPhase("build defines");
                var loader = new CfgDefLoader(agent);
                // [ALB] 读取'__root__.xml'
                await loader.LoadAsync(args.DefineFile);
                // [ALB] 读取所有表格数据类型
                await loader.LoadDefinesFromFileAsync(inputDataDir);
                timer.EndPhaseAndLog();

                var rawDefines = loader.BuildDefines();

                TimeZoneInfo timeZoneInfo = string.IsNullOrEmpty(args.L10nTimeZone) ? null : TimeZoneInfo.FindSystemTimeZoneById(args.L10nTimeZone);

                var excludeTags = args.OutputExcludeTags.Split(',').Select(t => t.Trim().ToLowerInvariant()).Where(t => !string.IsNullOrEmpty(t)).ToList();
                var ass = new DefAssembly(args.L10nPatchName, timeZoneInfo, excludeTags, agent);
                // [ALB] 根据读取的表格数据类型，生成对应的数据结构
                ass.Load(rawDefines, agent, args);

                List<DefTable> exportTables = ass.GetExportTables();
                List<DefTypeBase> exportTypes = ass.GetExportTypes();

                bool hasLoadCfgData = false;

                bool needL10NTextConvert = !string.IsNullOrWhiteSpace(args.L10nInputTextTableFiles);

                async Task CheckLoadCfgDataAsync()
                {
                    if (!hasLoadCfgData)
                    {
                        hasLoadCfgData = true;
                        var timer = new ProfileTimer();
                        timer.StartPhase("load config data");
                        // [ALB] 读取表格数据
                        await DataLoaderUtil.LoadCfgDataAsync(agent, ass, args.InputDataDir, args.L10nPatchName, args.L10nPatchInputDataDir, args.InputConvertDataDir);
                        timer.EndPhaseAndLog();

                        if (needL10NTextConvert)
                        {
                            ass.InitL10n(args.L10nTextValueFieldName);
                            await DataLoaderUtil.LoadTextTablesAsync(agent, ass, ".", args.L10nInputTextTableFiles);
                        }

                        timer.StartPhase("validate");
                        // [ALB] 检测表格数据合法性
                        var validateCtx = new ValidatorContext(ass, args.ValidateRootDir);
                        await validateCtx.ValidateTables(ass.GetAllTables());
                        timer.EndPhaseAndLog();
                    }
                }

                var tasks = new List<Task>();

                var genCodeFilesInOutputCodeDir = new ConcurrentBag<FileInfo>();
                var genDataFilesInOutputDataDir = new ConcurrentBag<FileInfo>();
                var genScatteredFiles = new ConcurrentBag<FileInfo>();

                foreach (var genType in genTypes)
                {
                    var ctx = new GenContext()
                    {
                        GenType = genType,
                        Assembly = ass,
                        GenArgs = args,
                        ExportTypes = exportTypes,
                        ExportTables = exportTables,
                        GenCodeFilesInOutputCodeDir = genCodeFilesInOutputCodeDir,
                        GenDataFilesInOutputDataDir = genDataFilesInOutputDataDir,
                        GenScatteredFiles = genScatteredFiles,
                        Tasks = tasks,
                        DataLoader = CheckLoadCfgDataAsync,
                    };
                    GenContext.Ctx = ctx;

                    var render = RenderFactory.CreateRender(genType);
                    if (render == null)
                    {
                        throw new Exception($"unknown gentype:{genType}");
                    }
                    if (render is DataRenderBase)
                    {
                        await CheckLoadCfgDataAsync();
                    }
                    // [ALB] CsCodeUnityJsonRender生成.cs代码内容，DataScatterRender生成.json数据内容，此时还未保存，后续还会比对MD5，以确定是否保存
                    render.Render(ctx);
                    GenContext.Ctx = null;
                }
                await Task.WhenAll(tasks.ToArray());

                if (needL10NTextConvert)
                {
                    var notConvertTextList = DataExporterUtil.GenNotConvertTextList(ass.NotConvertTextSet);
                    var md5 = FileUtil.CalcMD5(notConvertTextList);
                    string outputNotConvertTextFile = args.L10nOutputNotTranslatedTextFile;
                    CacheManager.Ins.AddCache(outputNotConvertTextFile, md5, notConvertTextList);

                    genScatteredFiles.Add(new FileInfo() { FilePath = outputNotConvertTextFile, MD5 = md5 });
                }

                if (!genCodeFilesInOutputCodeDir.IsEmpty)
                {
                    res.FileGroups.Add(new FileGroup() { Dir = outputCodeDir, Files = genCodeFilesInOutputCodeDir.ToList() });
                }
                if (!genDataFilesInOutputDataDir.IsEmpty)
                {
                    //多语言Key总结文件
                    var socLanguageFile = "language_default.json";
                    var socLanguageContent = DataExporterUtil.GenSocDataDict(ass.OutputCompactJson, ass.SocTextDict);
                    var socLanguageMd5 = CacheFileUtil.GenStringOrBytesMd5AndAddCache(socLanguageFile, socLanguageContent);
                    CacheManager.Ins.AddCache(socLanguageFile, socLanguageMd5, socLanguageContent);
                    genDataFilesInOutputDataDir.Add(new FileInfo() { FilePath = socLanguageFile, MD5 = socLanguageMd5 });

                    res.FileGroups.Add(new FileGroup() { Dir = outputDataDir, Files = genDataFilesInOutputDataDir.ToList() });
                }
                if (!genScatteredFiles.IsEmpty)
                {
                    res.ScatteredFiles.AddRange(genScatteredFiles);
                }

                if (args.ExportType == "client")
                {
                    s_logger.Info("WriteUiMultiLanExport");
                    WriteUiMultiLanExport(ass, agent, inputDataDir, outputCodeDir);
                    WriteResCheckExport(ass, agent, inputDataDir, outputCodeDir);
                    Tags.TableAssetRefWriter.Write(ass, agent, inputDataDir, outputCodeDir);
                    Tags.TableFieldCommentWriter.Write(ass, agent, inputDataDir, outputCodeDir);
                }
            }
            catch (DataCreateException e)
            {
                res.ErrCode = Luban.Common.EErrorCode.DATA_PARSE_ERROR;
                res.ErrMsg = $@"
=======================================================================
    解析失败!

        文件名:      {e.OriginDataLocation}
        文件路径:    {e.ActualDataLocation}
        错误位置:    {e.DataLocationInFile}
        Err:         {e.OriginErrorMsg}
        字段:        {e.VariableFullPathStr} 

=======================================================================
";
                res.StackTrace = e.OriginStackTrace;
            }
            catch (Exception e)
            {
                res.ErrCode = Luban.Common.EErrorCode.JOB_EXCEPTION;
                res.ErrMsg = $@"
=======================================================================

{ExceptionUtil.ExtractMessage(e)}

=======================================================================
";
                res.StackTrace = e.StackTrace;
            }
            DefAssemblyBase.LocalAssebmly = null;
            timer.EndPhaseAndLog();

            agent.Session.ReplyRpc<GenJob, GenJobArg, GenJobRes>(rpc, res);
        }

        private async void WriteUiMultiLanExport(DefAssembly ass, RemoteAgent agent, string inputDataDir, string outputCodeDir)
        {
            // 读取114_多语言_UI多语言，写入ui_muilt_lan_export.json（这个单词拼错了）
            // 手工定义这张表
            var uiMultiLanExportBeanDef = new DefBean(new CfgBean()
            {
                Namespace = "__intern__",
                Name = "__UiMultiLanExport__",
                Parent = "",
                Alias = "",
                IsValueType = false,
                Sep = "",
                TypeId = 0,
                IsSerializeCompatible = false,
                Fields = new List<Field>()
                    {
                        new CfgField() { Name = "index", Type = "int" },
                        new CfgField() { Name =  "text", Type = "string" },
                        new CfgField() { Name = "desc", Type = "string" },
                    }
            })
            {
                AssemblyBase = ass,
            };
            ass.AddType(uiMultiLanExportBeanDef);
            uiMultiLanExportBeanDef.PreCompile();
            uiMultiLanExportBeanDef.Compile();
            uiMultiLanExportBeanDef.PostCompile();
            var uiMultiLanExportBean = TBean.Create(false, uiMultiLanExportBeanDef, null);
            var fileContent = await agent.GetFromCacheOrReadAllBytesAsync(Path.Join(inputDataDir, "114_多语言_UI静态文本多语言.xlsx"), "");
            var records = DataLoaderUtil.LoadCfgRecords(uiMultiLanExportBean, "114_多语言_UI静态文本多语言.xlsx", "UI多语言表", fileContent, true);

            using var ss = new FileStream(Path.Join(outputCodeDir, "../..", "Resources", "Data", "Language", "Default", "ui_muilt_lan_export.json"), FileMode.Create);
            var jsonWriter = new Utf8JsonWriter(ss, new JsonWriterOptions()
            {
                Indented = true,
                SkipValidation = false,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
            });

            jsonWriter.WriteStartArray();
            foreach (var r in records)
            {
                jsonWriter.WriteStartObject();
                jsonWriter.WritePropertyName("index");
                jsonWriter.WriteStartArray();
                jsonWriter.WriteNumberValue((r.Data.GetField("index") as DInt).Value);
                jsonWriter.WriteEndArray();
                jsonWriter.WritePropertyName("text");
                jsonWriter.WriteStringValue((r.Data.GetField("text") as DString).Value);
                jsonWriter.WritePropertyName("desc");
                jsonWriter.WriteStringValue((r.Data.GetField("desc") as DString).Value);
                jsonWriter.WriteEndObject();
            }
            jsonWriter.WriteEndArray();
            jsonWriter.Flush();
            ss.Flush();
        }

        private void WriteResCheckAllExport(DefAssembly ass, string path)
        {
            using var ss = new FileStream(path, FileMode.Create);
            var jsonWriter = new Utf8JsonWriter(ss, new JsonWriterOptions()
            {
                Indented = true,
                SkipValidation = false,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
            });


            var socResCheckList = ass.SocResCheckDict.Values.ToList();
            socResCheckList.Sort((data1, data2) =>
            {
                return data1.HashCode.CompareTo(data2.HashCode);
            });

            jsonWriter.WriteStartArray();
            foreach (var d in socResCheckList)
            {
                jsonWriter.WriteStartObject();
                jsonWriter.WritePropertyName(DSocString.LocationField);
                jsonWriter.WriteStringValue(d.UniqueKey);
                jsonWriter.WritePropertyName(DSocString.ContentField);
                jsonWriter.WriteStringValue(d.Value);
                jsonWriter.WritePropertyName(DSocString.RowIdFiled);
                jsonWriter.WriteStringValue(d.RowId);
                jsonWriter.WritePropertyName(DSocString.FilePathFiled);
                jsonWriter.WriteStringValue(d.FileName);
                jsonWriter.WritePropertyName(DSocString.TitleNameFiled);
                jsonWriter.WriteStringValue(d.TitleName);

                jsonWriter.WriteEndObject();
            }
            jsonWriter.WriteEndArray();
            jsonWriter.Flush();
            ss.Flush();
        }

        private void WriteResCheckDiffExport(Dictionary<string, DSocString> diff, string path)
        {
            using var ss = new FileStream(path, FileMode.Create);
            var jsonWriter = new Utf8JsonWriter(ss, new JsonWriterOptions()
            {
                Indented = true,
                SkipValidation = false,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
            });

            var socResCheckList = diff.Values.ToList();
            socResCheckList.Sort((data1, data2) =>
            {
                return data1.HashCode.CompareTo(data2.HashCode);
            });

            jsonWriter.WriteStartArray();
            foreach (var d in socResCheckList)
            {
                jsonWriter.WriteStartObject();
                jsonWriter.WritePropertyName(DSocString.LocationField);
                jsonWriter.WriteStringValue(d.UniqueKey);
                jsonWriter.WritePropertyName(DSocString.ContentField);
                jsonWriter.WriteStringValue(d.Value);
                jsonWriter.WritePropertyName(DSocString.RowIdFiled);
                jsonWriter.WriteStringValue(d.RowId);
                jsonWriter.WritePropertyName(DSocString.FilePathFiled);
                jsonWriter.WriteStringValue(d.FileName);
                jsonWriter.WritePropertyName(DSocString.TitleNameFiled);
                jsonWriter.WriteStringValue(d.TitleName);
                jsonWriter.WriteEndObject();
            }
            jsonWriter.WriteEndArray();
            jsonWriter.Flush();
            ss.Flush();
        }

        private class ResCheckContent
        {
            public string Location = string.Empty;
            public string Content = string.Empty;
            public int Id = 0;
        }

        private void WriteResCheckExport(DefAssembly ass, RemoteAgent agent, string inputDataDir, string outputCodeDir)
        {
            // 读取上一次结果
            var lastAllPath = Path.Join(outputCodeDir, "../..", "Resources", "Data", "ResCheck", "res_check.json");
            var diffAllPath = Path.Join(outputCodeDir, "../..", "Resources", "Data", "ResCheck", "res_check_diff.json");

            // 上一次有结果，根据信息生成diff
            if (File.Exists(lastAllPath))
            {
                using var lastss = new FileStream(lastAllPath, FileMode.Open);
                using (StreamReader reader = new StreamReader(lastss))
                {
                    string content = reader.ReadToEnd();
                    var items = JsonConvert.DeserializeObject<ResCheckContent[]>(content);
                    var origin = new HashSet<string>();
                    var diff = new Dictionary<string, DSocString>();
                    foreach (var item in items) origin.Add(item.Location);


                    foreach (var item in ass.SocResCheckDict.Values)
                    {
                        if (!origin.Contains(item.UniqueKey))
                        {
                            diff.Add(item.UniqueKey, item);
                        }
                    }

                    WriteResCheckDiffExport(diff, diffAllPath);
                }
            }

            // 输出全量
            WriteResCheckAllExport(ass, lastAllPath);
        }
    }
}
