// [ALB]
using Luban.Job.Cfg.Datas;
using Luban.Job.Common;
using Luban.Job.Common.Defs;
using Luban.Job.Common.RawDefs;
using Luban.Job.Common.Source.Types;
using Luban.Job.Common.Types;
using Luban.Job.Common.TypeVisitors;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

namespace Luban.Job.Cfg.TypeVisitors
{
    /// <summary>
    /// 获取Bean的全名称，包括namespace
    /// </summary>
    public class CsDefineTypeFullName : DecoratorFuncVisitor<string>
    {
        public static CsDefineTypeFullName Ins { get; } = new CsDefineTypeFullName();

        protected virtual ITypeFuncVisitor<string> UnderlyingVisitor => CsUnderingDefineTypeName.Ins;

        public override string DoAccept(TType type)
        {
            return type.IsNullable && !type.Apply(CsIsRawNullableTypeVisitor.Ins) ? (type.Apply(UnderlyingVisitor) + "?") : type.Apply(UnderlyingVisitor);
        }

        public override string Accept(TEnum type)
        {
            return type.DefineEnum.FullNameWithTopModule;
        }

        public override string Accept(TBean type)
        {
            return type.Bean.FullNameWithTopModule;
        }

        public override string Accept(TArray type)
        {
            return $"{type.ElementType.Apply(this)}[]";
        }

        public override string Accept(TList type)
        {
            return $"{ConstStrings.CsList}<{type.ElementType.Apply(this)}>";
        }

        public override string Accept(TSet type)
        {
            return $"{ConstStrings.CsHashSet}<{type.ElementType.Apply(this)}>";
        }

        public override string Accept(TMap type)
        {
            return $"{ConstStrings.CsHashMap}<{type.KeyType.Apply(this)}, {type.ValueType.Apply(this)}>";
        }
    }
}
