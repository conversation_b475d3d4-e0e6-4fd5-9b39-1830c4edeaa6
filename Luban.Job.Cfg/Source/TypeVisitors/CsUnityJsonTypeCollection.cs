using Luban.Job.Common.Types;
using System.Collections.Generic;

namespace Luban.Job.Cfg.TypeVisitors
{
    public class CsTextType
    {
        public string TypeName { get; set; }
        public string KeyName { get; set; }
        public TText Type { get; set; }
        public bool IsNullable => Type.IsNullable;
    }

    public class CsValueType
    {
        public TType Type { get; set; }
        public string TypeName { get; set; }

        public bool IsBool => Type is TBean;
        public bool IsNumber { get; set; }
        public bool IsString => !IsNumber && Type is TString;
        public bool IsObject { get; set; }
        public bool IsNullable => Type.IsNullable;

        public CsValueType(TType type, bool isNumber)
        {
            Type = type;
            TypeName = type.Apply(CsDefineTypeFullName.Ins);
            IsNumber = isNumber;
            IsObject = !isNumber;
        }
    }

    public class CsEnumType
    {
        public TEnum Type { get; set; }
        public string TypeName { get; set; }
        public bool IsNullable => Type.IsNullable;

        public CsEnumType(TEnum type)
        {
            Type = type;
            TypeName = type.Apply(CsDefineTypeFullName.Ins);
        }
    }

    public class CsBeanType
    {
        public TBean Type { get; set; }
        public string TypeName { get; set; }
        public bool IsNullable => Type.IsNullable;

        public CsBeanType(TBean type)
        {
            Type = type;
            TypeName = type.Apply(CsDefineTypeFullName.Ins);
        }
    }

    public class CsArrayType
    {
        public TArray Type { get; set; }
        public string TypeName { get; set; }
        public string ElementTypeName { get; set; }

        public bool IsElementEnumType => Type.ElementType.IsEnum;
        public bool IsElementBeanType => Type.ElementType.IsBean;
        public bool IsConvertable { get; set; }
        public bool IsNullable => Type.IsNullable;

        public CsArrayType(TArray type)
        {
            Type = type;
            TypeName = type.Apply(CsDefineTypeFullName.Ins);
            ElementTypeName = type.ElementType.Apply(CsDefineTypeFullName.Ins);
        }
    }

    public class CsListType
    {
        public TType Type { get; set; }
        public string TypeName { get; set; }
        public string ElementTypeName { get; set; }
        public bool IsElementEnumType => Type.ElementType.IsEnum;
        public bool IsElementBeanType => Type.ElementType.IsBean;
        public bool IsConvertable { get; set; }
        public bool IsNullable => Type.IsNullable;

        public CsListType(TType type)
        {
            Type = type;
            TypeName = type.Apply(CsDefineTypeFullName.Ins);
            ElementTypeName = type.ElementType.Apply(CsDefineTypeFullName.Ins);
        }
    }

    public class CsMapType
    {
        public TMap Type { get; set; }
        public string TypeName { get; set; }

        public string KeyTypeName { get; set; }
        public string ValueTypeName { get; set; }

        public bool IsKeyEnumType => Type.KeyType.IsEnum;
        public bool IsKeyBeanType => Type.KeyType.IsBean;

        public bool IsValueEnumType => Type.ValueType.IsEnum;
        public bool IsValueBeanType => Type.ValueType.IsBean;

        public bool IsConvertable { get; set; }
        public bool IsNullable => Type.IsNullable;

        public CsMapType(TMap type)
        {
            Type = type;
            TypeName = type.Apply(CsDefineTypeFullName.Ins);
            KeyTypeName = type.KeyType.Apply(CsDefineTypeFullName.Ins);
            ValueTypeName = type.ValueType.Apply(CsDefineTypeFullName.Ins);
        }
    }

    public class CsUnityJsonCollection
    {
        public string PortDefine { get; set; }

        public List<CsValueType> ValueTypes { get; set; }
        public List<CsEnumType> EnumTypes { get; set; }
        public List<CsBeanType> BeanTypes { get; set; }
        public List<CsArrayType> ArrayTypes { get; set; }
        public List<CsListType> ListTypes { get; set; }

        public List<CsMapType> MapTypes { get; set; }

        public List<CsTextType> TextTypes{ get; set; }

        public List<CsArrayType> ArrayElementTypes { get; set; }
        public List<CsListType> ListElementTypes { get; set; }
        //public List<TSet> SetElementTypes { get; set; }
        public List<CsMapType> MapElementTypes { get; set; }

        public bool HasVector2 { get; set; }
        public bool HasVector3 { get; set; }
        public bool HasVector4 { get; set; }
    }
}
