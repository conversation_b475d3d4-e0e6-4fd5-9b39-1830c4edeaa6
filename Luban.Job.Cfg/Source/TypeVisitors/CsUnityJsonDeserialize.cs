using Luban.Job.Cfg.Datas;
using Luban.Job.Common.Types;
using Luban.Job.Common.TypeVisitors;
using System;
using System.Runtime.Serialization;

namespace Luban.Job.Cfg.TypeVisitors
{
    class CsUnityJsonDeserialize : ITypeFuncVisitor<string, string, int, string>
    {
        public static CsUnityJsonDeserialize Ins { get; } = new();

        private string GetException()
        {
            if (GenContext.Ctx.GenArgs.ExportType == "server")
            {
                return "throw new SerializationException();";
            }
            else
            {
                return "\r\n #if UNITY_EDITOR\r\n throw new SerializationException();\r\n #endif\r\n";
            }
        }
        
        private string GetFormattedException(string indent)
        {
            if (GenContext.Ctx.GenArgs.ExportType == "server")
            {
                return $"{indent}throw new SerializationException();";
            }
            else
            {
                return $@"{indent}#if UNITY_EDITOR
{indent}    throw new SerializationException();
{indent}#endif{indent}";
            }
        }

        private string GetExceptionWithMessage(string msg)
        {
            if (GenContext.Ctx.GenArgs.ExportType == "server")
            {
                return $"throw new SerializationException({msg});";
            }
            else
            {
                return $"\r\n #if UNITY_EDITOR\r\n throw new SerializationException({msg});\r\n #endif\r\n";
            }
        }

        public string Accept(TBool type, string json, string x, int depth)
        {
            return $"if({json}.IsBoolean) {{ {x} = {json}; }} else {{ {GetException()} }}";
        }

        public string Accept(TByte type, string json, string x, int depth)
        {
            return $"if({json}.IsNumber) {{ {x} = {json}; }} else {{ {GetException()} }}";
        }

        public string Accept(TShort type, string json, string x, int depth)
        {
            return $"if({json}.IsNumber) {{ {x} = {json}; }} else {{ {GetException()} }}";
        }

        public string Accept(TFshort type, string json, string x, int depth)
        {
            return $"if( {json} .IsNumber) {{  {x}  =  {json} ; }} else {{ {GetException()} }}";
        }

        public string Accept(TInt type, string json, string x, int depth)
        {
            return $"if( {json} .IsNumber) {{  {x}  =  {json} ; }} else {{ {GetException()} }}";
        }

        public string Accept(TFint type, string json, string x, int depth)
        {
            return $"if( {json} .IsNumber) {{  {x}  =  {json} ; }} else {{ {GetException()} }}";
        }

        public string Accept(TLong type, string json, string x, int depth)
        {
            return $"if( {json} .IsNumber) {{  {x}  =  {json} ; }} else {{ {GetException()} }}";
        }

        public string Accept(TFlong type, string json, string x, int depth)
        {
            return $"if( {json} .IsNumber) {{  {x}  =  {json} ; }} else {{ {GetException()} }}";
        }

        public string Accept(TFloat type, string json, string x, int depth)
        {
            return $"if( {json} .IsNumber) {{  {x}  =  {json} ; }} else {{ {GetException()} }}";
        }

        public string Accept(TDouble type, string json, string x, int depth)
        {
            return $"if( {json} .IsNumber) {{  {x}  =  {json} ; }} else {{ {GetException()} }}";
        }

        public string Accept(TEnum type, string json, string x, int depth)
        {
            return $"if({json}.IsNumber) {{ {x} = ({type.CsUnderingDefineType()}){json}.AsInt; }} else {{ {GetException()} }}";
        }

        public string Accept(TString type, string json, string x, int depth)
        {
            return $"if( {json} .IsString) {{  {x}  =  {json} ; }} else {{ {GetException()} }}";
        }

        public string Accept(TBytes type, string json, string x, int depth)
        {
            throw new NotSupportedException();
        }

        public string Accept(TText type, string json, string x, int depth)
        {
            if (type is TSocText)
            {
                return $"if({json}[\"{DSocText.ID_NAME}\"].IsNumber) {{ {x}{TSocText.L10N_FIELD_SUFFIX} = {json}[\"{DSocText.ID_NAME}\"]; }} else {{ {GetExceptionWithMessage($"\"{x}.{DSocText.ID_NAME} is \" + {json}.Tag + \", not number.\"")} }}";
            }
            else
            {
                return $"if({json}[\"{DText.KEY_NAME}\"].IsString) {{ {x}{TText.L10N_FIELD_SUFFIX} = {json}[\"{DText.KEY_NAME}\"]; }} else {{ {GetExceptionWithMessage($"\"{x}.{DText.KEY_NAME} is \" + {json}.Tag + \", not string.\"")} }}   if({json}[\"{DText.TEXT_NAME}\"].IsString) {{ {x} = {json}[\"{DText.TEXT_NAME}\"]; }} else {{ {GetExceptionWithMessage($"\"{x}.{DText.TEXT_NAME} is \" + {json}.Tag + \", not string.\"")} }}";
            }
        }

        public string Accept(TBean type, string json, string x, int depth)
        {
            return $"if({json}.IsObject) {{ {x} = {type.CsUnderingDefineType()}.Deserialize{type.Bean.Name}({json}); }} else {{ {GetException()} }}";
        }

        public string Accept(TArray type, string json, string x, int depth)
        {
            string _n = $"_n{depth}";
            string __e = $"__e{depth}";
            string __v = $"__v{depth}";
            string __json = $"__json{depth}";
            string __index = $"__index{depth}";
            string tempJsonName = __json;
            string typeStr = $"{type.ElementType.Apply(CsDefineTypeName.Ins)}[{_n}]";
            if (type.Dimension > 1)
            {
                if (type.FinalElementType == null)
                {
                    throw new System.Exception("多维数组没有元素类型");
                }
                typeStr = $"{type.FinalElementType.Apply(CsUnderingDefineTypeName.Ins)}[{_n}]";
                for (int i = 0; i < type.Dimension - 1; i++)
                {
                    typeStr += "[]";
                }
            }
            return $"{{ var {tempJsonName} = {json}; if({tempJsonName}.IsArray) {{ int {_n} = {tempJsonName}.Count; {x} = new {typeStr}; int {__index}=0; foreach(JSONNode {__e} in {tempJsonName}.Children) {{ {type.ElementType.CsUnderingDefineType()} {__v}= default;  {type.ElementType.Apply(this, __e, __v, depth + 1)}  {x}[{__index}++] = {__v}; }} }} else {{ {GetException()} }} }}";
        }

        public string Accept(TList type, string json, string x, int depth)
        {
            string __e = $"__e{depth}";
            string __v = $"__v{depth}";
            string __json = $"__json{depth}";
            string tempJsonName = __json;
            return $"{{ var {tempJsonName} = {json}; if({tempJsonName}.IsArray) {{ {x} = new {type.CsUnderingDefineType()}({tempJsonName}.Count); foreach(JSONNode {__e} in {tempJsonName}.Children) {{ {type.ElementType.CsUnderingDefineType()} {__v}= default;  {type.ElementType.Apply(this, __e, __v, depth + 1)}  {x}.Add({__v}); }} }} else {{ {GetException()} }} }}";
        }

        public string Accept(TSet type, string json, string x, int depth)
        {
            string __e = $"__e{depth}";
            string __v = $"__v{depth}";
            string __json = $"__json{depth}";
            string tempJsonName = __json;
            return $"{{ var {tempJsonName} = {json}; if({tempJsonName}.IsArray) {{ {x} = new {type.CsUnderingDefineType()}(/*{tempJsonName}.Count*/); foreach(JSONNode {__e} in {tempJsonName}.Children) {{ {type.ElementType.CsUnderingDefineType()} {__v}= default;  {type.ElementType.Apply(this, __e, __v, depth + 1)}  {x}.Add({__v}); }} }} else {{ {GetException()} }} }}";
        }

        public string Accept(TMap type, string json, string x, int depth)
        {
            string __e = $"__e{depth}";
            string __k = $"_k{depth}";
            string __v = $"_v{depth}";
            string __json = $"__json{depth}";
            string tempJsonName = __json;
            return @$"{{ var {tempJsonName} = {json}; if({tempJsonName}.IsArray) {{ {x} = new {type.CsUnderingDefineType()}({tempJsonName}.Count); foreach(JSONNode {__e} in {tempJsonName}.Children) {{ {type.KeyType.CsUnderingDefineType()} {__k} = default;  {type.KeyType.Apply(this, $"{__e}[0]", __k, depth + 1)} {type.ValueType.CsUnderingDefineType()} { __v}=default;  {type.ValueType.Apply(this, $"{__e}[1]", __v, depth + 1)}  {x}.Add({__k}, { __v}); }} }} else {{
                    {GetFormattedException("                    ")}
                    }} }}";
        }

        public string Accept(TVector2 type, string json, string x, int depth)
        {
            string tempJsonName = $"_json2";
            return $"{{ var {tempJsonName} = {json}; if({tempJsonName}.IsObject) {{ float __x=0.0f; {TFloat.Ins.Apply(this, $"{tempJsonName}[\"x\"]", "__x", depth) } float __y=0.0f; {TFloat.Ins.Apply(this, $"{tempJsonName}[\"y\"]", "__y", depth) } {x} = new {type.Apply(CsDefineTypeName.Ins)}(__x, __y); }} else {{ {GetException()} }} }}";
        }

        public string Accept(TVector3 type, string json, string x, int depth)
        {
            string tempJsonName = $"_json2";
            return $"{{ var {tempJsonName} = {json}; if({tempJsonName}.IsObject) {{ float __x=0.0f; {TFloat.Ins.Apply(this, $"{tempJsonName}[\"x\"]", "__x", depth) } float __y=0.0f; {TFloat.Ins.Apply(this, $"{tempJsonName}[\"y\"]", "__y", depth) } float __z=0.0f; {TFloat.Ins.Apply(this, $"{tempJsonName}[\"z\"]", "__z", depth) }  {x} = new {type.Apply(CsDefineTypeName.Ins)}(__x, __y,__z); }} else {{ {GetException()} }} }}";
        }

        public string Accept(TVector4 type, string json, string x, int depth)
        {
            string tempJsonName = $"_json2";
            return $"{{ var {tempJsonName} = {json}; if({tempJsonName}.IsObject) {{ float __x=0.0f; {TFloat.Ins.Apply(this, $"{tempJsonName}[\"x\"]", "__x", depth) } float __y=0.0f; {TFloat.Ins.Apply(this, $"{tempJsonName}[\"y\"]", "__y", depth) } float __z=0.0f; {TFloat.Ins.Apply(this, $"{tempJsonName}[\"z\"]", "__z", depth) }  float __w=0.0f; {TFloat.Ins.Apply(this, $"{tempJsonName}[\"w\"]", "__w", depth) } {x} = new {type.Apply(CsDefineTypeName.Ins)}(__x, __y, __z, __w); }} else {{ {GetException()} }} }}";
        }

        public string Accept(TDateTime type, string json, string x, int depth)
        {
            return $"if({json}.IsNumber) {{ {x} = {json}; }} else {{ {GetException()} }}";
        }
    }
}
