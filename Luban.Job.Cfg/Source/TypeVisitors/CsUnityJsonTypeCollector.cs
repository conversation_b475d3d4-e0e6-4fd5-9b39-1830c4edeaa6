// [ALB]
using Luban.Job.Cfg.Datas;
using Luban.Job.Common;
using Luban.Job.Common.Defs;
using Luban.Job.Common.RawDefs;
using Luban.Job.Common.Source.Types;
using Luban.Job.Common.Types;
using Luban.Job.Common.TypeVisitors;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

namespace Luban.Job.Cfg.TypeVisitors
{
    /// <summary>
    /// 收集所有需要自动生成代码的数据结构
    /// </summary>
    class CsUnityJsonCollector : ITypeActionVisitor<int>
    {
        public SortedDictionary<string, CsValueType> ValueTypes = new SortedDictionary<string, CsValueType>();
        public SortedDictionary<string, CsEnumType> EnumTypes = new SortedDictionary<string, CsEnumType>();
        public SortedDictionary<string, CsBeanType> BeanTypes = new SortedDictionary<string, CsBeanType>();
        public SortedDictionary<string, CsArrayType> ArrayTypes = new SortedDictionary<string, CsArrayType>();
        public SortedDictionary<string, CsListType> ListTypes = new SortedDictionary<string, CsListType>();
        public SortedDictionary<string, CsListType> SetTypes = new SortedDictionary<string, CsListType>();
        public SortedDictionary<string, CsMapType> MapTypes = new SortedDictionary<string, CsMapType>();

        public SortedDictionary<string, CsArrayType> ArrayElementTypes = new SortedDictionary<string, CsArrayType>();
        public SortedDictionary<string, CsListType> ListElementTypes = new SortedDictionary<string, CsListType>();
        public SortedDictionary<string, CsListType> SetElementTypes = new SortedDictionary<string, CsListType>();
        public SortedDictionary<string, CsMapType> MapElementTypes = new SortedDictionary<string, CsMapType>();
        public List<CsTextType> TextTypes = new List<CsTextType>(2);

        public bool HasVector2 = false;
        public bool HasVector3 = false;
        public bool HasVector4 = false;

        public CsUnityJsonCollection AsValueTypeCollection()
        {
            CsUnityJsonCollection collection = new CsUnityJsonCollection();
            // 1、自动生成JSONNode.TryGetValue(string, T)
            collection.ValueTypes = ValueTypes.Values.ToList();
            // enum无需单独处理，使用int获取值后，强制转换类型即可
            collection.EnumTypes = EnumTypes.Values.ToList();
            collection.BeanTypes = BeanTypes.Values.ToList();
            collection.ArrayTypes = ArrayTypes.Values.ToList();
            collection.ListTypes = new List<CsListType>(ListTypes.Values);
            collection.ListTypes.AddRange(SetTypes.Values); // List和Set的模板相同
            collection.MapTypes = MapTypes.Values.ToList();
            collection.TextTypes = TextTypes;

            // 2、自动生成Array\List\Set\Map的隐式转换函数
            collection.ArrayElementTypes = ArrayElementTypes.Values.ToList();
            // 不需要string[]的隐式转换函数，因为已经在SimpleJSONDotNetTypes.cs存在
            collection.ArrayElementTypes.RemoveAll(x => x.Type.ElementType is TString);
            collection.ListElementTypes = ListElementTypes.Values.ToList();
            // 不需要List<string>的隐式转换函数，因为已经在SimpleJSONDotNetTypes.cs存在
            collection.ListElementTypes.RemoveAll(x => x.Type.ElementType is TString);
            collection.ListElementTypes.AddRange(SetElementTypes.Values);
            //collection.SetElementTypes = SetElementTypes.Values.ToList();
            collection.MapElementTypes = MapElementTypes.Values.ToList();

            // 3、因为已经在SimpleJSONDotNetTypes.cs存在，所以支持string[]和List<string>的隐式转换函数
            TString stringType = TString.Create(false, null);
            TArray stringArrayType = TArray.Create(false, null, stringType);
            TList stringListType = TList.Create(false, null, stringType, false);
            CsArrayType csArray = new CsArrayType(stringArrayType);
            CsListType csList = new CsListType(stringListType);
            ArrayElementTypes.TryAdd(csArray.TypeName, csArray);
            ListElementTypes.TryAdd(csList.TypeName, csList);

            // 4、是否支持JSONNode到本类型的隐式转换函数
            foreach (var csArrayType in collection.ArrayTypes)
            {
                csArrayType.IsConvertable = IsImplicitConvertable(csArrayType.TypeName);
            }
            foreach (var csListType in collection.ListTypes)
            {
                csListType.IsConvertable = IsImplicitConvertable(csListType.TypeName);
            }
            foreach (var csMapType in collection.MapTypes)
            {
                csMapType.IsConvertable = IsImplicitConvertable(csMapType.TypeName);
            }

            // 5、是否自动生成Vector2\Vector3\Vector4的隐式转换函数
            collection.HasVector2 = HasVector2;
            collection.HasVector3 = HasVector3;
            collection.HasVector4 = HasVector4;
            return collection;
        }

        public bool IsImplicitConvertable(string typeName)
        {
            return ArrayElementTypes.ContainsKey(typeName)
                || ListElementTypes.ContainsKey(typeName)
                || SetElementTypes.ContainsKey(typeName)
                || MapElementTypes.ContainsKey(typeName);
        }

        public void Accept(TBool type, int depth)
        {
            CsValueType csType = new CsValueType(type, false);
            csType.IsObject = false;
            ValueTypes.TryAdd(csType.TypeName, csType);
        }

        public void Accept(TByte type, int depth)
        {
            CsValueType csType = new CsValueType(type, true);
            ValueTypes.TryAdd(csType.TypeName, csType);
        }

        public void Accept(TShort type, int depth)
        {
            CsValueType csType = new CsValueType(type, true);
            ValueTypes.TryAdd(csType.TypeName, csType);
        }

        public void Accept(TFshort type, int depth)
        {
            CsValueType csType = new CsValueType(type, true);
            ValueTypes.TryAdd(csType.TypeName, csType);
        }

        public void Accept(TInt type, int depth)
        {
            CsValueType csType = new CsValueType(type, true);
            ValueTypes.TryAdd(csType.TypeName, csType);
        }

        public void Accept(TFint type, int depth)
        {
            CsValueType csType = new CsValueType(type, true);
            ValueTypes.TryAdd(csType.TypeName, csType);
        }

        public void Accept(TLong type, int depth)
        {
            CsValueType csType = new CsValueType(type, true);
            ValueTypes.TryAdd(csType.TypeName, csType);
        }

        public void Accept(TFlong type, int depth)
        {
            CsValueType csType = new CsValueType(type, true);
            ValueTypes.TryAdd(csType.TypeName, csType);
        }

        public void Accept(TFloat type, int depth)
        {
            CsValueType csType = new CsValueType(type, true);
            ValueTypes.TryAdd(csType.TypeName, csType);
        }

        public void Accept(TDouble type, int depth)
        {
            CsValueType csType = new CsValueType(type, true);
            ValueTypes.TryAdd(csType.TypeName, csType);
        }

        public void Accept(TEnum type, int depth)
        {
            CsEnumType csType = new CsEnumType(type);
            EnumTypes.TryAdd(csType.TypeName, csType);
        }

        public void Accept(TString type, int depth)
        {
            CsValueType csType = new CsValueType(type, false);
            csType.IsObject = false;
            ValueTypes.TryAdd(csType.TypeName, csType);
        }

        public void Accept(TBytes type, int depth)
        {
            throw new NotSupportedException();
        }

        public void Accept(TText type, int depth)
        {
            if (TextTypes.Count == 2)
                return;

            string typeName = "string";
            string keyName = DText.KEY_NAME;
            if (type is TSocText)
            {
                typeName = "int";
                keyName = DSocText.ID_NAME;
            }

            if (TextTypes.Count == 0 || TextTypes[0].KeyName != keyName)
            {
                var textType = new CsTextType();
                textType.Type = type;
                textType.TypeName = typeName;
                textType.KeyName = keyName;
                TextTypes.Add(textType);
            }
        }

        public void Accept(TBean type, int depth)
        {
            if (depth == 0)
            {
                CsBeanType csType = new CsBeanType(type);
                BeanTypes.TryAdd(csType.TypeName, csType);
            }

            //++depth; // depth只用于TArray\TList\TSet\TMap之类的容器深度，TBean无需增加深度
            var fields = type.Bean.Fields;
            for (int i = 0; i < fields.Count; ++i)
            {
                fields[i].CType.Apply(this, depth);
            }
        }

        public void Accept(TArray type, int depth)
        {
            if (type.Dimension > 1)
            {
                if (type.FinalElementType == null)
                {
                    throw new System.Exception("多维数组没有元素类型");
                }
            }

            CsArrayType csType = new CsArrayType(type);
            if (depth == 0)
                ArrayTypes.TryAdd(csType.TypeName, csType);
            else
                ArrayElementTypes.TryAdd(csType.TypeName, csType);

            type.ElementType.Apply(this, ++depth);
        }

        public void Accept(TList type, int depth)
        {
            CsListType csType = new CsListType(type);
            if (depth == 0)
                ListTypes.TryAdd(csType.TypeName, csType);
            else
                ListElementTypes.TryAdd(csType.TypeName, csType);

            type.ElementType.Apply(this, ++depth);
        }

        public void Accept(TSet type, int depth)
        {
            CsListType csType = new CsListType(type);
            if (depth == 0)
                SetTypes.TryAdd(csType.TypeName, csType);
            else
                SetElementTypes.TryAdd(csType.TypeName, csType);

            type.ElementType.Apply(this, ++depth);
        }

        public void Accept(TMap type, int depth)
        {
            CsMapType csType = new CsMapType(type);
            if (depth == 0)
                MapTypes.TryAdd(csType.TypeName, csType);
            else
                MapElementTypes.TryAdd(csType.TypeName, csType);

            ++depth;
            type.KeyType.Apply(this, depth);
            type.ValueType.Apply(this, depth);
        }

        public void Accept(TVector2 type, int depth)
        {
            CsValueType csType = new CsValueType(type, false);
            ValueTypes.TryAdd(csType.TypeName, csType);
            HasVector2 = true;
        }

        public void Accept(TVector3 type, int depth)
        {
            CsValueType csType = new CsValueType(type, false);
            ValueTypes.TryAdd(csType.TypeName, csType);
            HasVector3 = true;
        }

        public void Accept(TVector4 type, int depth)
        {
            CsValueType csType = new CsValueType(type, false);
            ValueTypes.TryAdd(csType.TypeName, csType);
            HasVector4 = true;
        }

        public void Accept(TDateTime type, int depth)
        {
            CsValueType csType = new CsValueType(type, true);
            ValueTypes.TryAdd(csType.TypeName, csType);
        }
    }
}
