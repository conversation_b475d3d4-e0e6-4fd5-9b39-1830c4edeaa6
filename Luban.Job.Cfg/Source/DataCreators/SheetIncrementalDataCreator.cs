using Luban.Job.Cfg.DataCreators;
using Luban.Job.Cfg.Datas;
using Luban.Job.Cfg.DataSources.Excel;
using Luban.Job.Cfg.Defs;
using Luban.Job.Cfg.Utils;
using Luban.Job.Common.Source.Types;
using Luban.Job.Common.Types;
using Luban.Job.Common.TypeVisitors;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Luban.Job.Cfg.Source.DataCreators
{
    class SheetIncrementalDataCreator : ITypeFuncVisitor<Sheet, TitleRow, DType>
    {
        public static SheetIncrementalDataCreator Ins { get; } = new();
        [ThreadStatic]
        public static string CurrentPlayModuleName = "invalid";

        private bool CheckNull(bool nullable, object o)
        {
            return nullable && o is string s && s == "null";
        }

        private bool CheckNotChange(object o)
        {
            return o == null;
        }

        private void ThrowIfNonEmpty(TitleRow row)
        {
            if (row.SelfTitle.NonEmpty)
            {
                throw new Exception($"字段不允许为空");
            }
        }

        public DType Accept(TBool type, Sheet sheet, TitleRow row)
        {
            object x = row.Current;
            if (CheckNull(type.IsNullable, x))
            {
                return DNull.Default;
            }
            if (CheckNotChange(x))
            {
                return null;
            }
            if (x is bool v)
            {
                return DBool.ValueOf(v);
            }
            return DBool.ValueOf(bool.Parse(x.ToString()));
        }

        public DType Accept(TByte type, Sheet sheet, TitleRow row)
        {
            object x = row.Current;
            if (CheckNull(type.IsNullable, x))
            {
                return DNull.Default;
            }
            if (CheckNotChange(x))
            {
                return null;
            }
            return DByte.ValueOf(byte.Parse(x.ToString()));
        }

        public DType Accept(TShort type, Sheet sheet, TitleRow row)
        {
            object x = row.Current;
            if (CheckNull(type.IsNullable, x))
            {
                return DNull.Default;
            }
            if (CheckNotChange(x))
            {
                return null;
            }
            return DShort.ValueOf(short.Parse(x.ToString()));
        }

        public DType Accept(TFshort type, Sheet sheet, TitleRow row)
        {
            object x = row.Current;
            if (CheckNull(type.IsNullable, x))
            {
                return DNull.Default;
            }
            if (CheckNotChange(x))
            {
                return null;
            }
            return DFshort.ValueOf(short.Parse(x.ToString()));
        }

        public DType Accept(TInt type, Sheet sheet, TitleRow row)
        {
            object x = row.Current;
            if (CheckNull(type.IsNullable, x))
            {
                return DNull.Default;
            }
            if (CheckNotChange(x))
            {
                return null;
            }
            return DInt.ValueOf(int.Parse(x.ToString()));
        }

        public DType Accept(TFint type, Sheet sheet, TitleRow row)
        {
            object x = row.Current;
            if (CheckNull(type.IsNullable, x))
            {
                return DNull.Default;
            }
            if (CheckNotChange(x))
            {
                return null;
            }
            return DFint.ValueOf(int.Parse(x.ToString()));
        }

        public DType Accept(TLong type, Sheet sheet, TitleRow row)
        {
            object x = row.Current;
            if (CheckNull(type.IsNullable, x))
            {
                return DNull.Default;
            }
            if (CheckNotChange(x))
            {
                return null;
            }
            return DLong.ValueOf(long.Parse(x.ToString()));
        }

        public DType Accept(TFlong type, Sheet sheet, TitleRow row)
        {
            object x = row.Current;
            if (CheckNull(type.IsNullable, x))
            {
                return DNull.Default;
            }
            if (CheckNotChange(x))
            {
                return null;
            }
            return DFlong.ValueOf(long.Parse(x.ToString()));
        }

        public DType Accept(TFloat type, Sheet sheet, TitleRow row)
        {
            object x = row.Current;
            if (CheckNull(type.IsNullable, x))
            {
                return DNull.Default;
            }
            if (CheckNotChange(x))
            {
                return null;
            }
            return DFloat.ValueOf(float.Parse(x.ToString()));
        }

        public DType Accept(TDouble type, Sheet sheet, TitleRow row)
        {
            object x = row.Current;
            if (CheckNull(type.IsNullable, x))
            {
                return DNull.Default;
            }
            if (CheckNotChange(x))
            {
                return null;
            }
            return DDouble.ValueOf(double.Parse(x.ToString()));
        }

        public DType Accept(TEnum type, Sheet sheet, TitleRow row)
        {
            object x = row.Current;
            if (CheckNull(type.IsNullable, x))
            {
                return DNull.Default;
            }
            if (CheckNotChange(x))
            {
                return null;
            }
            return new DEnum(type, x.ToString());
        }


        private static string ParseString(object d)
        {
            if (d == null)
            {
                return null;
            }
            else if (d is string s)
            {
                return DataUtil.UnEscapeRawString(s);
            }
            else
            {
                return d.ToString();
            }
        }

        public DType Accept(TString type, Sheet sheet, TitleRow row)
        {
            if (type is TSocResCheck resCheck)
            {
                object x = row.Current;
                if (CheckNull(type.IsNullable, x))
                {
                    return DNull.Default;
                }
                if (CheckNotChange(x))
                {
                    return null;
                }
                var s = ParseString(x);
                if (s == null)
                {
                    if (type.IsNullable)
                    {
                        return null;
                    }
                    else
                    {
                        throw new InvalidExcelDataException("字段不是nullable类型，不能为null");
                    }
                }

                string key = $"{sheet.RawUrl}|{row.SelfTitle.Name}|{row.Row[1].Value}";
                return new DSocString(key, sheet.RawUrl.ToString(), row.SelfTitle.Name, row.Row[1].Value.ToString(), s);
            }
            else
            {
                object x = row.Current;
                if (CheckNull(type.IsNullable, x))
                {
                    return DNull.Default;
                }
                if (CheckNotChange(x))
                {
                    return null;
                }
                var s = ParseString(x);
                if (s == null)
                {
                    if (type.IsNullable)
                    {
                        return null;
                    }
                    else
                    {
                        throw new InvalidExcelDataException("字段不是nullable类型，不能为null");
                    }
                }
                return DString.ValueOf(s);
            }
        }

        public DType Accept(TBytes type, Sheet sheet, TitleRow row)
        {
            throw new NotSupportedException();
        }

        public DType Accept(TText type, Sheet sheet, TitleRow row)
        {
            if (type is TSocText socType)
            {
                object x = row.Current;
                if (CheckNull(type.IsNullable, x))
                {
                    return DNull.Default;
                }
                if (CheckNotChange(x))
                {
                    return null;
                }
                string s = ParseString(x);
                if (s == null)
                {
                    if (type.IsNullable)
                    {
                        return null;
                    }
                    else
                    {
                        throw new InvalidExcelDataException("字段不是nullable类型，不能为null");
                    }
                }
                string key = $"{sheet.RawUrl}|{row.SelfTitle.Name}|{row.Row[1].Value}|{CurrentPlayModuleName}";
                return new DSocText(key, s, socType.EngLength);
            }

            if (string.IsNullOrEmpty(row.SelfTitle.SepOr(type.GetTag("sep"))))
            {
                if (row.CellCount != 2)
                {
                    throw new Exception($"text 要求两个字段");
                }
                int startIndex = row.SelfTitle.FromIndex;
                if (row.Row[startIndex].Value == null && row.Row[startIndex + 1].Value == null)
                {
                    return null;
                }
                var key = ParseString(row.Row[startIndex].Value);
                var text = ParseString(row.Row[startIndex + 1].Value);
                if (type.IsNullable && key == null && text == null)
                {
                    return null;
                }
                DataUtil.ValidateText(key, text);
                return new DText(key, text);
            }
            else
            {
                var s = row.AsStream(row.SelfTitle.Sep, sheet.RawUrl);
                return type.Apply(ExcelStreamDataCreator.Ins, s);
            }
        }

        public DType Accept(TDateTime type, Sheet sheet, TitleRow row)
        {
            var d = row.Current;
            if (CheckNull(type.IsNullable, d))
            {
                return DNull.Default;
            }
            if (CheckNotChange(d))
            {
                return null;
            }
            if (d is System.DateTime datetime)
            {
                return new DDateTime(datetime);
            }
            return DataUtil.CreateDateTime(d.ToString());
        }

        public DType Accept(TVector2 type, Sheet sheet, TitleRow row)
        {
            var d = row.Current;
            if (CheckNull(type.IsNullable, d))
            {
                return DNull.Default;
            }
            if (CheckNotChange(d))
            {
                return null;
            }
            if (CheckNotChange(d))
            {
                ThrowIfNonEmpty(row);
                return DVector2.Default;
            }
            return DataUtil.CreateVector(type, d.ToString());
        }

        public DType Accept(TVector3 type, Sheet sheet, TitleRow row)
        {
            var d = row.Current;
            if (CheckNull(type.IsNullable, d))
            {
                return DNull.Default;
            }
            if (CheckNotChange(d))
            {
                return null;
            }
            if (CheckNotChange(d))
            {
                ThrowIfNonEmpty(row);
                return DVector3.Default;
            }
            return DataUtil.CreateVector(type, d.ToString());
        }

        public DType Accept(TVector4 type, Sheet sheet, TitleRow row)
        {
            var d = row.Current;
            if (CheckNull(type.IsNullable, d))
            {
                return DNull.Default;
            }
            if (CheckNotChange(d))
            {
                return null;
            }
            if (CheckNotChange(d))
            {
                ThrowIfNonEmpty(row);
                return DVector4.Default;
            }
            return DataUtil.CreateVector(type, d.ToString());
        }

        private List<DType> CreateBeanFields(DefBean bean, Sheet sheet, TitleRow row)
        {
            var list = new List<DType>();
            foreach (DefField f in bean.HierarchyFields)
            {
                string fname = f.Name;
                TitleRow field = row.GetSubTitleNamedRow(fname);
                if (field == null)
                {
                    list.Add(null);
                    continue;
                }
                try
                {
                    list.Add(f.CType.Apply(this, sheet, field));
                }
                catch (DataCreateException dce)
                {
                    dce.Push(bean, f);
                    throw;
                }
                catch (Exception e)
                {
                    var dce = new DataCreateException(e, $"字段：{fname} 位置:{field.Location}");
                    dce.Push(bean, f);
                    throw dce;
                }
            }
            return list;
        }

        public DType Accept(TBean type, Sheet sheet, TitleRow row)
        {
            string sep = row.SelfTitle.Sep;// type.GetBeanAs<DefBean>().Sep;

            if (row.Row != null)
            {
                var s = row.AsStream(sep, sheet.RawUrl);
                if (type.IsNullable && s.TryReadEOF())
                {
                    return null;
                }
                return type.Apply(ExcelStreamDataCreator.Ins, s);
            }
            else if (row.Rows != null)
            {
                var s = row.AsMultiRowConcatStream(sep);
                if (type.IsNullable && s.TryReadEOF())
                {
                    return null;
                }
                return type.Apply(ExcelStreamDataCreator.Ins, s);
            }
            else if (row.Fields != null)
            {
                var originBean = (DefBean)type.Bean;
                if (originBean.IsAbstractType)
                {
                    TitleRow typeTitle = row.GetSubTitleNamedRow(DefBean.EXCEL_TYPE_NAME_KEY) ?? row.GetSubTitleNamedRow(DefBean.FALLBACK_TYPE_NAME_KEY);
                    if (typeTitle == null)
                    {
                        throw new Exception($"type:'{originBean.FullName}' 是多态类型,需要定义'{DefBean.EXCEL_TYPE_NAME_KEY}'列来指定具体子类型");
                    }

                    string subType = typeTitle.Current?.ToString()?.Trim();
                    if (subType == null || subType == DefBean.BEAN_NULL_STR)
                    {
                        if (!type.IsNullable)
                        {
                            throw new Exception($"type:'{originBean.FullName}' 不是可空类型 '{type.Bean.FullName}?' , 不能为空");
                        }
                        return null;
                    }
                    DefBean implType = DataUtil.GetImplTypeByNameOrAlias(originBean, subType);
                    return new DBean(type, implType, CreateBeanFields(implType, sheet, row));
                }
                else
                {
                    if (type.IsNullable)
                    {
                        TitleRow typeTitle = row.GetSubTitleNamedRow(DefBean.EXCEL_TYPE_NAME_KEY) ?? row.GetSubTitleNamedRow(DefBean.FALLBACK_TYPE_NAME_KEY);
                        if (typeTitle == null)
                        {
                            throw new Exception($"type:'{originBean.FullName}' 是可空类型,需要定义'{DefBean.EXCEL_TYPE_NAME_KEY}'列来指明是否可空");
                        }
                        string subType = typeTitle.Current?.ToString()?.Trim();
                        if (subType == null || subType == DefBean.BEAN_NULL_STR)
                        {
                            return null;
                        }
                        else if (subType != DefBean.BEAN_NOT_NULL_STR && subType != originBean.Name)
                        {
                            throw new Exception($"type:'{originBean.FullName}' 可空标识:'{subType}' 不合法（只能为'{DefBean.BEAN_NULL_STR}'或'{DefBean.BEAN_NOT_NULL_STR}'或'{originBean.Name}')");
                        }
                    }

                    return new DBean(type, originBean, CreateBeanFields(originBean, sheet, row));
                }
            }
            else if (row.Elements != null)
            {
                var s = row.AsMultiRowConcatElements(sep);
                return type.Apply(ExcelStreamDataCreator.Ins, s);
            }
            else
            {
                throw new Exception();
            }
        }

        public List<DType> ReadList(TType type, ExcelStream stream)
        {
            var sep = type.GetTag("sep");
            var datas = new List<DType>();
            while (!stream.TryReadEOF())
            {
                if (string.IsNullOrEmpty(sep))
                {
                    datas.Add(type.Apply(ExcelStreamDataCreator.Ins, stream));
                }
                else
                {
                    datas.Add(type.Apply(ExcelStreamDataCreator.Ins, new ExcelStream(stream.ReadCell(), sep)));
                }
            }
            return datas;
        }

        private static List<DType> ReadList(TType type, IEnumerable<ExcelStream> streams)
        {
            var datas = new List<DType>();
            foreach (var stream in streams)
            {
                while (!stream.TryReadEOF())
                {
                    datas.Add(type.Apply(ExcelStreamDataCreator.Ins, stream));
                }
            }
            return datas;
        }

        private List<DType> ReadCollectionDatas(TType type, TType elementType, Sheet sheet, TitleRow row)
        {
            if (row.Row != null)
            {
                var s = row.AsStream(row.SelfTitle.Sep, sheet.RawUrl);
                return ExcelStreamDataCreator.Ins.ReadList(type, elementType, s);
            }
            else if (row.Rows != null)
            {
                var s = row.AsMultiRowStream(row.SelfTitle.Sep);
                return ReadList(elementType, s);
            }
            else if (row.Fields != null)
            {
                //throw new Exception($"array 不支持 子字段. 忘记将字段设为多行模式?  {row.SelfTitle.Name} => *{row.SelfTitle.Name}");

                var datas = new List<DType>(row.Fields.Count);
                var sortedFields = row.Fields.Values.ToList();
                sortedFields.Sort((a, b) => a.SelfTitle.FromIndex - b.SelfTitle.FromIndex);
                foreach (var field in sortedFields)
                {
                    if (field.IsBlank)
                    {
                        continue;
                    }
                    datas.Add(elementType.Apply(this, sheet, field));
                }
                return datas;
            }
            else if (row.Elements != null)
            {
                return row.Elements.Select(e => elementType.Apply(this, sheet, e)).ToList();
            }
            else
            {
                throw new Exception();
            }
        }

        public DType Accept(TArray type, Sheet sheet, TitleRow row)
        {
            //string sep = DataUtil.GetSep(type);
            return new DArray(type, ReadCollectionDatas(type, type.ElementType, sheet, row));
        }

        public DType Accept(TList type, Sheet sheet, TitleRow row)
        {
            return new DList(type, ReadCollectionDatas(type, type.ElementType, sheet, row));
        }

        public DType Accept(TSet type, Sheet sheet, TitleRow row)
        {
            return new DSet(type, ReadCollectionDatas(type, type.ElementType, sheet, row));
        }

        public DType Accept(TMap type, Sheet sheet, TitleRow row)
        {
            string sep = row.SelfTitle.Sep;

            if (row.Row != null)
            {
                var s = row.AsStream(sep, sheet.RawUrl);
                return type.Apply(ExcelStreamDataCreator.Ins, s);
            }
            else if (row.Rows != null)
            {
                var datas = new Dictionary<DType, DType>();
                foreach (ExcelStream s in row.AsMultiRowStream(sep))
                {
                    while (!s.TryReadEOF())
                    {
                        var key = type.KeyType.Apply(ExcelStreamDataCreator.Ins, s);
                        var value = type.ValueType.Apply(ExcelStreamDataCreator.Ins, s);
                        datas.Add(key, value);
                    }
                }
                return new DMap(type, datas);
            }
            else if (row.Fields != null)
            {
                var datas = new Dictionary<DType, DType>();
                foreach (var e in row.Fields)
                {
                    var keyData = type.KeyType.Apply(StringDataCreator.Ins, e.Key);
                    if (Sheet.IsBlankRow(e.Value.Row, e.Value.SelfTitle.FromIndex, e.Value.SelfTitle.ToIndex))
                    {
                        continue;
                    }
                    var valueData = type.ValueType.Apply(ExcelStreamDataCreator.Ins, e.Value.AsStream("", sheet.RawUrl));
                    datas.Add(keyData, valueData);
                }
                return new DMap(type, datas);
            }
            else if (row.Elements != null)
            {
                var datas = new Dictionary<DType, DType>();
                foreach (var e in row.Elements)
                {
                    var stream = e.AsStream(sep, sheet.RawUrl);
                    var keyData = type.KeyType.Apply(ExcelStreamDataCreator.Ins, stream);
                    var valueData = type.ValueType.Apply(ExcelStreamDataCreator.Ins, stream);
                    datas.Add(keyData, valueData);
                }
                return new DMap(type, datas);
            }
            else
            {
                throw new Exception();
            }
        }
    }
}
