using Luban.Job.Cfg.DataCreators;
using Luban.Job.Cfg.Datas;
using Luban.Job.Cfg.DataSources;
using Luban.Job.Cfg.Defs;
using Luban.Job.Cfg.RawDefs;
using Luban.Job.Cfg.Tags;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;

namespace Luban.Job.Cfg.Validators
{
    public class KeyValidator
    {
        public static bool IsValidate(DefTable table)
        {
            if (table.Mode == ETableMode.ONE || table.IndexField == null)
                return false;

            var tags = table.IndexField.Tags;
            return tags.ContainsKey(TagDefines.KEY) // 是否检测键值唯一
                       || tags.ContainsKey(TagDefines.NO_DELETE); // 是否检测键值所在行被删除
        }

        public void Validate(DefTable table, GenArgs args)
        {
            var indexTags = table.IndexField.Tags;
            // 是否检测键值唯一
            bool isValidateKeyUnique = indexTags.ContainsKey(TagDefines.KEY);
            // 是否检测键值所在行被删除
            bool isValidateNoDelete = indexTags.ContainsKey(TagDefines.NO_DELETE);

            // 查找当前表的键值到数据行的映射表，查找过程中，会检测键值唯一性
            FindKeyMapVisitor findVisitor = new FindKeyMapVisitor();
            if (isValidateKeyUnique || isValidateNoDelete)
            {
                if (!findVisitor.Find(table))
                    return;
            }

            // 检测键值所在行被删除
            if (isValidateNoDelete)
                ValidateNoDelete(table, args, findVisitor.Result.KeyMap, findVisitor.KeyFields);
        }

        public void ValidateNoDelete(DefTable defTable, GenArgs args, Dictionary<DType, DKeyMap> keyMap, List<TableGridField> keyFields)
        {
            var originFile = System.IO.Path.Combine(args.OutputDataDir, defTable.OutputDataFile + ".json");
            if (!System.IO.File.Exists(originFile))
                return;

            using FileStream fs = System.IO.File.OpenRead(originFile);
            JsonDocument document = JsonDocument.Parse(fs);
            JsonElement root = document.RootElement;
            if (!root.TryGetProperty("data", out JsonElement data))
                throw new Exception($"{string.Join('/', defTable.InputFiles)} {defTable.Name} Json文件'{originFile}'中，没有'data'数据段");

            Stack<DType> paths = new Stack<DType>(4);
            foreach (JsonElement element in data.EnumerateArray())
            {
                ValidateNoDeleteKey(defTable, keyMap, keyFields, paths, element);
            }
        }

        public void ValidateNoDeleteKey(DefTable table, Dictionary<DType, DKeyMap> keyMap, List<TableGridField> keyFields, Stack<DType> paths, JsonElement element)
        {
            var level = paths.Count;
            var field = keyFields[level].Field;
            var keyElement = element.GetProperty(field.Name);
            var key = field.CType.Apply(JsonDataCreator.Ins, keyElement, table.Assembly);
            paths.Push(key);
            if (!keyMap.TryGetValue(key, out var km))
            {
                throw new Exception($"{FindKeyMapVisitor.GetKeyPaths(table, paths, keyFields)} 上次转表时存在，因为'no_delete'不允许删除");
            }

            level = paths.Count;
            if (km.KeyMap != null && keyFields.Count > level)
            {
                if (element.TryGetProperty(keyFields[level].HostField.Name, out var hostElement))
                {
                    foreach (JsonElement childElement in hostElement.EnumerateArray())
                    {
                        ValidateNoDeleteKey(table, km.KeyMap, keyFields, paths, childElement);
                    }
                }
            }

            paths.Pop();
        }

    }

}
