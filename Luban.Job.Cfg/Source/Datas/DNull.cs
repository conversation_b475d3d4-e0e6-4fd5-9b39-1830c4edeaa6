using Luban.Job.Cfg.DataVisitors;
using System;

namespace Luban.Job.Cfg.Datas
{
    public class DNull : DType
    {
        public static DNull Default = new DNull();

        public override string TypeName => "null";

        public override void Apply<T>(IDataActionVisitor<T> visitor, T x)
        {
            throw new NotImplementedException();
        }

        public override void Apply<T1, T2>(IDataActionVisitor<T1, T2> visitor, T1 x, T2 y)
        {
            throw new NotImplementedException();
        }

        public override TR Apply<TR>(IDataFuncVisitor<TR> visitor)
        {
            throw new NotImplementedException();
        }

        public override TR Apply<T, TR>(IDataFuncVisitor<T, TR> visitor, T x)
        {
            throw new NotImplementedException();
        }

        public override TR Apply<T1, T2, TR>(IDataFuncVisitor<T1, T2, TR> visitor, T1 x, T2 y)
        {
            throw new NotImplementedException();
        }
    }
}
