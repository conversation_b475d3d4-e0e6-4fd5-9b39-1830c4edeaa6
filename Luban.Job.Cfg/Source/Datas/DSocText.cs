using DocumentFormat.OpenXml.Drawing.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;
using Luban.Common.Utils;
using Luban.Job.Cfg.Defs;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;

namespace Luban.Job.Cfg.Datas
{
    public class DSocText : DText
    {
        public const string ID_NAME = "index";
        public const string DESC_NAME = "desc";
        public const string LENGTH_NAME = "eng_length";

        public override string TypeName => "soctext";

        public readonly int EngLength;
        public readonly int HashCode;
        public static ConcurrentDictionary<string, int> Text2Hash = new();
        public static ConcurrentDictionary<int, object> AllHash = new();

        public DSocText(string key, string x, int engLength) : base(key, x)
        {
            if (key.EndsWith("invalid"))
            {
                throw new Exception($"多语言 key:{Key} 无效");
            }
            EngLength = engLength;
            if (Text2Hash.TryGetValue(key, out var hash))
            {
                HashCode = hash;
            }
            else
            {
                var newHash = key.GetStableHashCode();
                if (AllHash.ContainsKey(newHash))
                {
                    for (int i = 1; i <= 1000; i++)
                    {
                        if (!AllHash.ContainsKey(newHash + i))
                        {
                            newHash = newHash + i;
                            break;
                        }
                    }
                    HashCode = newHash;
                    Text2Hash.TryAdd(key, HashCode);
                    AllHash.TryAdd(HashCode, null);
                }
                else
                {
                    HashCode = newHash;
                    Text2Hash.TryAdd(key, HashCode);
                    AllHash.TryAdd(HashCode, null);
                }
            }
        }


        public override string TextOfCurrentAssembly
        {
            get
            {
                var ass = DefAssembly.LocalAssebmly;
                if (ass.SocTextDict.TryGetValue(Key, out var info) && info != this)
                {
                    throw new Exception($"多语言 key:{Key} 重复");
                }

                ass.SocTextDict[Key] = this;
                return RawValue;
            }
        }
    }

    public class DSocString : DString
    {
        public override string TypeName => FieldType;

        public const string FieldType = "socrescheck";
        public const string IdField = "Id";
        public const string LocationField = "Location";
        public const string FilePathFiled = "FileName";
        public const string RowIdFiled = "RowId";
        public const string TitleNameFiled = "TitleName";
        public const string ContentField = "Content";

        public string UniqueKey;
        public string FileName;
        public string RowId;
        public string TitleName;

        private void CollectResCheckKey()
        {
            var ass = DefAssembly.LocalAssebmly;

            if (Value != "")
            {
                ass.SocResCheckDict.TryAdd(UniqueKey, this);
            }
        }

        public DSocString(string uniqueKey, string fileName, string titleName, string rowId, string key) : base(key)
        {
            UniqueKey = uniqueKey + Value;
            FileName = fileName;
            RowId = rowId;
            TitleName = titleName;
            CollectResCheckKey();
        }


        public int HashCode => UniqueKey.GetStableHashCode();
    }
}
