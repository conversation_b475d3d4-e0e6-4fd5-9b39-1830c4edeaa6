using Luban.Job.Cfg.Defs;
using Luban.Job.Cfg.TypeVisitors;
using Luban.Job.Common.Defs;
using Luban.Job.Common.RawDefs;
using Luban.Job.Common.Tpl;
using Luban.Job.Common.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Luban.Job.Cfg.Generate
{
    abstract class TemplateCodeRenderBase : CodeRenderBase
    {
        protected virtual string CommonRenderTemplateDir => RenderFileUtil.GetCommonTemplateDirName(DefAssembly.LocalAssebmly.CurrentLanguage);

        protected abstract string RenderTemplateDir { get; }

        protected Scriban.Template GetConfigTemplate(string name)
        {
            return StringTemplateManager.Ins.GetTemplate($"config/{RenderTemplateDir}/{name}");
        }

        protected Scriban.Template GetCommonTemplate(string name)
        {
            return StringTemplateManager.Ins.GetTemplate($"common/{CommonRenderTemplateDir}/{name}");
        }

        public override void Render(GenContext ctx)
        {
            // [ALB] 启动生成代码低内存的模板
            GenerateCodeGetValue(ctx);
            GenerateCodeScatter(ctx);
        }

        public override string Render(DefEnum e)
        {
            var template = GetCommonTemplate("enum");
            var result = template.RenderCode(e);

            return result;
        }

        public override string Render(DefBean b)
        {
            string templateName = "bean";
            var template = GetConfigTemplate(templateName);
            var result = template.RenderCode(b);
            return result;
        }

        public override string Render(DefTable p)
        {
            string templateName = "table";
            var template = GetConfigTemplate(templateName);
            var result = template.RenderCode(p);
            return result;
        }

        public override string RenderService(string name, string module, List<DefTable> tables)
        {
            var template = GetConfigTemplate("tables");
            var result = template.RenderCode(new {
                Name = name,
                Namespace = module,
                Tables = tables,
                TableCount = tables.Count.ToString(),
            });
            return result;
        }

        public virtual string RenderAll(List<DefTypeBase> types)
        {
            var enums = types.Where(t => t is DefEnum).ToList();
            var beans = types.Where(t => t is DefBean).ToList();
            var tables = types.Where(t => t is DefTable).ToList();

            var template = GetConfigTemplate("all");
            var result = template.RenderCode(new {
                Namespace = DefAssembly.LocalAssebmly.TopModule,
                Enums = enums.Select(e => Render((DefEnum)e)).ToList(),
                Beans = beans.Select(b => Render((DefBean)b)).ToList(),
                Tables = tables.Select(t => Render((DefTable)t)).ToList(),
            });
            return result;
        }

        /// <summary>
        /// [ALB] 生成Json获取值的通用方法，以此减少生成代码的内存
        /// </summary>
        /// <param name="ctx"></param>
        /// <exception cref="Exception"></exception>
        public void GenerateCodeGetValue(GenContext ctx)
        {
            string genType = ctx.GenType;
            ctx.Render = this;
            ctx.Lan = GetLanguage(ctx);
            DefAssembly.LocalAssebmly.CurrentLanguage = ctx.Lan;

            CsUnityJsonCollector collector = new CsUnityJsonCollector();

            List<DefTypeBase> exportTypes = ctx.ExportTypes;
            foreach (var exportType in exportTypes)
            {
                switch (exportType)
                {
                    case DefEnum e:
                        break;
                    case DefBean b:
                    {
                        foreach (var field in b.ExportFields)
                        {
                            field.CType.Apply(collector, 0);
                        }
                        break;
                    }
                    case DefTable t:
                    {
                        var bean = t.ValueTType.Bean;
                        if (bean != null)
                            foreach (var field in bean.Fields)
                            {
                                field.CType.Apply(collector, 0);
                            }
                        break;
                    }
                    default:
                        throw new Exception($"unknown render type:'{exportType}'");
                }
            }

            CsUnityJsonCollection collection = collector.AsValueTypeCollection();
            collection.PortDefine = ctx.GenArgs.ExportType == "client" ? "SOC_CLIENT" :  "!SOC_CLIENT";
            AddRenderGetValueTask(ctx, collection, "convert_array", "JsonEx/SimpleJSONConvertArray");
            AddRenderGetValueTask(ctx, collection, "convert_list", "JsonEx/SimpleJSONConvertList");
            AddRenderGetValueTask(ctx, collection, "convert_map", "JsonEx/SimpleJSONConvertMap");
            AddRenderGetValueTask(ctx, collection, "convert_vector", "JsonEx/SimpleJSONConvertVector");

            AddRenderGetValueTask(ctx, collection, "try_get_value", "JsonEx/SimpleJSONGetValue");
            AddRenderGetValueTask(ctx, collection, "try_get_enum", "JsonEx/SimpleJSONGetEnum");
            AddRenderGetValueTask(ctx, collection, "try_get_bean", "JsonEx/SimpleJSONGetBean");
            AddRenderGetValueTask(ctx, collection, "try_get_text_key", "JsonEx/SimpleJSONGetTextKey");
            AddRenderGetValueTask(ctx, collection, "try_get_array", "JsonEx/SimpleJSONGetArray");
            AddRenderGetValueTask(ctx, collection, "try_get_list", "JsonEx/SimpleJSONGetList");
            AddRenderGetValueTask(ctx, collection, "try_get_map", "JsonEx/SimpleJSONGetMap");
        }

        public void AddRenderGetValueTask(GenContext ctx, CsUnityJsonCollection collection, string tplName, string fileName)
        {
            ctx.Tasks.Add(Task.Run(() =>
            {
                var template = GetConfigTemplate(tplName);
                string body = template.RenderCode(collection);
                if (string.IsNullOrWhiteSpace(body))
                {
                    return;
                }

                var content = FileHeaderUtil.ConcatAutoGenerationHeader(body, ctx.Lan);
                var file = RenderFileUtil.GetDefTypePath(fileName, ctx.Lan);
                var md5 = CacheFileUtil.GenMd5AndAddCache(file, content);
                ctx.GenCodeFilesInOutputCodeDir.Add(new Luban.Common.Protos.FileInfo() { FilePath = file, MD5 = md5 });
            }));
        }
    }
}
