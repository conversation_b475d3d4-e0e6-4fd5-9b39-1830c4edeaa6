using Luban.Job.Common.Defs;
using System.Collections.Generic;

namespace Luban.Job.Cfg.Tags
{
    public class FindFieldByTypeVisitor : DefineTypeVisitor
    {
        public string TypeName;
        public List<TableGridField> Result;

        public static bool Find(DefBeanBase type, string typeName, out List<TableGridField> result)
        {
            FindFieldByTypeVisitor visitor = new FindFieldByTypeVisitor();
            bool found = visitor.Find(type, typeName);
            result = found ? visitor.Result : null;
            return found;
        }

        public bool Find(DefBeanBase type, string typeName)
        {
            TypeName = typeName;

            TableGridID id = new TableGridID() { Level = 0, FieldIndex = 0 };
            Accept(null, type, id);
            return Result != null;
        }

        public override void Accept(DefFieldBase hostField, DefFieldBase field, TableGridID id)
        {
            if (field.Type == TypeName)
            {
                // ����������Դ�����ƺ������ƣ��������봦��
                Result ??= new List<TableGridField>();
                Result.Add(new TableGridField()
                {
                    HostField = hostField,
                    Field = field,
                    TagValue = TypeName,
                    Id = id
                });
            }
            //Console.WriteLine($"FindDefineFieldByTagVisitor.Accept level:{level} field:{fieldIndex} Name:{field.Name}");
            base.Accept(hostField, field, id);
        }
    }

}
