using Luban.Job.Cfg.Datas;
using Luban.Job.Cfg.DataSources;
using Luban.Job.Cfg.Defs;
using System;
using System.Collections.Generic;

namespace Luban.Job.Cfg.Tags
{
    class FindSocResCheckVisitor : DataFieldVisitor
    {
        public DefTable Table;
        // 表的资源键值到资源的映射
        public Dictionary<int, string> KeyToAssetMap;
        // 表的行到该行所有资源键值的映射
        public Dictionary<DBean, List<int>> BeanToAssetsMap;

        public static bool Find(DefTable table, TableDataInfo tableDataInfo, out Dictionary<int, string> keyToAssetMap, out Dictionary<DBean, List<int>> beanToAssetsMap)
        {
            if (!FindFieldByTypeVisitor.Find(table.ValueTType.Bean, DSocString.FieldType, out var _))
            {
                keyToAssetMap = null;
                beanToAssetsMap = null;
                return false;
            }

            keyToAssetMap = new Dictionary<int, string>();
            beanToAssetsMap = new Dictionary<DBean, List<int>>();

            FindSocResCheckVisitor visitor = new FindSocResCheckVisitor();
            visitor.Table = table;
            visitor.KeyToAssetMap = keyToAssetMap;
            visitor.BeanToAssetsMap = beanToAssetsMap;

            bool found = visitor.Find(tableDataInfo.FinalRecords);
            foreach (var (_, playModuleRecords) in tableDataInfo.PlayModuleRecords)
            {
                if (playModuleRecords.Count > 0 && visitor.Find(playModuleRecords))
                    found = true;
            }
            return found;
        }

        public static bool Find(DefTable table, List<Record> records, out Dictionary<int, string> keyToAssetMap, out Dictionary<DBean, List<int>> beanToAssetsMap)
        {
            if (!FindFieldByTypeVisitor.Find(table.ValueTType.Bean, DSocString.FieldType, out var _))
            {
                keyToAssetMap = null;
                beanToAssetsMap = null;
                return false;
            }

            FindSocResCheckVisitor visitor = new FindSocResCheckVisitor();
            visitor.Table = table;
            bool found = visitor.Find(records);
            keyToAssetMap = visitor.KeyToAssetMap;
            beanToAssetsMap = visitor.BeanToAssetsMap;
            return found;
        }

        public bool Find(List<Record> records)
        {
            base.Visit(records);
            return KeyToAssetMap != null;
        }

        public bool Find(DBean bean)
        {
            TableGridID id = new TableGridID() { Level = 0, FieldIndex = 0 };
            bean.Apply(this, null, id);
            return KeyToAssetMap != null;
        }

        public override bool Accept(DString data, DBean host, TableGridID id)
        {
            if (data.TypeName == DSocString.FieldType)
            {
                var ap = (data as DSocString).Value;
                if (string.IsNullOrWhiteSpace(ap))
                    return true;

                ap = ap.Trim().Replace('\\', '/');
                var lowerAp = ap.ToLower();
                //var key = ap.GetHashCode();
                var key = Convert.GetStableHash(lowerAp);

                KeyToAssetMap ??= new Dictionary<int, string>(16);
                BeanToAssetsMap ??= new Dictionary<DBean, List<int>>(16);

                if (!KeyToAssetMap.TryAdd(key, ap))
                {
                    if (KeyToAssetMap.TryGetValue(key, out var oap) && oap != ap)
                    {
                        throw new Exception($"{string.Join('/', Table.InputFiles)} {Table.Name}中\n，请检查资源路径{oap}和{ap}大小写是否不同 Hash:{key}");
                    }
                }
                //var hostKey = Convert.ToAddress(host);
                if (!BeanToAssetsMap.TryGetValue(host, out var assets))
                    BeanToAssetsMap.Add(host, new List<int>(2) { key });
                else if (!assets.Contains(key))
                    assets.Add(key);
            }
            return true;
        }
    }
}
