using Luban.Job.Cfg.Datas;
using Luban.Job.Cfg.DataSources;
using Luban.Job.Cfg.DataVisitors;
using Luban.Job.Cfg.Defs;
using Luban.Job.Cfg.Utils;
using Luban.Job.Common.Defs;
using Luban.Job.Common.Types;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace Luban.Job.Cfg.Tags
{
    class FindDataByTagVisitor : DataFieldVisitor
    {
        public string TagKey;
        public List<TableGridData> Result;

        public static bool Find(DefTable table, string tagKey, out List<TableGridData> result)
        {
            result = null;
            var findTagVisitor = new FindFieldByTagVisitor();
            if (!findTagVisitor.Find(table.ValueTType.Bean, tagKey))
                return false;

            FindDataByTagVisitor visitor = new FindDataByTagVisitor();
            bool found = visitor.Find(table, tagKey);
            result = visitor.Result;
            return found;
        }

        public bool Find(DefTable table, string tagKey)
        {
            var records = table.Assembly.GetTableAllDataList(table);
            if (records.Count == 0)
                return false;

            TagKey = tagKey;
            base.Visit(records);

            return Result != null;
        }

        public override bool Accept(DBean data, DBean host, TableGridID id)
        {
            bool done = false;
            var dataFields = data.Fields;
            var typeFields = data.ImplType.HierarchyFields;
            for (int i = 0; i < dataFields.Count; ++i)
            {
                var dataField = dataFields[i];
                var typeField = typeFields[i];

                // ���⴦�� bean ��̬����
                if (dataField == null || dataField == DNull.Default)
                    continue;

                id.FieldIndex = i;
                if (typeField.Tags.Count > 0 && typeField.Tags.TryGetValue(TagKey, out var tagValue))
                {
                    Result ??= new List<TableGridData>();
                    Result.Add(new TableGridData()
                    {
                        Host = data,
                        Value = dataField,
                        Field = typeField,
                        TagValue = tagValue,
                        Id = id
                    });
                }
                else if (!dataField.Apply(this, data, id))
                {
                    done = true;
                    break;
                }
            }

            return !done;
        }

    }
}
