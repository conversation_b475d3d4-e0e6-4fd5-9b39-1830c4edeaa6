using DocumentFormat.OpenXml.Office2013.Excel;
using Luban.Common.Utils;
using Luban.Job.Cfg.Datas;
using Luban.Job.Cfg.Defs;
using Luban.Job.Cfg.Source.Tags;
using Luban.Job.Common.Defs;
using Luban.Job.Common.Types;
using Luban.Server.Common;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Luban.Job.Cfg.Tags
{
    internal class TableFieldCommentWriter
    {
        TableFieldCommentMap tableCommentMap = new TableFieldCommentMap();


        public static void Write(DefAssembly ass, RemoteAgent agent, string inputDataDir, string outputCodeDir)
        {
            TableFieldCommentWriter writer = new TableFieldCommentWriter();

            Stopwatch stopwatch = Stopwatch.StartNew();

            List<DefTable> exportTables = ass.GetExportTables();
            Dictionary<DefBeanBase, DefTable> beanToTableMap = new Dictionary<DefBeanBase, DefTable>(exportTables.Count);

            foreach (var table in exportTables)
            {
                var bean = table.ValueTType.Bean;
                if (bean != null)
                    beanToTableMap.Add(bean, table);
            }

            foreach (var table in exportTables)
            {
                var bean = table.ValueTType.Bean;
                if (bean == null)
                    continue;

                foreach (var field in bean.Fields)
                {
                    CollectBeanType(beanToTableMap, field.CType.ElementType);
                }
                //CollectBeanType(exportBeans, bean);
            }

            //var tasks = new List<Task>();
            foreach (var (bean, table) in beanToTableMap)
            {
                //tasks.Add(Task.Run(() => {
                writer.CollectComments(ass, bean, table);
                //}));
            }

            //Task.WaitAll(tasks.ToArray());
            stopwatch.Stop();
            Console.WriteLine($"TableFieldCommentWriter.CollectComments 耗时: {stopwatch.ElapsedMilliseconds}");

            // 保存表格的打包tag和资源引用
            stopwatch.Start();
            var outputFileName = Path.Join(outputCodeDir, "../..", "Resources", "Data", "Helper", "table_comment.json");
            writer.WriteJson(outputFileName);
            stopwatch.Stop();
            Console.WriteLine($"TableFieldCommentWriter.WriteJson 耗时: {stopwatch.ElapsedMilliseconds}");
        }

        private void CollectComments(DefAssembly ass, DefBeanBase bean, DefTable table)
        {
            var fields = bean.Fields;
            var fieldComments = new List<FieldComment>(fields.Count);
            foreach (var defField in fields)
            {
                var fieldComment = new FieldComment();
                fieldComment.Name = defField.Name;
                fieldComment.Comment = defField.Comment;
                fieldComment.TypeName = GetTypeName(defField.CType);

                fieldComments.Add(fieldComment);
            }

            TableFieldComment tableComment = new TableFieldComment();
            if (table != null)
            {
                tableComment.TypeName = table.FullName;
                //tableComment.DataName = table.OutputDataFile;
                tableComment.InputFiles = table.InputFiles;
            }
            else
            {
                tableComment.TypeName = bean.FullName;
                //tableComment.DataName = tableComment.Name;
                tableComment.InputFiles = new List<string>() { bean.InputFile };
            }

            tableComment.Fields = fieldComments;

            tableCommentMap.Tables.Add(tableComment);
        }

        private static string GetTypeName(TType type)
        {
            if (type.IsCollection)
                return $"{type.TypeName}<{GetTypeName(type.ElementType)}>";
            if (type.IsBean)
                return ((TBean)type).Bean.FullName;
            if (type is TEnum enumType)
                return enumType.DefineEnum.FullName;
            else
                return type.TypeName;
        }

        private static void CollectBeanType(Dictionary<DefBeanBase, DefTable> beanToTableMap, DefBeanBase bean, DefTable table = null)
        {
            if (bean == null || !beanToTableMap.TryAdd(bean, table))
                return;

            foreach (var field in bean.Fields)
            {
                CollectBeanType(beanToTableMap, field.CType.ElementType);
            }
        }

        private static void CollectBeanType(Dictionary<DefBeanBase, DefTable> beanToTableMap, TType type)
        {
            if (type == null)
                return;

            if (type.IsBean)
            {
                TBean beanType = type as TBean;
                CollectBeanType(beanToTableMap, beanType.Bean);
            }
            else if (type.IsCollection)
            {
                CollectBeanType(beanToTableMap, type.ElementType);
            }
        }

        private void WriteJson(string filePath)
        {
            string fileDir = Path.GetDirectoryName(filePath);
            if (!Directory.Exists(fileDir))
                Directory.CreateDirectory(fileDir);

            // 对导出表名称排序，方便差异对比
            tableCommentMap.Tables.Sort((x, y) => x.TypeName.CompareTo(y.TypeName));

#if false
            var items = JsonConvert.SerializeObject(tableCommentMap, Newtonsoft.Json.Formatting.Indented); 
            File.WriteAllText(filePath, items);
#else
            using var ss = new FileStream(filePath, FileMode.Create);
            var jsonWriter = new Utf8JsonWriter(ss, new JsonWriterOptions()
            {
                Indented = true,
                SkipValidation = false,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
            });
            jsonWriter.WriteStartObject();

            // 表格
            jsonWriter.WritePropertyName("Tables");
            jsonWriter.WriteStartArray();
            foreach (var table in tableCommentMap.Tables)
            {
                jsonWriter.WriteStartObject();

                //// json数据文件名称
                //jsonWriter.WritePropertyName("Name");
                //jsonWriter.WriteStringValue(table.Name);
                // 表格名称
                jsonWriter.WritePropertyName("TypeName");
                jsonWriter.WriteStringValue(table.TypeName);

                // 数据来源文件（Excel表格或Json文件目录）
                jsonWriter.WritePropertyName("InputFiles");
                jsonWriter.WriteStartArray();
                if (table.InputFiles != null)
                {
                    foreach (var inputFile in table.InputFiles)
                        jsonWriter.WriteStringValue(inputFile);
                }
                jsonWriter.WriteEndArray();

                // 字段
                var fields = table.Fields;
                jsonWriter.WritePropertyName("Fields");
                jsonWriter.WriteStartArray();
                for (int i = 0; i < fields.Count; i++)
                {
                    jsonWriter.WriteStartObject();
                    jsonWriter.WritePropertyName("Name");
                    jsonWriter.WriteStringValue(fields[i].Name);
                    jsonWriter.WritePropertyName("Comment");
                    jsonWriter.WriteStringValue(fields[i].Comment);
                    jsonWriter.WritePropertyName("TypeName");
                    jsonWriter.WriteStringValue(fields[i].TypeName);
                    jsonWriter.WriteEndObject();
                }
                jsonWriter.WriteEndArray();

                jsonWriter.WriteEndObject();
            }
            jsonWriter.WriteEndArray();


            jsonWriter.WriteEndObject();
            jsonWriter.Flush();
            ss.Flush();
#endif
        }

    }
}
