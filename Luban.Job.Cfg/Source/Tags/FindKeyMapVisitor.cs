using DocumentFormat.OpenXml.Office2013.Excel;
using Luban.Job.Cfg.DataCreators;
using Luban.Job.Cfg.Datas;
using Luban.Job.Cfg.DataSources;
using Luban.Job.Cfg.Defs;
using Luban.Job.Cfg.RawDefs;
using Luban.Job.Cfg.Tags;
using Luban.Job.Cfg.Utils;
using Luban.Job.Common.Defs;
using Luban.Job.Common.Source.Types;
using Luban.Job.Common.Types;
using Luban.Job.Common.TypeVisitors;
using Luban.Job.Common.Utils;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace Luban.Job.Cfg.Validators
{
    public class FindKeyMapVisitor : DataFieldVisitor
    {
        public DKeyMap Result;
        public DKeyMap Current;
        public DefTable Table;

        public DType CurrentCollection;
        public Stack<DType> Paths;
        public List<TableGridField> KeyFields;

        public bool Find(DefTable table, List<TableGridField> keyFields = null)
        {
            if (table.Mode == ETableMode.ONE)
                return false;

            Current = new DKeyMap();
            Result = Current;
            Table = table;
            KeyFields = keyFields !=null ? keyFields : FindFieldByTagVisitor.GetKeyFields(table, TagDefines.KEY);

            Paths = new Stack<DType>(4);
            var records = table.Assembly.GetTableAllDataList(table);
            base.Visit(records);

            return Result.KeyMap != null;
        }

        public static string GetKeyPaths(DefTable table, Stack<DType> paths, List<TableGridField> keyFields)
        {
            StringBuilder sb = new StringBuilder(64);
            sb.Append(table.InputFiles[0]);
            for (int i = 1; i < table.InputFiles.Count; ++i)
            {
                sb.Append('/').Append(table.InputFiles[i]);
            }
            sb.Append(' ').Append(table.Name);

            int fieldIndex = 0;
            foreach (var path in paths.Reverse())
            {
                var field = keyFields[fieldIndex];
                sb.Append('.').Append(field.Field.Name).Append('[').Append(path).Append(']');
                ++fieldIndex;
            }

            return sb.ToString();
        }

        //public override bool Accept(DMap data, DBean host, TableGridID id)
        //{
        //    var previous = Current;
        //    var previousKeyMap = Current.GetOrCreateKeyMap();

        //    ++id.Level;
        //    foreach (var (key, value) in data.Datas)
        //    {
        //        DKeyMap keyMap = new DKeyMap();
        //        keyMap.Host = host;
        //        //keyMap.Field = data.Type.KeyType.;
        //        previousKeyMap.Add(key, keyMap);

        //        Current = keyMap;
        //        value.Apply(this, host, id);
        //    }

        //    Current = previous;
        //    return true;
        //}

        public override bool Accept(DBean data, DBean host, TableGridID id)
        {
            if (KeyFields.Count <= id.Level)
                return true;

            var previous = Current;
            var keyIndex = KeyFields[id.Level].Id.FieldIndex;
            var dataField = data.Fields[keyIndex];

            Paths.Push(dataField);
            DKeyMap keyMap = new DKeyMap();
            keyMap.Host = data;
            keyMap.Field = data.ImplType.HierarchyFields[keyIndex];
            if (!Current.GetOrCreateKeyMap().TryAdd(dataField, keyMap))
            {
                //throw new Exception($"{GetKeyPaths(Table, Paths, KeyFields)} 键值重复");
            }

            if (KeyFields.Count > id.Level + 1)
            {
                Current = keyMap;
                base.Accept(data, host, id);
                Current = previous;
            }

            Paths.Pop();
            return true;
        }
    }

}
