using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Luban.Job.Cfg.Source.Tags
{
    internal struct FieldComment
    {
        public string Name;
        public string Comment;
        public string TypeName;
    }
    internal struct TableFieldComment
    {
        public string TypeName;
        //public string Name;
        public List<string> InputFiles;

        public List<FieldComment> Fields;
    }

    internal class TableFieldCommentMap
    {
        public List<TableFieldComment> Tables = new List<TableFieldComment>();
    }
}
