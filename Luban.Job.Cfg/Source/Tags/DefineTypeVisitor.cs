using Luban.Job.Common.Defs;
using Luban.Job.Common.Types;
using Luban.Job.Common.TypeVisitors;
using System;
using System.Collections.Generic;

namespace Luban.Job.Cfg.Tags
{
    public class DefineTypeVisitor
    {
        public virtual void Accept(DefFieldBase hostField, DefBeanBase bean, TableGridID id)
        {
            if (bean == null)
                return;

            var fields = bean.Fields;
            for (int i = 0; i < fields.Count; ++i)
            {
                id.FieldIndex = i;
                Accept(hostField, fields[i], id);
            }
        }

        public virtual void Accept(DefFieldBase hostField, DefFieldBase field, TableGridID id)
        {
            TType type = field.CType;
            if (type.IsBean)
            {
                TBean beanType = type as TBean;
                Accept(hostField, beanType.Bean, id);
            }
            else if (type.IsCollection)
            {
                ++id.Level;
                Visit(field, type.ElementType, id);
            }
        }

        private void Visit(DefFieldBase hostField, TType type, TableGridID id)
        {
            if (type.IsBean)
            {
                TBean beanType = type as TBean;
                Accept(hostField, beanType.Bean, id);
            }
            else if (type.IsCollection)
            {
                ++id.Level;
                Visit(hostField, type.ElementType, id);
            }
        }

    }

}
