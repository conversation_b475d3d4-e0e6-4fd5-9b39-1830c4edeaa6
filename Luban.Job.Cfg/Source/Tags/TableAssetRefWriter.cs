using Luban.Job.Cfg.DataCreators;
using Luban.Job.Cfg.Datas;
using Luban.Job.Cfg.DataSources;
using Luban.Job.Cfg.Defs;
using Luban.Job.Cfg.Validators;
using Luban.Job.Common.Defs;
using Luban.Job.Common.Types;
using Luban.Server.Common;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Luban.Job.Cfg.Tags
{
    public class TableRes
    {
        // 表的键值
        public int TableKey;
        // 表名称
        public string TableName;
        // 表的打包标签
        public string[] Tags;

        // 表的行到该行所有资源键值的映射
        public Dictionary<DBean, List<int>> BeanToAssetsMap;
        // 表的资源键值到资源的映射
        public Dictionary<int, string> KeyToAssetMap;

        // 依赖键名称到间接依赖键数据的映射表
        public Dictionary<string, List<TableGridData>> DepToDataMap;

        // 表数据行
        public DefTable Table;
        public DefBeanBase Bean;
    }

    struct FieldKeyMap
    {
        public DKeyMap KeyMap;
        public List<TableGridField> Fields;
        public TableRes TableRes;

        public static FieldKeyMap Create(DKeyMap km, TableRes tableRes)
        {
            var value = new FieldKeyMap();
            value.KeyMap = km;
            value.TableRes = tableRes;
            return value;
        }
    }

    class MultiKeyMap
    {
        // 由于一列键值可以对应多个表的键值，因此此处为队列
        public List<FieldKeyMap> KeyMaps = new List<FieldKeyMap>();

        public bool TryGetValue(DType key, out FieldKeyMap value)
        {
            for (int i = 0; i < KeyMaps.Count; ++i)
            {
                if (KeyMaps[i].KeyMap.KeyMap.TryGetValue(key, out var km))
                {
                    value = new FieldKeyMap() { KeyMap = km, TableRes = KeyMaps[i].TableRes };
                    return true;
                }
            }

            value = default;
            return false;
        }

        public bool TryGetValue(DType key, TType keyType, List<FieldKeyMap> values)
        {
            values.Clear();
            if (keyType.IsCollection)
            {
                // 多个键值，查找多行
                List<DType> keys = null;
                if (key is DArray keyArr)
                    keys = keyArr.Datas;
                else if (key is DList keyList)
                    keys = keyList.Datas;
                else if (key is DSet keySet)
                    keys = keySet.Datas;

                if (keys == null || keys.Count == 0)
                    return false; // 没填键值

                foreach (var k in keys)
                {
                    if (TryGetValue(k, out var val))
                        values.Add(val);
                }
            }
            else if (TryGetValue(key, out var val))
            {
                // 单个键值，查找一行
                values.Add(val);
            }
            else if (key is DString keyString)
            {
                // 可能是联合键值，查找一行，以字符'.'分隔
                string[] keyStrings = keyString.Value.Split('.', StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < KeyMaps.Count; i++)
                {
                    var fields = KeyMaps[i].Fields;
                    if (fields.Count != keyStrings.Length)
                        return false; // 没填键值或键值格式不对

                    var km = KeyMaps[i].KeyMap;
                    for (int k = 0; k < fields.Count; ++k)
                    {
                        var kt = fields[k].Field.CType;
                        var kd = kt.Apply(StringDataCreator.Ins, keyStrings[k]);
                        if (!km.KeyMap.TryGetValue(key, out var value))
                        {
                            km = null;
                            break;
                        }

                        km = value;
                    }

                    if (km != null)
                    {
                        var value = new FieldKeyMap() { KeyMap = km, TableRes = KeyMaps[i].TableRes };
                        values.Add(value);
                        break;
                    }
                }
            }

            return values.Count > 0;
        }
    }

    class TableKeyMap
    {
        public ConcurrentDictionary<string, TableRes> NameToTableMap;
        public Dictionary<string, FieldKeyMap> RefToKeyMapMap = new Dictionary<string, FieldKeyMap>();
        public Dictionary<string, MultiKeyMap> RefToMultiKeyMapMap = new Dictionary<string, MultiKeyMap>();

        private FindFieldByNameVisitor findFieldVisitor = new FindFieldByNameVisitor();
        private FindKeyMapVisitor findKeyMapVisitor = new FindKeyMapVisitor();

        public MultiKeyMap GetOrCreateMultKeyMap(DefTable table, TableGridData data)
        {
            if (RefToMultiKeyMapMap.TryGetValue(data.TagValue, out var value))
                return value;

            // 一列键值可以对应多个表的键值，以字符'|'分隔，表1查不到，就接下来查表2，直到找到为止
            var tags = data.TagValue.Split('|', StringSplitOptions.RemoveEmptyEntries);
            //if (tags.Length > 1)
            //{
            //    Console.WriteLine($"{string.Join('/', table.InputFiles)} {table.Name}.{data.Field.Name} res_ref={data.TagValue}");
            //}
            MultiKeyMap mkMap = new MultiKeyMap();
            for (int i = 0; i < tags.Length; i++)
            {
                var tag = tags[i];
                if (RefToKeyMapMap.TryGetValue(tag, out var km))
                {
                    mkMap.KeyMaps.Add(km);
                    continue;
                }

                // 一列键值由格式'表名.列名1.列名2...'组成，支持联合键值（多个键值对一行）或队列键值（一个键值对多行）
                var keyNames = tag.Split('.');
                if (keyNames.Length < 2)
                    throw new Exception($"{string.Join('/', table.InputFiles)} {table.Name}.{data.Field.Name} res_ref={data.TagValue}，格式不对必须是'表名.列名1.列名2...'");

                var depTableName = keyNames[0];
                if (string.IsNullOrEmpty(depTableName) || !NameToTableMap.TryGetValue(depTableName, out var depTableRes))
                    throw new Exception($"{string.Join('/', table.InputFiles)} {table.Name}.{data.Field.Name} res_ref={data.TagValue}, 找不到依赖表:{depTableName}");

                if (!findFieldVisitor.Find(depTableRes.Bean, keyNames, 1) || !findKeyMapVisitor.Find(depTableRes.Table, findFieldVisitor.Result))
                    throw new Exception($"{string.Join('/', table.InputFiles)} {table.Name}.{data.Field.Name}[{data.Value}] res_ref={data.TagValue}, 找不到依赖列");

                km = new FieldKeyMap();
                km.KeyMap = findKeyMapVisitor.Result;
                km.Fields = findFieldVisitor.Result;
                km.TableRes = depTableRes;
                RefToKeyMapMap.Add(tag, km);
                mkMap.KeyMaps.Add(km);
            }

            RefToMultiKeyMapMap.Add(data.TagValue, mkMap);
            return mkMap;
        }

    }

    // [ALB] 导出资源打包Tags
    internal class TableAssetRefWriter
    {
        public ConcurrentDictionary<string, TableRes> NameToTableMap = new ConcurrentDictionary<string, TableRes>();
        public Dictionary<int, TableAssetRef> KeyToAssetMap = new Dictionary<int, TableAssetRef>(8192);

        public static void Write(DefAssembly ass, RemoteAgent agent, string inputDataDir, string outputCodeDir)
        {
            TableAssetRefWriter writer = new TableAssetRefWriter();

            Stopwatch stopwatch = Stopwatch.StartNew();
            // 查找每个表格中资源，以及资源引用的其他表
            List<DefTable> exportTables = ass.GetExportTables();
#if true
            var tasks = new Task[exportTables.Count];
            for (int i = 0; i < exportTables.Count; ++i)
            {
                var t = exportTables[i];
                tasks[i] = Task.Run(() =>
                    writer.CreateTableRes(ass, t));
            }
            Task.WaitAll(tasks);
#else
            foreach (var t in exportTables)
            {
                writer.CreateTableRes(ass, t);
            }
#endif
            stopwatch.Stop();
            Console.WriteLine($"TableResRefWriter.CreateTableResRef 耗时: {stopwatch.ElapsedMilliseconds}");

            stopwatch.Start();
            // 查找每个表格中资源引用的其他表资源
            writer.GetAssetResRefMap();
            stopwatch.Stop();
            Console.WriteLine($"TableResRefWriter.GetAssetResRefMap 耗时: {stopwatch.ElapsedMilliseconds}");

            // 保存表格的打包tag和资源引用
            stopwatch.Start();
            var resRefFilePath = Path.Join(outputCodeDir, "../..", "Resources", "Data", "ResCheck", "res_tag.json");
            WriteJson(resRefFilePath, writer.NameToTableMap.Values.ToList(), writer.KeyToAssetMap.Values.ToList());
            stopwatch.Stop();
            Console.WriteLine($"TableResRefWriter.WriteJson 耗时: {stopwatch.ElapsedMilliseconds}");
        }

        private void CreateTableRes(DefAssembly ass, DefTable defTable)
        {
            var tableDataInfo = ass.GetTableDataInfo(defTable);
            if (tableDataInfo.FinalRecords.Count == 0 && tableDataInfo.PlayModuleRecords.Count == 0)
                return;

            DefBeanBase defBean = defTable.ValueTType.Bean;
            TableRes tableRes = new TableRes();
            tableRes.TableName = defTable.Name;
            tableRes.TableKey = Convert.GetStableHash(tableRes.TableName);
            tableRes.Table = defTable;
            tableRes.Bean = defBean;

            var indexField = defTable.IndexField;
            if (indexField?.Tags != null && indexField.Tags.TryGetValue(TagDefines.BUNDLE, out var tagBundleName))
            {
                // 表Tags，以'.'分割
                tableRes.Tags = tagBundleName.Split('.', StringSplitOptions.RemoveEmptyEntries);
            }

            // 查找直接引用的资源
            //FindSocResCheckVisitor.Find(defTable, records, out tableRes.KeyToAssetMap, out tableRes.BeanToAssetsMap);
            FindSocResCheckVisitor.Find(defTable, tableDataInfo, out tableRes.KeyToAssetMap, out tableRes.BeanToAssetsMap);

            // 查找间接依赖的资源
            if (FindDataByTagVisitor.Find(defTable, TagDefines.RES_REF, out var depDataList))
            {
                Dictionary<string, List<TableGridData>> depToDataMap = new Dictionary<string, List<TableGridData>>();
                tableRes.DepToDataMap = depToDataMap;
                for (int i = 0; i < depDataList.Count; i++)
                {
                    var depData = depDataList[i];
                    if (depToDataMap.TryGetValue(depData.TagValue, out var deps))
                        deps.Add(depData);
                    else
                        depToDataMap.Add(depData.TagValue, new List<TableGridData>() { depData });
                }
            }

            NameToTableMap.TryAdd(tableRes.TableName, tableRes);
        }

        private Dictionary<int, TableAssetRef> GetAssetResRefMap()
        {
            if (KeyToAssetMap.Count > 0)
                return KeyToAssetMap;

            Dictionary<int, TableRes> keyToTableMap = new Dictionary<int, TableRes>(NameToTableMap.Count);
            foreach (var table in NameToTableMap.Values)
            {
                keyToTableMap.Add(table.TableKey, table);
            }
            // 收集所有资源
            foreach (var table in NameToTableMap.Values)
            {
                if (table.KeyToAssetMap == null)
                    continue;

                int tableKey = table.TableKey;
                foreach (var (assetKey, assetPath) in table.KeyToAssetMap)
                {
                    if (!KeyToAssetMap.TryGetValue(assetKey, out var asset))
                    {
                        asset = new TableAssetRef()
                        {
                            Path = assetPath,
                            Key = assetKey,
                            Tables = new HashSet<int>() { tableKey },
                            Depends = new HashSet<int>()
                        };
                        KeyToAssetMap.Add(assetKey, asset);
                    }
                    else
                    {
                        if (asset.Path != assetPath)
                        {
                            var otherTables = asset.Tables.Select(k =>
                            {
                                var t = keyToTableMap[k];
                                var s = $"{string.Join('/', t.Table.InputFiles)} {t.TableName}";
                                return s;
                            });
                            var otherTableString = string.Join('|', otherTables);
                            throw new Exception($"{string.Join('/', table.Table.InputFiles)} {table.TableName}和{otherTableString}之间\n，请检查资源路径{asset.Path}和{assetPath}大小写是否不同");
                        }
                        asset.Tables.Add(tableKey);
                    }
                }
            }

            // 查找依赖资源
            TableKeyMap tableKeyMap = new TableKeyMap();
            tableKeyMap.NameToTableMap = NameToTableMap;

            List<FieldKeyMap> depValues = new List<FieldKeyMap>();
            foreach (var tableRes in NameToTableMap.Values)
            {
                var depToDataMap = tableRes.DepToDataMap;
                if (depToDataMap == null)
                    continue; // 该表没有依赖资源

                var table = tableRes.Table;
                foreach (var (depTagValue, depDataList) in depToDataMap)
                {
                    var depKeyMap = tableKeyMap.GetOrCreateMultKeyMap(table, depDataList[0]);

                    foreach (var depData in depDataList)
                    {
                        if (!depKeyMap.TryGetValue(depData.Value, depData.Field.CType, depValues))
                        {
                            if (depData.IsDefaultValue())
                                break; // 默认值不报异常，这是因为如果用户没有填写，就是默认值

                            throw new Exception($"{string.Join('/', table.InputFiles)} {table.Name}.{depData.Field.Name}[{depData.Value}] res_ref={depTagValue}, 找不到依赖键值，是否格式不对");
                        }

                        foreach (var depVal in depValues)
                        {
                            AddDepend(tableRes, depVal.TableRes, depVal.KeyMap, depData);
                        }
                    }
                }
            }

            return KeyToAssetMap;
        }

        private void AddDepend(TableRes tableRes, TableRes dependTableRes, DKeyMap dep, TableGridData keyData)
        {
            // 获取依赖数据行的所有资源键值
            if (dependTableRes.BeanToAssetsMap == null || !dependTableRes.BeanToAssetsMap.TryGetValue(dep.Host, out var depAssetKeys))
                return;
            //throw new Exception($"{string.Join('/', table.Table.InputFiles)} {table.TableName}.{depend.Field.Name}[{depend.Value}] res_ref={depTagValue}, can not find ref asset path");

            // 获取行的所有资源键值
            if (tableRes.BeanToAssetsMap == null || !tableRes.BeanToAssetsMap.TryGetValue(keyData.Host, out var assetKeys))
                return;

            foreach (var assetKey in assetKeys)
            {
                if (!KeyToAssetMap.TryGetValue(assetKey, out var asset))
                    throw new Exception($"{string.Join('/', tableRes.Table.InputFiles)} {tableRes.TableName}.{keyData.Field.Name}[{keyData.Value}] res_ref={keyData.TagValue}, 遗漏了依赖资源:{assetKey}");

                //if (asset.Path == "Weapon/Gun/Bow/Bow/Animator/Fp_ac_bow_fp/Ac_bow_Fp.overrideController")
                //{
                //    Console.WriteLine($"{asset.Path}:dep asset count:{depAssetKeys.Count}");
                //    foreach (var depAssetKey in depAssetKeys)
                //    {
                //        // 添加资源依赖
                //        KeyToAssetMap.TryGetValue(depAssetKey, out var dasset);
                //        Console.WriteLine($"    tableRes:{tableRes.TableName}.{keyData.Field.Name}[{keyData.Value}] depTableRes:{dependTableRes.TableName}.{dep.Field.Name} {dasset.Path}");
                //    }
                //}
                foreach (var depAssetKey in depAssetKeys)
                {
                    // 添加资源依赖
                    asset.Depends.Add(depAssetKey);
                }
            }
        }

        private static void WriteJson(string filePath, List<TableRes> tables, List<TableAssetRef> assets)
        {
            // 对导出表和资源进行排序，方便差异对比
            List<TableRes> sortedTables = tables;
            sortedTables.Sort((x, y) => x.TableName.CompareTo(y.TableName));
            List<TableAssetRef> sortedAssets = assets;
            sortedAssets.Sort((x, y) => x.Path.CompareTo(y.Path));
            HashSet<int> validTables = new HashSet<int>(128);
            List<int> sortKeys = new List<int>(32);
            foreach (var asset in sortedAssets)
            {
                sortKeys.Clear();
                sortKeys.AddRange(asset.Tables);
                sortKeys.Sort();
                asset.Tables = sortKeys.ToHashSet();

                if (asset.Depends != null)
                {
                    sortKeys.Clear();
                    sortKeys.AddRange(asset.Depends);
                    sortKeys.Sort();
                    asset.Depends = sortKeys.ToHashSet();
                }
                validTables.UnionWith(asset.Tables);
            }

            // 收集数据
            var tableResRefs = new TableAssetRefs();
            tableResRefs.Assets = sortedAssets;
            tableResRefs.Tables = new List<TableTag>(sortedTables.Count);
            foreach (var table in sortedTables)
            {
                if (!validTables.Contains(table.TableKey))
                    continue; // 没有资源关联的表

                tableResRefs.Tables.Add(new TableTag()
                {
                    Key = table.TableKey,
                    Name = table.TableName,
                    Tags = table.Tags,
                });
            }
#if false
            var items = JsonConvert.SerializeObject(tableResRefMap, Newtonsoft.Json.Formatting.Indented); 
            File.WriteAllText(filePath, items);
#else
            using var ss = new FileStream(filePath, FileMode.Create);
            var jsonWriter = new Utf8JsonWriter(ss, new JsonWriterOptions()
            {
                Indented = true,
                SkipValidation = false,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
            });
            jsonWriter.WriteStartObject();

            // 表格
            jsonWriter.WritePropertyName("Tables");
            jsonWriter.WriteStartArray();
            foreach (var table in tableResRefs.Tables)
            {
                jsonWriter.WriteStartObject();

                // 表格键值和名称
                jsonWriter.WritePropertyName("Key");
                jsonWriter.WriteNumberValue(table.Key);
                jsonWriter.WritePropertyName("Name");
                jsonWriter.WriteStringValue(table.Name);

                // 表格的打包Tag
                jsonWriter.WritePropertyName("Tags");
                jsonWriter.WriteStartArray();
                if (table.Tags != null)
                {
                    foreach (var tag in table.Tags)
                        jsonWriter.WriteStringValue(tag);
                }
                jsonWriter.WriteEndArray();

                jsonWriter.WriteEndObject();
            }
            jsonWriter.WriteEndArray();

            // 表格资源
            jsonWriter.WritePropertyName("Assets");
            jsonWriter.WriteStartArray();
            foreach (var asset in tableResRefs.Assets)
            {
                jsonWriter.WriteStartObject();

                jsonWriter.WritePropertyName("Key");
                jsonWriter.WriteNumberValue(asset.Key);

                jsonWriter.WritePropertyName("Path");
                jsonWriter.WriteStringValue(asset.Path);

                jsonWriter.WritePropertyName("Tables");
                jsonWriter.WriteStartArray();
                foreach (var table in asset.Tables)
                    jsonWriter.WriteNumberValue(table);
                jsonWriter.WriteEndArray();

                if (asset.Depends != null && asset.Depends.Count > 0)
                {
                    jsonWriter.WritePropertyName("Depends");
                    jsonWriter.WriteStartArray();
                    foreach (var depend in asset.Depends)
                        jsonWriter.WriteNumberValue(depend);
                    jsonWriter.WriteEndArray();
                }

                jsonWriter.WriteEndObject();
            }
            jsonWriter.WriteEndArray();

            jsonWriter.WriteEndObject();
            jsonWriter.Flush();
            ss.Flush();
#endif
        }

        public static void GetTableAssetRes(DefAssembly ass)
        {
            // 查找每个表格中资源，以及资源引用的其他表
            List<DefTable> exportTables = ass.GetExportTables();

            ConcurrentDictionary<string, DSocString> resMap = ass.SocResCheckDict;
            Dictionary<string, DefTable> inputFileToTableMap = new Dictionary<string, DefTable>();
            Dictionary<int, TableAssetRef> keyToAssetMap = new Dictionary<int, TableAssetRef>(8192);
            for (int i = 0; i < exportTables.Count; ++i)
            {
                var t = exportTables[i];
                List<string> inputFiles = t.InputFiles;

                foreach (var inputFile in inputFiles)
                {
                    if (!inputFileToTableMap.TryAdd(inputFile, t))
                    {
                        throw new Exception($"相同的输入文件：{inputFile}，不能分别关联表格：{t.Name}和表格：{inputFileToTableMap[inputFile].Name}");
                    }
                }
            }

            Dictionary<string, TableRes> inputFileToTableResMap = new Dictionary<string, TableRes>(inputFileToTableMap.Count);
            foreach (var (inputFile, table) in inputFileToTableMap)
            {
                DefBeanBase defBean = table.ValueTType.Bean;
                TableRes tableRes = new TableRes();
                tableRes.TableName = table.Name;
                tableRes.TableKey = Convert.GetStableHash(tableRes.TableName);
                tableRes.Table = table;
                tableRes.Bean = defBean;

                var indexField = table.IndexField;
                if (indexField?.Tags != null && indexField.Tags.TryGetValue(TagDefines.BUNDLE, out var tagBundleName))
                {
                    // 表Tags，以'.'分割
                    tableRes.Tags = tagBundleName.Split('.', StringSplitOptions.RemoveEmptyEntries);
                }

                inputFileToTableResMap.Add(inputFile, tableRes);
            }

            foreach (var res in resMap.Values)
            {
                if (!inputFileToTableResMap.TryGetValue(res.FileName, out var tableRes))
                    throw new Exception($"输入文件：{res.FileName}，找不到对应的表格");

                var ap = res.Value.Trim().Replace('\\', '/');
                var ak = Convert.GetStableHash(ap.ToLower());

                if (keyToAssetMap.TryGetValue(ak, out var asset))
                {
                    asset.Tables.Add(tableRes.TableKey);
                }
                else
                {
                    asset = new TableAssetRef()
                    {
                        Path = ap,
                        Key = ak,
                        Tables = new HashSet<int>() { tableRes.TableKey },
                        Depends = new HashSet<int>()
                    };
                    keyToAssetMap.Add(ak, asset);
                }
            }
        }
    }
}
