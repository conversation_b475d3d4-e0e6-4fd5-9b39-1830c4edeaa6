using Google.Protobuf.Collections;
using Luban.Job.Cfg.Datas;
using Luban.Job.Cfg.Defs;
using Luban.Job.Common.Defs;
using Luban.Job.Common.RawDefs;
using Luban.Job.Common.Source.Types;
using Luban.Job.Common.Types;
using Luban.Job.Common.TypeVisitors;
using System;
using System.Collections.Generic;

namespace Luban.Job.Cfg.Tags
{
    public class FindFieldByNameVisitor : DefineTypeVisitor
    {
        public int FieldLevelOffset;
        public string[] FieldNames;
        public List<TableGridField> Result;

        public static bool Find(DefBeanBase type, string[] fieldNames, int fieldOffset, out List<TableGridField> result)
        {
            FindFieldByNameVisitor visitor = new FindFieldByNameVisitor();
            bool found = visitor.Find(type, fieldNames, fieldOffset);
            result = found ? visitor.Result : null;
            return found;
        }

        public bool Find(DefBeanBase type, string[] fieldNames, int fieldOffset)
        {
            FieldLevelOffset = fieldOffset;
            FieldNames = fieldNames;
            int numLevels = FieldNames.Length - fieldOffset;
            if (Result == null)
                Result = new List<TableGridField>(numLevels);
            else
                Result.Clear();

            for (int i = 0; i < numLevels; i++)
                Result.Add(new TableGridField());

            TableGridID id = new TableGridID() { Level = 0, FieldIndex = 0 };
            Accept(null, type, id);

            if (Result.Exists(i => i.Field == null))
                return false;

            Result.Sort((x, y) => x.Id.Level.CompareTo(y.Id.Level));
            return true;
        }

        public override void Accept(DefFieldBase hostField, DefFieldBase field, TableGridID id)
        {
            if (FieldNames.Length > id.Level + FieldLevelOffset && FieldNames[id.Level + FieldLevelOffset] == field.Name)
            {
                Result[id.Level] = new TableGridField()
                {
                    HostField = hostField,
                    Field = field,
                    Id = id
                };
            }
            //Console.WriteLine($"FindDefineFieldByNameVisitor.Accept level:{level} field:{fieldIndex} Name:{field.Name}");
            base.Accept(hostField, field, id);
        }
    }
}
