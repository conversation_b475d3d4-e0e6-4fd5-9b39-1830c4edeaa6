using Luban.Job.Cfg.Datas;
using Luban.Job.Cfg.Defs;
using Luban.Job.Cfg.RawDefs;
using Luban.Job.Common.Defs;
using Luban.Job.Common.RawDefs;
using Luban.Job.Common.Source.Types;
using Luban.Job.Common.Types;
using Luban.Job.Common.TypeVisitors;
using System;
using System.Collections.Generic;

namespace Luban.Job.Cfg.Tags
{
    public class FindFieldByTagVisitor : DefineTypeVisitor
    {
        public string TagKey;
        public List<TableGridField> Result;

        public static List<TableGridField> GetKeyFields(DefTable table, string tagKey)
        {
            if (table.Mode == ETableMode.ONE)
                return null;

            FindFieldByTagVisitor fieldVisitor = new FindFieldByTagVisitor();
            if (!fieldVisitor.Find(table.ValueTType.Bean, tagKey))
            {
                table.IndexField.Tags.TryGetValue(tagKey, out var tagValue);
                var keyField = new TableGridField()
                {
                    Field = table.IndexField,
                    TagValue = tagValue,
                    Id = new TableGridID() { Level = 0, FieldIndex = table.IndexFieldIdIndex }
                };

                var keyFields = new List<TableGridField>(1) { keyField };
                return keyFields;
            }

            return fieldVisitor.Result;
        }

        public bool Find(DefBeanBase type, string tagKey)
        {
            TagKey = tagKey;
            Result = null;

            TableGridID id = new TableGridID() { Level = 0, FieldIndex = 0 };
            Accept(null, type, id);
            if (Result != null)
                Result.Sort((x, y) => x.Id.Level.CompareTo(y.Id.Level));

            return Result != null;
        }

        public override void Accept(DefFieldBase hostField, DefFieldBase field, TableGridID id)
        {
            if (field.Tags.TryGetValue(TagKey, out var tagValue))
            {
                // ����������Դ�����ƺ������ƣ��������봦��
                Result ??= new List<TableGridField>();
                Result.Add(new TableGridField()
                {
                    HostField = hostField,
                    Field = field,
                    TagValue = tagValue,
                    Id = id
                });
            }
            //Console.WriteLine($"FindDefineFieldByTagVisitor.Accept level:{level} field:{fieldIndex} Name:{field.Name}");
            base.Accept(hostField, field, id);
        }
    }

}
