using Luban.Job.Common.Utils;
using System.Collections.Generic;

namespace Luban.Job.Cfg.Tags
{
    public class TagDefines
    {
        public const string NO_DELETE = "no_delete";
        public const string REF_CHECK = "ref_check";
        public const string BUNDLE = "bundle";
        public const string RES_REF = "res_ref";
        public const string GROUP = "group";
        public const string KEY = "key";

        public string RefCheck = string.Empty;
        public string Bundle = string.Empty;
        public string ResRef = string.Empty;

        public bool NoDelete = false;
        public bool IsClient = true;
        public bool IsServer = true;

        public Dictionary<string, string> Tags;

        public void Init(string tags)
        {
            this.Init(DefUtil.ParseAttrs(tags));
        }

        public void Init(Dictionary<string, string> tags)
        {
            if (tags == null)
                return;

            string value;

            Tags = tags;
            NoDelete = tags.ContainsKey(NO_DELETE);
            RefCheck = tags.TryGetValue(REF_CHECK, out value) ? value : string.Empty;
            Bundle = tags.TryGetValue(BUNDLE, out value) ? value : string.Empty;
            ResRef = tags.TryGetValue(RES_REF, out value) ? value : string.Empty;
            IsClient = IsServer = true;
            if (tags.TryGetValue(RES_REF, out value) && !string.IsNullOrEmpty(value))
            {
                IsClient = value.Contains('c');
                IsServer = value.Contains('s');
            }
        }

    }
}
