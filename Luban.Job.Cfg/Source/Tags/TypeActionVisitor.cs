using Luban.Job.Cfg.Datas;
using Luban.Job.Common.Defs;
using Luban.Job.Common.Source.Types;
using Luban.Job.Common.Types;
using Luban.Job.Common.TypeVisitors;
using System.Collections.Generic;

namespace Luban.Job.Cfg.Tags
{
    public class TypeActionVisitor : ITypeActionVisitor<string>
    {
        public virtual void Accept(TBool type, string x)
        {

        }

        public virtual void Accept(TByte type, string x)
        {

        }

        public virtual void Accept(TShort type, string x)
        {

        }

        public virtual void Accept(TFshort type, string x)
        {

        }

        public virtual void Accept(TInt type, string x)
        {

        }

        public virtual void Accept(TFint type, string x)
        {

        }

        public virtual void Accept(TLong type, string x)
        {

        }

        public virtual void Accept(TFlong type, string x)
        {

        }

        public virtual void Accept(TFloat type, string x)
        {

        }

        public virtual void Accept(TDouble type, string x)
        {

        }

        public virtual void Accept(TEnum type, string x)
        {

        }

        public virtual void Accept(TString type, string x)
        {

        }

        public virtual void Accept(TBytes type, string x)
        {

        }

        public virtual void Accept(TText type, string x)
        {

        }

        public virtual void Accept(TBean type, string x)
        {

        }

        public virtual void Accept(TArray type, string x)
        {
            type.ElementType.Apply(this, x);
        }

        public virtual void Accept(TList type, string x)
        {
            type.ElementType.Apply(this, x);

        }

        public virtual void Accept(TSet type, string x)
        {
            type.ElementType.Apply(this, x);
        }

        public virtual void Accept(TMap type, string x)
        {
            type.KeyType.Apply(this, x);
            type.ValueType.Apply(this, x);
        }

        public virtual void Accept(TVector2 type, string x)
        {

        }

        public virtual void Accept(TVector3 type, string x)
        {

        }

        public virtual void Accept(TVector4 type, string x)
        {

        }

        public virtual void Accept(TDateTime type, string x)
        {

        }
    }

}
