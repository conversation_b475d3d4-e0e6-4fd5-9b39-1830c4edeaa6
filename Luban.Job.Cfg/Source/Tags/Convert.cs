using Luban.Job.Cfg.Datas;
using Luban.Job.Cfg.DataSources;
using System;
using System.Collections.Generic;
using System.Text;
using System.Security.Cryptography;
using DocumentFormat.OpenXml.Vml;
using System.Runtime.InteropServices;
using DocumentFormat.OpenXml.Office2010.PowerPoint;

namespace Luban.Job.Cfg.Tags
{
    public static class Convert
    {
        const uint FNV_OFFSET_BASIS = 2166136261U;
        const uint FNV_PRIME = 16777619U;
        //const uint FNV_PRIME = 0x811C9DC5;

        [StructLayout(LayoutKind.Explicit, Size = 4)]
        struct ConvertInt
        {
            [FieldOffset(0)]
            public int i;

            [FieldOffset(0)]
            public uint ui;
        }

        public static int GetStableHash(string text)
        {
            //var sha256 = SHA256.Create();
            //byte[] hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(text));
            //return BitConverter.ToInt32(hashBytes, 0);
            uint val = FNV_OFFSET_BASIS;
            for (int i = 0; i < text.Length; ++i)
            {
                val ^= (uint)text[i];
                val *= FNV_PRIME;
            }

            ConvertInt convert = new ConvertInt();
            convert.ui = val;
            return convert.i;
        }
    }
}
