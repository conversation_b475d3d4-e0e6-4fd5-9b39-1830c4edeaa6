using Luban.Job.Cfg.DataSources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Luban.Job.Cfg.Tags
{
    public struct TableTag
    {
        // 表的键值
        public int Key;
        // 表名称
        public string Name;
        // 表的打包标签
        public string[] Tags;
    }

    public class TableAssetRef
    {
        // 资源的键值
        public int Key;
        // 资源路径
        public string Path;
        // 资源关联表的索引
        public HashSet<int> Tables;
        // 引用其他表的资源
        public HashSet<int> Depends;
    }

    public class TableAssetRefs
    {
        // 引用其他表的资源
        public List<TableTag> Tables;
        // 表的资源
        public List<TableAssetRef> Assets;
    }

}
