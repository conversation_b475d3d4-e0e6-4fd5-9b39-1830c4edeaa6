using Luban.Job.Cfg.DataCreators;
using Luban.Job.Cfg.Datas;
using Luban.Job.Cfg.Defs;
using Luban.Job.Common.Defs;
using Luban.Job.Common.Types;
using System;
using System.Collections.Generic;

namespace Luban.Job.Cfg.Tags
{
    public struct TableGridID
    {
        public int Level;
        public int FieldIndex;
        //public int Row;
        public override string ToString()
        {
            return $"Level:{Level} FieldIndex:{FieldIndex}";
        }
    }

    public struct TableGridField
    {
        public DefFieldBase HostField;
        public DefFieldBase Field;
        public string TagValue;
        public TableGridID Id;

        public override string ToString()
        {
            return Field.Name;
        }
    }

    public struct TableGridData
    {
        public DBean Host;
        public DType Value;
        public DefFieldBase Field;
        public string TagValue;
        public TableGridID Id;

        public TableGridField AsGridField()
        {
            return new TableGridField()
            {
                Field = Field,
                TagValue = TagValue,
                Id = Id,
            };
        }

        public override string ToString()
        {
            return $"{Field.Name}[{Value.ToString()}] {Id.ToString()}";
        }

        public bool IsDefaultValue()
        {
            return Value.Apply(DataVisitors.IsDefaultValue.Ins);
        }
    }

    public class DKeyMap
    {
        public DBean Host;
        public DefFieldBase Field;
        public Dictionary<DType, DKeyMap> KeyMap;

        public Dictionary<DType, DKeyMap> GetOrCreateKeyMap()
        {
            KeyMap ??= new Dictionary<DType, DKeyMap>();
            return KeyMap;
        }

        public DKeyMap GetValue(List<DType> keys, int level = 0)
        {
            if (KeyMap != null && KeyMap.TryGetValue(keys[level], out var km))
            {
                ++level;
                return keys.Count == level ? km : km.GetValue(keys, level);
            }

            return null;
        }
    }
}
