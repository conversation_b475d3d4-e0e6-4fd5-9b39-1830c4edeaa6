using Luban.Job.Cfg.Datas;
using Luban.Job.Cfg.DataSources;
using Luban.Job.Cfg.DataVisitors;
using Luban.Job.Cfg.Defs;
using Luban.Job.Cfg.Utils;
using Luban.Job.Common.Defs;
using Luban.Job.Common.Types;
using System.Collections.Generic;


namespace Luban.Job.Cfg.Tags
{
    public class DataFieldVisitor : IDataFuncVisitor<DBean, TableGridID, bool>
    {
        public void Visit(List<Record> records)
        {
            TableGridID id = new TableGridID() { Level = 0, FieldIndex = 0 };

            for (int i = 0; i < records.Count; ++i)
            {
                if (!records[i].Data.Apply(this, null, id))
                {
                    break;
                }
            }
        }

        public virtual bool AcceptList(List<DType> datas, DBean host, TableGridID id)
        {
            bool done = false;
            ++id.Level;

            for (int i = 0; i < datas.Count; ++i)
            {
                if (!datas[i].Apply(this, host, id))
                {
                    done = true;
                    break;
                }
            }
            return !done;
        }

        public virtual bool Accept(DArray data, DBean host, TableGridID id)
        {
            return AcceptList(data.Datas, host, id);
        }

        public virtual bool Accept(DList data, DBean host, TableGridID id)
        {
            return AcceptList(data.Datas, host, id);
        }

        public virtual bool Accept(DSet data, DBean host, TableGridID id)
        {
            return AcceptList(data.Datas, host, id);
        }

        public virtual bool Accept(DMap data, DBean host, TableGridID id)
        {
            bool done = false;
            ++id.Level;
            foreach (var (key, value) in data.Datas)
            {
                if (!key.Apply(this, host, id) || !value.Apply(this, host, id))
                {
                    done = true;
                    break;
                }
            }

            return !done;
        }

        public virtual bool Accept(DBean data, DBean host, TableGridID id)
        {
            bool done = false;
            var dataFields = data.Fields;
            var typeFields = data.ImplType.HierarchyFields;
            for (int i = 0; i < dataFields.Count; ++i)
            {
                var dataField = dataFields[i];
                var typeField = typeFields[i];

                // 特殊处理 bean 多态类型
                if (dataField == null || dataField == DNull.Default)
                    continue;

                id.FieldIndex = i;
                if (!dataField.Apply(this, data, id))
                {
                    done = true;
                    break;
                }
            }

            return !done;
        }

        public virtual bool Accept(DVector2 data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DVector3 data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DVector4 data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DDateTime data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DBool data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DByte data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DShort data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DFshort data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DInt data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DFint data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DLong data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DFlong data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DFloat data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DDouble data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DEnum data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DString data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DBytes data, DBean host, TableGridID id)
        {
            return true;
        }

        public virtual bool Accept(DText data, DBean host, TableGridID id)
        {
            return true;
        }
    }
}
