using Luban.Job.Cfg.DataCreators;
using Luban.Job.Cfg.Datas;
using Luban.Job.Cfg.DataSources;
using Luban.Job.Cfg.DataVisitors;
using Luban.Job.Cfg.Defs;
using Luban.Job.Cfg.Utils;
using Luban.Job.Common.Defs;
using Luban.Job.Common.Types;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace Luban.Job.Cfg.Tags
{
    class FindDataByKeyVisitor : DataFieldVisitor
    {
        public int CurrentLevel;
        public List<TableGridField> FieldLevels;
        public List<DType> Keys;
        public List<TableGridData> Result;

        public bool Find(DefTable table, List<TableGridField> fieldLevels, DType key)
        {
            var records = table.Assembly.GetTableAllDataList(table);
            if (records.Count == 0)
                return false;

            CurrentLevel = 0;
            FieldLevels = fieldLevels;
            if (Result == null)
                Result = new List<TableGridData>(fieldLevels.Count);
            else
                Result.Clear();
            if (Keys == null)
                Keys = new List<DType>(fieldLevels.Count);
            else
                Keys.Clear();

            if (FieldLevels.Count == 1)
                Keys.Add(key);
            else if (key is DArray keyArr)
                Keys.AddRange(keyArr.Datas);
            else if (key is DList keyList)
                Keys.AddRange(keyList.Datas);
            else if (key is DSet keySet)
                Keys.AddRange(keySet.Datas);
            else if (key is DString keyString)
            {
                string[] keys = keyString.Value.Split('.', StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < keys.Length; ++i)
                {
                    var keyType = fieldLevels[i].Field.CType;
                    var keyData = keyType.Apply(StringDataCreator.Ins, keys[i]);
                    Keys.Add(keyData);
                }
            }

            if (Keys.Count != fieldLevels.Count)
                throw new Exception($"{string.Join('/', table.InputFiles)} {table.Name} ����Ĳ��Ҽ�ֵ������:{key.TypeName} ֵ:{key}����'{string.Join('.', fieldLevels)}'����ƥ��");

            base.Visit(records);

            TableGridID id = new TableGridID() { Level = 0, FieldIndex = 0 };
            while (++CurrentLevel < FieldLevels.Count && Result.Count == CurrentLevel)
            {
                var fields = Result[CurrentLevel - 1].Host.Fields;
                for (int i = 0; i < fields.Count; ++i)
                {
                    id.Level = CurrentLevel - 1;
                    id.FieldIndex = i;
                    if (!fields[i].Apply(this, null, id))
                        break;
                }
            }
            return FieldLevels.Count == Result.Count;
        }

        public override bool Accept(DBean data, DBean host, TableGridID id)
        {
            if (CurrentLevel != id.Level)
                return true;

            var dataFields = data.Fields;
            var typeFields = data.ImplType.HierarchyFields;
            int fieldIndex = FieldLevels[CurrentLevel].Id.FieldIndex;
            if (dataFields.Count <= fieldIndex)
                return true;

            var dataField = dataFields[fieldIndex];
            var typeField = typeFields[fieldIndex];
            if (FieldLevels[CurrentLevel].Field != typeField)
                return true;
            if (dataField == null ||
                dataField == DNull.Default ||
                dataField.CompareTo(Keys[CurrentLevel]) != 0)
                return true;

            id.FieldIndex = fieldIndex;
            Result.Add(new TableGridData()
            {
                Host = data,
                Value = dataField,
                Field = typeField,
                Id = id
            });

            return false;
        }

    }

}
