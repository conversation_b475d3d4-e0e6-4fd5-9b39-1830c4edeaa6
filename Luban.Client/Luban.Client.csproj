<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <StartupObject>Luban.Client.Program</StartupObject>
    <PackageProjectUrl>https://github.com/focus-creative-games/luban</PackageProjectUrl>
    <EnforceCodeStyleInBuild>True</EnforceCodeStyleInBuild>
    <PackageReadmeFile>README.md</PackageReadmeFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Scripts\**" />
    <EmbeddedResource Remove="Scripts\**" />
    <None Remove="Scripts\**" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\..\README.md">
      <Pack>True</Pack>
      <PackagePath>\</PackagePath>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="YamlDotNet" Version="11.2.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Luban.Common\Luban.Common.csproj" />
  </ItemGroup>

</Project>
